#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试CogVideoX-Flash支持的duration值
"""

import asyncio
import aiohttp
import json
import base64
import os

# 智谱AI API配置
API_KEY = "ed7ffe9976bb4484ab16647564c0cb27.rI1BXVjDDDURMtJY"
BASE_URL = "https://open.bigmodel.cn/api/paas/v4"

def image_to_base64(image_path):
    """将图像转换为base64格式"""
    try:
        with open(image_path, 'rb') as f:
            image_data = f.read()
        
        # 获取文件扩展名
        ext = os.path.splitext(image_path)[1].lower()
        if ext == '.png':
            mime_type = 'image/png'
        elif ext in ['.jpg', '.jpeg']:
            mime_type = 'image/jpeg'
        else:
            mime_type = 'image/png'  # 默认
        
        base64_str = base64.b64encode(image_data).decode('utf-8')
        return f"data:{mime_type};base64,{base64_str}"
    except Exception as e:
        print(f"图像转换失败: {e}")
        return None

async def test_duration_values():
    """测试不同的duration值"""
    print("🎬 测试CogVideoX-Flash支持的duration值...")
    
    # 查找测试图像
    test_image_paths = [
        "D:\\AI_Video_Generator\\output\\视频生成测试\\images\\pollinations\\pollinations_1750834422274_0.png",
        "test_image.png",
        "sample.jpg"
    ]
    
    test_image = None
    for path in test_image_paths:
        if os.path.exists(path):
            test_image = path
            break
    
    if not test_image:
        print("❌ 未找到测试图像")
        return False
    
    print(f"📷 使用测试图像: {test_image}")
    
    # 转换图像为base64
    image_base64 = image_to_base64(test_image)
    if not image_base64:
        return False
    
    headers = {
        'Authorization': f'Bearer {API_KEY}',
        'Content-Type': 'application/json'
    }
    
    # 测试不同的duration值
    duration_tests = [
        1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 9.0, 10.0,
        1.5, 2.5, 3.5, 4.5, 5.5, 6.5, 7.5, 8.5, 9.5,
        7.512, 5.736  # 实际使用的值
    ]
    
    successful_durations = []
    failed_durations = []
    
    async with aiohttp.ClientSession() as session:
        for duration in duration_tests:
            print(f"\n📋 测试duration: {duration}")
            
            test_data = {
                "model": "cogvideox-flash",
                "prompt": "一朵花在微风中摇摆",
                "image_url": image_base64,
                "duration": duration,
                "fps": 30,  # 使用已知支持的FPS
                "size": "960x1280"
            }
            
            try:
                url = f"{BASE_URL}/videos/generations"
                
                async with session.post(url, headers=headers, json=test_data) as response:
                    print(f"   状态码: {response.status}")
                    
                    if response.status == 200:
                        result = await response.json()
                        print(f"   ✅ duration {duration} 支持")
                        successful_durations.append(duration)
                    else:
                        response_text = await response.text()
                        print(f"   ❌ duration {duration} 不支持: {response_text[:100]}")
                        failed_durations.append(duration)
                        
                        # 检查是否是duration错误
                        if "1214" in response_text and "duration" in response_text.lower():
                            print(f"   💡 确认duration {duration} 不被支持")
                        
            except Exception as e:
                print(f"   ❌ 测试异常: {e}")
                failed_durations.append(duration)
            
            await asyncio.sleep(1)
    
    print(f"\n📊 测试结果总结:")
    print(f"✅ 支持的duration值: {successful_durations}")
    print(f"❌ 不支持的duration值: {failed_durations}")
    
    return len(successful_durations) > 0

async def test_without_duration():
    """测试不传duration参数"""
    print("\n🔍 测试不传duration参数...")
    
    # 查找测试图像
    test_image_paths = [
        "D:\\AI_Video_Generator\\output\\视频生成测试\\images\\pollinations\\pollinations_1750834422274_0.png",
        "test_image.png",
        "sample.jpg"
    ]
    
    test_image = None
    for path in test_image_paths:
        if os.path.exists(path):
            test_image = path
            break
    
    if not test_image:
        print("❌ 未找到测试图像")
        return False
    
    # 转换图像为base64
    image_base64 = image_to_base64(test_image)
    if not image_base64:
        return False
    
    headers = {
        'Authorization': f'Bearer {API_KEY}',
        'Content-Type': 'application/json'
    }
    
    test_data = {
        "model": "cogvideox-flash",
        "prompt": "一朵花在微风中摇摆",
        "image_url": image_base64,
        "fps": 30,
        "size": "960x1280"
    }
    
    async with aiohttp.ClientSession() as session:
        try:
            url = f"{BASE_URL}/videos/generations"
            
            async with session.post(url, headers=headers, json=test_data) as response:
                print(f"状态码: {response.status}")
                
                if response.status == 200:
                    result = await response.json()
                    print(f"✅ 不传duration参数成功")
                    print(f"响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
                    return True
                else:
                    response_text = await response.text()
                    print(f"❌ 不传duration参数失败: {response_text}")
                    return False
                    
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            return False

async def main():
    """主测试函数"""
    print("=" * 80)
    print("🧪 CogVideoX-Flash duration值测试")
    print("=" * 80)
    
    # 测试不同duration值
    success1 = await test_duration_values()
    
    # 测试不传duration参数
    success2 = await test_without_duration()
    
    print("\n" + "=" * 80)
    if success1 or success2:
        print("✅ 找到了可用的duration配置")
    else:
        print("❌ 所有duration配置都失败了")
    print("=" * 80)

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n👋 测试已取消")
    except Exception as e:
        print(f"\n❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
