#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强的错误处理系统
统一全局错误处理，提供更好的用户体验和系统稳定性
"""

import os
import sys
import time
import json
import traceback
import threading
import functools
import asyncio
from typing import Dict, Optional, Any, Callable, List, Union, Type
from dataclasses import dataclass, asdict, field
from enum import Enum
from pathlib import Path
from datetime import datetime, timedelta

from PyQt5.QtCore import QObject, pyqtSignal, QTimer, QThread
from PyQt5.QtWidgets import QMessageBox, QWidget

from src.utils.logger import logger
from src.gui.notification_system import show_error, show_warning, show_info, show_success

class ErrorLevel(Enum):
    """错误级别"""
    DEBUG = "debug"
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"

class ErrorDomain(Enum):
    """错误域"""
    SYSTEM = "system"           # 系统级错误
    NETWORK = "network"         # 网络相关
    FILE_IO = "file_io"        # 文件操作
    API = "api"                # API调用
    UI = "ui"                  # 用户界面
    VIDEO = "video"            # 视频处理
    AUDIO = "audio"            # 音频处理
    IMAGE = "image"            # 图像处理
    AI_SERVICE = "ai_service"  # AI服务
    DATABASE = "database"      # 数据库
    VALIDATION = "validation"  # 数据验证
    PERMISSION = "permission"  # 权限相关
    MEMORY = "memory"          # 内存相关
    UNKNOWN = "unknown"        # 未知错误

@dataclass
class ErrorContext:
    """错误上下文信息"""
    module: str = ""
    function: str = ""
    line_number: int = 0
    user_action: str = ""
    system_state: Dict[str, Any] = field(default_factory=dict)
    request_id: str = ""
    session_id: str = ""

@dataclass
class ErrorRecord:
    """错误记录"""
    id: str
    timestamp: datetime
    level: ErrorLevel
    domain: ErrorDomain
    message: str
    exception_type: str
    exception_message: str
    traceback_str: str
    context: ErrorContext
    user_message: str
    solutions: List[str]
    retry_count: int = 0
    resolved: bool = False
    resolution_time: Optional[datetime] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'id': self.id,
            'timestamp': self.timestamp.isoformat(),
            'level': self.level.value,
            'domain': self.domain.value,
            'message': self.message,
            'exception_type': self.exception_type,
            'exception_message': self.exception_message,
            'traceback_str': self.traceback_str,
            'context': asdict(self.context),
            'user_message': self.user_message,
            'solutions': self.solutions,
            'retry_count': self.retry_count,
            'resolved': self.resolved,
            'resolution_time': self.resolution_time.isoformat() if self.resolution_time else None
        }

class ErrorClassifier:
    """错误分类器"""
    
    def __init__(self):
        # 异常类型到错误域的映射
        self.exception_domain_map = {
            FileNotFoundError: ErrorDomain.FILE_IO,
            PermissionError: ErrorDomain.PERMISSION,
            OSError: ErrorDomain.SYSTEM,
            IOError: ErrorDomain.FILE_IO,
            ConnectionError: ErrorDomain.NETWORK,
            TimeoutError: ErrorDomain.NETWORK,
            MemoryError: ErrorDomain.MEMORY,
            ValueError: ErrorDomain.VALIDATION,
            TypeError: ErrorDomain.VALIDATION,
            KeyError: ErrorDomain.VALIDATION,
            AttributeError: ErrorDomain.SYSTEM,
            ImportError: ErrorDomain.SYSTEM,
            ModuleNotFoundError: ErrorDomain.SYSTEM,
        }
        
        # 关键词到错误域的映射
        self.keyword_domain_map = {
            'network': ErrorDomain.NETWORK,
            'connection': ErrorDomain.NETWORK,
            'timeout': ErrorDomain.NETWORK,
            'api': ErrorDomain.API,
            'video': ErrorDomain.VIDEO,
            'audio': ErrorDomain.AUDIO,
            'image': ErrorDomain.IMAGE,
            'file': ErrorDomain.FILE_IO,
            'permission': ErrorDomain.PERMISSION,
            'memory': ErrorDomain.MEMORY,
            'ui': ErrorDomain.UI,
            'database': ErrorDomain.DATABASE,
        }
    
    def classify_exception(self, exception: Exception, context: ErrorContext = None) -> ErrorDomain:
        """分类异常"""
        # 首先根据异常类型分类
        exception_type = type(exception)
        if exception_type in self.exception_domain_map:
            return self.exception_domain_map[exception_type]
        
        # 根据异常消息中的关键词分类
        message = str(exception).lower()
        for keyword, domain in self.keyword_domain_map.items():
            if keyword in message:
                return domain
        
        # 根据上下文分类
        if context:
            module_name = context.module.lower()
            for keyword, domain in self.keyword_domain_map.items():
                if keyword in module_name:
                    return domain
        
        return ErrorDomain.UNKNOWN
    
    def determine_level(self, exception: Exception, domain: ErrorDomain) -> ErrorLevel:
        """确定错误级别"""
        # 严重错误类型
        critical_exceptions = (MemoryError, SystemExit, KeyboardInterrupt)
        if isinstance(exception, critical_exceptions):
            return ErrorLevel.CRITICAL
        
        # 根据错误域确定级别
        domain_level_map = {
            ErrorDomain.SYSTEM: ErrorLevel.ERROR,
            ErrorDomain.NETWORK: ErrorLevel.WARNING,
            ErrorDomain.FILE_IO: ErrorLevel.ERROR,
            ErrorDomain.API: ErrorLevel.WARNING,
            ErrorDomain.UI: ErrorLevel.WARNING,
            ErrorDomain.VIDEO: ErrorLevel.ERROR,
            ErrorDomain.AUDIO: ErrorLevel.ERROR,
            ErrorDomain.IMAGE: ErrorLevel.ERROR,
            ErrorDomain.AI_SERVICE: ErrorLevel.WARNING,
            ErrorDomain.DATABASE: ErrorLevel.ERROR,
            ErrorDomain.VALIDATION: ErrorLevel.WARNING,
            ErrorDomain.PERMISSION: ErrorLevel.ERROR,
            ErrorDomain.MEMORY: ErrorLevel.CRITICAL,
            ErrorDomain.UNKNOWN: ErrorLevel.WARNING,
        }
        
        return domain_level_map.get(domain, ErrorLevel.WARNING)

class SolutionProvider:
    """解决方案提供器"""
    
    def __init__(self):
        self.solutions_map = {
            ErrorDomain.NETWORK: [
                "检查网络连接是否正常",
                "尝试重新连接网络",
                "检查防火墙设置",
                "稍后重试操作"
            ],
            ErrorDomain.FILE_IO: [
                "检查文件路径是否正确",
                "确认文件是否存在",
                "检查文件权限",
                "确保磁盘空间充足"
            ],
            ErrorDomain.PERMISSION: [
                "以管理员身份运行程序",
                "检查文件/目录权限",
                "确认用户有足够的访问权限"
            ],
            ErrorDomain.MEMORY: [
                "关闭其他不必要的程序",
                "重启应用程序",
                "检查系统内存使用情况",
                "考虑升级系统内存"
            ],
            ErrorDomain.API: [
                "检查API密钥是否正确",
                "确认API服务是否可用",
                "检查API调用频率限制",
                "稍后重试请求"
            ],
            ErrorDomain.VIDEO: [
                "检查视频文件格式是否支持",
                "确认视频文件没有损坏",
                "尝试使用其他视频文件",
                "检查视频处理库是否正确安装"
            ],
            ErrorDomain.AUDIO: [
                "检查音频文件格式是否支持",
                "确认音频文件没有损坏",
                "检查音频设备是否正常",
                "尝试使用其他音频文件"
            ],
            ErrorDomain.IMAGE: [
                "检查图像文件格式是否支持",
                "确认图像文件没有损坏",
                "尝试使用其他图像文件",
                "检查图像处理库是否正确安装"
            ],
            ErrorDomain.AI_SERVICE: [
                "检查AI服务配置",
                "确认API密钥有效",
                "检查服务额度是否充足",
                "尝试切换到其他AI服务"
            ],
            ErrorDomain.VALIDATION: [
                "检查输入数据格式",
                "确认所有必填字段已填写",
                "验证数据类型是否正确",
                "参考帮助文档"
            ],
            ErrorDomain.UI: [
                "重启应用程序",
                "检查界面组件是否正确初始化",
                "尝试重置界面布局",
                "联系技术支持"
            ]
        }
    
    def get_solutions(self, domain: ErrorDomain, exception: Exception = None) -> List[str]:
        """获取解决方案"""
        solutions = self.solutions_map.get(domain, ["联系技术支持获取帮助"])
        
        # 根据具体异常添加特定解决方案
        if exception:
            if isinstance(exception, FileNotFoundError):
                solutions.insert(0, f"确认文件 '{exception.filename}' 是否存在")
            elif isinstance(exception, PermissionError):
                solutions.insert(0, "检查对该文件/目录的访问权限")
            elif isinstance(exception, ConnectionError):
                solutions.insert(0, "检查网络连接和服务器状态")
        
        return solutions

class UserMessageGenerator:
    """用户消息生成器"""
    
    def __init__(self):
        self.level_prefixes = {
            ErrorLevel.DEBUG: "调试",
            ErrorLevel.INFO: "信息",
            ErrorLevel.WARNING: "警告",
            ErrorLevel.ERROR: "错误",
            ErrorLevel.CRITICAL: "严重错误"
        }
        
        self.domain_descriptions = {
            ErrorDomain.SYSTEM: "系统",
            ErrorDomain.NETWORK: "网络连接",
            ErrorDomain.FILE_IO: "文件操作",
            ErrorDomain.API: "API调用",
            ErrorDomain.UI: "用户界面",
            ErrorDomain.VIDEO: "视频处理",
            ErrorDomain.AUDIO: "音频处理",
            ErrorDomain.IMAGE: "图像处理",
            ErrorDomain.AI_SERVICE: "AI服务",
            ErrorDomain.DATABASE: "数据库",
            ErrorDomain.VALIDATION: "数据验证",
            ErrorDomain.PERMISSION: "权限",
            ErrorDomain.MEMORY: "内存",
            ErrorDomain.UNKNOWN: "未知"
        }
    
    def generate_user_message(self, level: ErrorLevel, domain: ErrorDomain, 
                            exception: Exception, context: ErrorContext = None) -> str:
        """生成用户友好的错误消息"""
        level_prefix = self.level_prefixes.get(level, "错误")
        domain_desc = self.domain_descriptions.get(domain, "系统")
        
        # 基础消息
        if isinstance(exception, FileNotFoundError):
            base_msg = f"找不到文件：{exception.filename}"
        elif isinstance(exception, PermissionError):
            base_msg = "权限不足，无法执行操作"
        elif isinstance(exception, ConnectionError):
            base_msg = "网络连接失败"
        elif isinstance(exception, TimeoutError):
            base_msg = "操作超时"
        elif isinstance(exception, MemoryError):
            base_msg = "内存不足"
        elif isinstance(exception, ValueError):
            base_msg = "输入数据格式不正确"
        else:
            base_msg = str(exception)
        
        # 添加上下文信息
        if context and context.user_action:
            return f"{level_prefix}：在执行「{context.user_action}」时，{domain_desc}出现问题：{base_msg}"
        else:
            return f"{level_prefix}：{domain_desc}出现问题：{base_msg}"

class ErrorRecoveryManager:
    """错误恢复管理器"""

    def __init__(self):
        self.recovery_strategies = {
            ErrorDomain.NETWORK: self._recover_network_error,
            ErrorDomain.API: self._recover_api_error,
            ErrorDomain.FILE_IO: self._recover_file_error,
            ErrorDomain.AI_SERVICE: self._recover_ai_service_error,
        }

        self.retry_configs = {
            ErrorDomain.NETWORK: {'max_retries': 3, 'delay': 2.0, 'backoff': 2.0},
            ErrorDomain.API: {'max_retries': 2, 'delay': 1.0, 'backoff': 1.5},
            ErrorDomain.AI_SERVICE: {'max_retries': 2, 'delay': 3.0, 'backoff': 2.0},
        }

    def can_recover(self, domain: ErrorDomain, exception: Exception) -> bool:
        """判断是否可以自动恢复"""
        recoverable_exceptions = (
            ConnectionError, TimeoutError, OSError
        )

        if domain in self.recovery_strategies:
            return isinstance(exception, recoverable_exceptions)

        return False

    def attempt_recovery(self, domain: ErrorDomain, exception: Exception,
                        context: ErrorContext = None) -> bool:
        """尝试自动恢复"""
        if not self.can_recover(domain, exception):
            return False

        strategy = self.recovery_strategies.get(domain)
        if strategy:
            try:
                return strategy(exception, context)
            except Exception as e:
                logger.error(f"错误恢复策略执行失败: {e}")
                return False

        return False

    def _recover_network_error(self, exception: Exception, context: ErrorContext = None) -> bool:
        """恢复网络错误"""
        # 简单的网络重连尝试
        import time
        time.sleep(1)  # 等待1秒
        return True  # 假设恢复成功，实际应该进行网络测试

    def _recover_api_error(self, exception: Exception, context: ErrorContext = None) -> bool:
        """恢复API错误"""
        # API错误通常需要重试
        return True

    def _recover_file_error(self, exception: Exception, context: ErrorContext = None) -> bool:
        """恢复文件错误"""
        # 文件错误通常难以自动恢复
        return False

    def _recover_ai_service_error(self, exception: Exception, context: ErrorContext = None) -> bool:
        """恢复AI服务错误"""
        # AI服务错误可能需要切换服务提供商
        return True

class EnhancedErrorHandler(QObject):
    """增强的错误处理器"""

    # 信号
    error_occurred = pyqtSignal(ErrorRecord)
    error_resolved = pyqtSignal(str)  # error_id
    error_suppressed = pyqtSignal(str)  # error_signature

    def __init__(self):
        super().__init__()

        # 组件初始化
        self.classifier = ErrorClassifier()
        self.solution_provider = SolutionProvider()
        self.message_generator = UserMessageGenerator()
        self.recovery_manager = ErrorRecoveryManager()

        # 错误记录和统计
        self.error_records: Dict[str, ErrorRecord] = {}
        self.error_statistics: Dict[str, int] = {}
        self.suppressed_errors: set = set()

        # 配置
        self.max_records = 1000
        self.suppress_threshold = 5
        self.auto_recovery_enabled = True
        self.user_notification_enabled = True

        # 错误日志文件
        self.log_file = Path("logs/error_records.json")
        self.log_file.parent.mkdir(exist_ok=True)

        # 定期清理定时器
        self.cleanup_timer = QTimer()
        self.cleanup_timer.timeout.connect(self._cleanup_old_records)
        self.cleanup_timer.start(3600000)  # 每小时清理一次

    def handle_exception(self, exception: Exception, context: ErrorContext = None,
                        user_action: str = "", show_to_user: bool = True) -> ErrorRecord:
        """处理异常"""
        try:
            # 创建上下文
            if context is None:
                context = self._create_context_from_traceback(user_action)
            elif user_action:
                context.user_action = user_action

            # 分类错误
            domain = self.classifier.classify_exception(exception, context)
            level = self.classifier.determine_level(exception, domain)

            # 生成错误ID
            error_id = self._generate_error_id(exception, context)

            # 创建错误记录
            error_record = ErrorRecord(
                id=error_id,
                timestamp=datetime.now(),
                level=level,
                domain=domain,
                message=str(exception),
                exception_type=type(exception).__name__,
                exception_message=str(exception),
                traceback_str=traceback.format_exc(),
                context=context,
                user_message=self.message_generator.generate_user_message(level, domain, exception, context),
                solutions=self.solution_provider.get_solutions(domain, exception)
            )

            # 检查是否需要抑制
            error_signature = f"{domain.value}:{type(exception).__name__}"
            self.error_statistics[error_signature] = self.error_statistics.get(error_signature, 0) + 1

            if self.error_statistics[error_signature] > self.suppress_threshold:
                if error_signature not in self.suppressed_errors:
                    self.suppressed_errors.add(error_signature)
                    logger.warning(f"错误类型 {error_signature} 出现频率过高，开始抑制通知")
                    self.error_suppressed.emit(error_signature)
                show_to_user = False

            # 记录错误
            self._record_error(error_record)

            # 尝试自动恢复
            if self.auto_recovery_enabled and self.recovery_manager.can_recover(domain, exception):
                if self.recovery_manager.attempt_recovery(domain, exception, context):
                    error_record.resolved = True
                    error_record.resolution_time = datetime.now()
                    logger.info(f"错误 {error_id} 已自动恢复")
                    self.error_resolved.emit(error_id)

            # 显示给用户
            if show_to_user and self.user_notification_enabled and not error_record.resolved:
                self._show_error_to_user(error_record)

            # 发送信号
            self.error_occurred.emit(error_record)

            return error_record

        except Exception as handling_error:
            # 错误处理器自身出错
            logger.critical(f"错误处理器出现异常: {handling_error}")
            # 创建最小错误记录
            fallback_record = ErrorRecord(
                id=f"fallback_{int(time.time())}",
                timestamp=datetime.now(),
                level=ErrorLevel.CRITICAL,
                domain=ErrorDomain.SYSTEM,
                message=str(exception),
                exception_type=type(exception).__name__,
                exception_message=str(exception),
                traceback_str=traceback.format_exc(),
                context=ErrorContext(),
                user_message=f"系统错误：{str(exception)}",
                solutions=["重启应用程序", "联系技术支持"]
            )
            return fallback_record

    def _create_context_from_traceback(self, user_action: str = "") -> ErrorContext:
        """从调用栈创建上下文"""
        frame = sys._getframe(3)  # 跳过当前函数和调用链

        return ErrorContext(
            module=frame.f_globals.get('__name__', 'unknown'),
            function=frame.f_code.co_name,
            line_number=frame.f_lineno,
            user_action=user_action,
            system_state={
                'memory_usage': self._get_memory_usage(),
                'thread_count': threading.active_count(),
                'timestamp': datetime.now().isoformat()
            }
        )

    def _generate_error_id(self, exception: Exception, context: ErrorContext) -> str:
        """生成错误ID"""
        import hashlib

        content = f"{type(exception).__name__}:{str(exception)}:{context.module}:{context.function}"
        hash_obj = hashlib.md5(content.encode())
        return f"err_{hash_obj.hexdigest()[:8]}_{int(time.time())}"

    def _get_memory_usage(self) -> float:
        """获取内存使用情况"""
        try:
            import psutil
            process = psutil.Process()
            return process.memory_info().rss / 1024 / 1024  # MB
        except ImportError:
            return 0.0

    def _record_error(self, error_record: ErrorRecord):
        """记录错误"""
        self.error_records[error_record.id] = error_record

        # 限制记录数量
        if len(self.error_records) > self.max_records:
            # 删除最旧的记录
            oldest_id = min(self.error_records.keys(),
                          key=lambda x: self.error_records[x].timestamp)
            del self.error_records[oldest_id]

        # 记录到日志文件
        self._save_to_log_file(error_record)

        # 记录到系统日志
        log_message = f"[{error_record.domain.value}] {error_record.user_message}"
        if error_record.level == ErrorLevel.CRITICAL:
            logger.critical(log_message)
        elif error_record.level == ErrorLevel.ERROR:
            logger.error(log_message)
        elif error_record.level == ErrorLevel.WARNING:
            logger.warning(log_message)
        else:
            logger.info(log_message)

    def _show_error_to_user(self, error_record: ErrorRecord):
        """向用户显示错误"""
        try:
            if error_record.level == ErrorLevel.CRITICAL:
                show_error(f"{error_record.user_message}\n\n建议解决方案：\n" +
                          "\n".join(f"• {solution}" for solution in error_record.solutions[:3]))
            elif error_record.level == ErrorLevel.ERROR:
                show_error(error_record.user_message)
            elif error_record.level == ErrorLevel.WARNING:
                show_warning(error_record.user_message)
            else:
                show_info(error_record.user_message)
        except Exception as e:
            logger.error(f"显示错误消息失败: {e}")

    def _save_to_log_file(self, error_record: ErrorRecord):
        """保存错误记录到日志文件"""
        try:
            # 读取现有记录
            existing_records = []
            if self.log_file.exists():
                try:
                    with open(self.log_file, 'r', encoding='utf-8') as f:
                        existing_records = json.load(f)
                except (json.JSONDecodeError, IOError):
                    existing_records = []

            # 添加新记录
            existing_records.append(error_record.to_dict())

            # 限制文件大小（保留最新的1000条记录）
            if len(existing_records) > 1000:
                existing_records = existing_records[-1000:]

            # 保存到文件
            with open(self.log_file, 'w', encoding='utf-8') as f:
                json.dump(existing_records, f, ensure_ascii=False, indent=2)

        except Exception as e:
            logger.error(f"保存错误记录到文件失败: {e}")

    def _cleanup_old_records(self):
        """清理旧的错误记录"""
        try:
            cutoff_time = datetime.now() - timedelta(days=7)  # 保留7天内的记录

            # 清理内存中的记录
            old_ids = [
                error_id for error_id, record in self.error_records.items()
                if record.timestamp < cutoff_time
            ]

            for error_id in old_ids:
                del self.error_records[error_id]

            if old_ids:
                logger.info(f"清理了 {len(old_ids)} 条旧错误记录")

        except Exception as e:
            logger.error(f"清理旧错误记录失败: {e}")

    def get_error_statistics(self) -> Dict[str, Any]:
        """获取错误统计信息"""
        total_errors = len(self.error_records)
        resolved_errors = sum(1 for record in self.error_records.values() if record.resolved)

        # 按域统计
        domain_stats = {}
        for record in self.error_records.values():
            domain = record.domain.value
            if domain not in domain_stats:
                domain_stats[domain] = {'count': 0, 'resolved': 0}
            domain_stats[domain]['count'] += 1
            if record.resolved:
                domain_stats[domain]['resolved'] += 1

        # 按级别统计
        level_stats = {}
        for record in self.error_records.values():
            level = record.level.value
            level_stats[level] = level_stats.get(level, 0) + 1

        return {
            'total_errors': total_errors,
            'resolved_errors': resolved_errors,
            'resolution_rate': resolved_errors / total_errors if total_errors > 0 else 0,
            'domain_statistics': domain_stats,
            'level_statistics': level_stats,
            'suppressed_error_types': len(self.suppressed_errors),
            'active_error_types': len(self.error_statistics)
        }

    def get_recent_errors(self, hours: int = 24) -> List[ErrorRecord]:
        """获取最近的错误记录"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        return [
            record for record in self.error_records.values()
            if record.timestamp > cutoff_time
        ]

    def mark_error_resolved(self, error_id: str, resolution_note: str = ""):
        """标记错误已解决"""
        if error_id in self.error_records:
            self.error_records[error_id].resolved = True
            self.error_records[error_id].resolution_time = datetime.now()
            if resolution_note:
                self.error_records[error_id].solutions.append(f"解决方案: {resolution_note}")

            logger.info(f"错误 {error_id} 已标记为已解决")
            self.error_resolved.emit(error_id)

    def clear_suppressed_errors(self):
        """清除抑制的错误类型"""
        self.suppressed_errors.clear()
        self.error_statistics.clear()
        logger.info("已清除所有抑制的错误类型")

# 全局错误处理器实例
enhanced_error_handler = EnhancedErrorHandler()

# 装饰器函数
def handle_errors(domain: ErrorDomain = ErrorDomain.UNKNOWN,
                 user_action: str = "",
                 show_to_user: bool = True,
                 return_on_error: Any = None):
    """错误处理装饰器"""
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                context = ErrorContext(
                    module=func.__module__,
                    function=func.__name__,
                    user_action=user_action or f"执行函数 {func.__name__}"
                )

                enhanced_error_handler.handle_exception(
                    e, context, user_action, show_to_user
                )

                return return_on_error
        return wrapper
    return decorator

def handle_async_errors(domain: ErrorDomain = ErrorDomain.UNKNOWN,
                       user_action: str = "",
                       show_to_user: bool = True,
                       return_on_error: Any = None):
    """异步错误处理装饰器"""
    def decorator(func):
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            try:
                return await func(*args, **kwargs)
            except Exception as e:
                context = ErrorContext(
                    module=func.__module__,
                    function=func.__name__,
                    user_action=user_action or f"执行异步函数 {func.__name__}"
                )

                enhanced_error_handler.handle_exception(
                    e, context, user_action, show_to_user
                )

                return return_on_error
        return wrapper
    return decorator

# 便捷函数
def handle_exception(exception: Exception, context: ErrorContext = None,
                    user_action: str = "", show_to_user: bool = True) -> ErrorRecord:
    """处理异常的便捷函数"""
    return enhanced_error_handler.handle_exception(exception, context, user_action, show_to_user)

def get_error_stats() -> Dict[str, Any]:
    """获取错误统计信息的便捷函数"""
    return enhanced_error_handler.get_error_statistics()

def get_recent_errors(hours: int = 24) -> List[ErrorRecord]:
    """获取最近错误的便捷函数"""
    return enhanced_error_handler.get_recent_errors(hours)
