#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
视频合成处理器
专门负责将多个视频片段、配音、音效合成为最终视频
"""

import os
import asyncio
import json
import tempfile
from typing import Dict, List, Optional, Any, Callable, Union, Tuple
from dataclasses import dataclass, field
from pathlib import Path
from datetime import datetime

from src.utils.logger import logger
from src.utils.error_handler import handle_error, safe_execute

# 导入视频处理库
try:
    import moviepy.editor as mp
    from moviepy.editor import (
        VideoFileClip, AudioFileClip, ImageClip, CompositeVideoClip,
        CompositeAudioClip, concatenate_videoclips, concatenate_audioclips,
        ColorClip, TextClip
    )
    from moviepy.video.fx import resize, fadein, fadeout
    from moviepy.audio.fx import volumex

    # 尝试导入可能不存在的函数
    try:
        from moviepy.video.fx import crossfadein, crossfadeout
    except ImportError:
        # 如果不存在，创建简单的替代函数
        def crossfadein(clip, duration):
            return fadein(clip, duration)
        def crossfadeout(clip, duration):
            return fadeout(clip, duration)

    MOVIEPY_AVAILABLE = True
    logger.info("MoviePy视频处理库加载成功")
except ImportError as e:
    logger.warning(f"MoviePy不可用，视频合成功能将受限: {e}")
    MOVIEPY_AVAILABLE = False
    # 创建占位符以避免NameError
    mp = None
    VideoFileClip = AudioFileClip = ImageClip = CompositeVideoClip = None
    CompositeAudioClip = concatenate_videoclips = concatenate_audioclips = None
    ColorClip = TextClip = resize = fadein = fadeout = crossfadein = crossfadeout = volumex = None

@dataclass
class VideoSegment:
    """视频片段"""
    id: str
    file_path: str
    start_time: float
    duration: float
    volume: float = 1.0
    fade_in: float = 0.0
    fade_out: float = 0.0
    effects: List[Dict[str, Any]] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class AudioSegment:
    """音频片段"""
    id: str
    file_path: str
    start_time: float
    duration: float
    volume: float = 1.0
    fade_in: float = 0.0
    fade_out: float = 0.0
    track_type: str = "voice"  # voice, music, sfx
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class ImageSegment:
    """图像片段（用于静态图像转视频）"""
    id: str
    file_path: str
    start_time: float
    duration: float
    effects: List[Dict[str, Any]] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class TransitionEffect:
    """转场效果"""
    type: str  # fade, dissolve, slide, zoom, etc.
    duration: float
    parameters: Dict[str, Any] = field(default_factory=dict)

@dataclass
class SynthesisConfig:
    """视频合成配置"""
    output_width: int = 1920
    output_height: int = 1080
    fps: int = 24
    video_codec: str = "libx264"
    audio_codec: str = "aac"
    video_bitrate: str = "5000k"
    audio_bitrate: str = "128k"
    background_color: Tuple[int, int, int] = (0, 0, 0)
    enable_transitions: bool = True
    default_transition: TransitionEffect = field(default_factory=lambda: TransitionEffect("fade", 0.5))
    quality_preset: str = "medium"  # ultrafast, fast, medium, slow, veryslow

@dataclass
class SynthesisProject:
    """视频合成项目"""
    name: str
    video_segments: List[VideoSegment] = field(default_factory=list)
    audio_segments: List[AudioSegment] = field(default_factory=list)
    image_segments: List[ImageSegment] = field(default_factory=list)
    transitions: List[TransitionEffect] = field(default_factory=list)
    config: SynthesisConfig = field(default_factory=SynthesisConfig)
    total_duration: float = 0.0
    metadata: Dict[str, Any] = field(default_factory=dict)

class VideoSynthesisProcessor:
    """视频合成处理器"""
    
    def __init__(self, output_dir: str = "output/videos"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 临时文件目录
        self.temp_dir = self.output_dir / "temp"
        self.temp_dir.mkdir(exist_ok=True)
        
        # 支持的格式
        self.supported_video_formats = ['.mp4', '.avi', '.mov', '.mkv', '.webm']
        self.supported_audio_formats = ['.mp3', '.wav', '.aac', '.m4a', '.ogg']
        self.supported_image_formats = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']
        
        # 转场效果映射
        self.transition_handlers = {
            'fade': self._apply_fade_transition,
            'dissolve': self._apply_dissolve_transition,
            'slide': self._apply_slide_transition,
            'zoom': self._apply_zoom_transition,
            'cut': self._apply_cut_transition
        }
        
        logger.info(f"视频合成处理器初始化完成，输出目录: {self.output_dir}")
    
    def create_project(self, name: str, config: Optional[SynthesisConfig] = None) -> SynthesisProject:
        """创建新的合成项目"""
        if config is None:
            config = SynthesisConfig()
        
        project = SynthesisProject(
            name=name,
            config=config,
            metadata={
                "created_time": datetime.now().isoformat(),
                "version": "1.0"
            }
        )
        
        logger.info(f"创建视频合成项目: {name}")
        return project
    
    def add_video_segment(self, project: SynthesisProject, video_path: str, 
                         start_time: float, duration: float = None,
                         segment_id: str = None, **kwargs) -> str:
        """添加视频片段到项目"""
        if not os.path.exists(video_path):
            raise FileNotFoundError(f"视频文件不存在: {video_path}")
        
        if segment_id is None:
            segment_id = f"video_{len(project.video_segments) + 1}"
        
        # 如果未指定时长，尝试获取视频时长
        if duration is None and MOVIEPY_AVAILABLE:
            try:
                with VideoFileClip(video_path) as clip:
                    duration = clip.duration
            except Exception as e:
                logger.warning(f"无法获取视频时长，使用默认值: {e}")
                duration = 5.0
        elif duration is None:
            duration = 5.0
        
        segment = VideoSegment(
            id=segment_id,
            file_path=video_path,
            start_time=start_time,
            duration=duration,
            **kwargs
        )
        
        project.video_segments.append(segment)
        project.total_duration = max(project.total_duration, start_time + duration)
        
        logger.info(f"添加视频片段: {segment_id} ({duration:.2f}s)")
        return segment_id
    
    def add_audio_segment(self, project: SynthesisProject, audio_path: str,
                         start_time: float, duration: float = None,
                         segment_id: str = None, **kwargs) -> str:
        """添加音频片段到项目"""
        if not os.path.exists(audio_path):
            raise FileNotFoundError(f"音频文件不存在: {audio_path}")
        
        if segment_id is None:
            segment_id = f"audio_{len(project.audio_segments) + 1}"
        
        # 如果未指定时长，尝试获取音频时长
        if duration is None and MOVIEPY_AVAILABLE:
            try:
                with AudioFileClip(audio_path) as clip:
                    duration = clip.duration
            except Exception as e:
                logger.warning(f"无法获取音频时长，使用默认值: {e}")
                duration = 5.0
        elif duration is None:
            duration = 5.0
        
        segment = AudioSegment(
            id=segment_id,
            file_path=audio_path,
            start_time=start_time,
            duration=duration,
            **kwargs
        )
        
        project.audio_segments.append(segment)
        project.total_duration = max(project.total_duration, start_time + duration)
        
        logger.info(f"添加音频片段: {segment_id} ({duration:.2f}s)")
        return segment_id
    
    def add_image_segment(self, project: SynthesisProject, image_path: str,
                         start_time: float, duration: float,
                         segment_id: str = None, **kwargs) -> str:
        """添加图像片段到项目（图像转视频）"""
        if not os.path.exists(image_path):
            raise FileNotFoundError(f"图像文件不存在: {image_path}")
        
        if segment_id is None:
            segment_id = f"image_{len(project.image_segments) + 1}"
        
        segment = ImageSegment(
            id=segment_id,
            file_path=image_path,
            start_time=start_time,
            duration=duration,
            **kwargs
        )
        
        project.image_segments.append(segment)
        project.total_duration = max(project.total_duration, start_time + duration)
        
        logger.info(f"添加图像片段: {segment_id} ({duration:.2f}s)")
        return segment_id
    
    def add_transition(self, project: SynthesisProject, transition: TransitionEffect,
                      between_segments: Tuple[str, str] = None):
        """添加转场效果"""
        project.transitions.append(transition)
        logger.info(f"添加转场效果: {transition.type} ({transition.duration:.2f}s)")
    
    async def synthesize_video(self, project: SynthesisProject, 
                              output_filename: str = None,
                              progress_callback: Optional[Callable] = None) -> str:
        """合成最终视频"""
        if not MOVIEPY_AVAILABLE:
            raise RuntimeError("MoviePy不可用，无法进行视频合成")
        
        try:
            if output_filename is None:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                output_filename = f"{project.name}_{timestamp}.mp4"
            
            output_path = self.output_dir / output_filename
            
            logger.info(f"开始合成视频: {project.name}")
            logger.info(f"输出路径: {output_path}")
            
            if progress_callback:
                progress_callback(0.1, "准备视频片段...")
            
            # 处理视频片段
            video_clips = await self._process_video_segments(project, progress_callback)
            
            if progress_callback:
                progress_callback(0.4, "处理图像片段...")
            
            # 处理图像片段
            image_clips = await self._process_image_segments(project, progress_callback)
            
            if progress_callback:
                progress_callback(0.6, "处理音频片段...")
            
            # 处理音频片段
            audio_clips = await self._process_audio_segments(project, progress_callback)
            
            if progress_callback:
                progress_callback(0.8, "合成最终视频...")
            
            # 合成视频
            final_video = await self._compose_final_video(
                video_clips, image_clips, audio_clips, project, progress_callback
            )
            
            if progress_callback:
                progress_callback(0.9, "导出视频文件...")
            
            # 导出视频
            await self._export_video(final_video, output_path, project.config, progress_callback)
            
            if progress_callback:
                progress_callback(1.0, "视频合成完成!")
            
            logger.info(f"视频合成完成: {output_path}")
            return str(output_path)

        except Exception as e:
            error_msg = f"视频合成失败: {e}"
            logger.error(error_msg)
            handle_error(e, {
                'source': 'video_synthesis',
                'project_name': project.name,
                'output_path': str(output_path) if 'output_path' in locals() else 'unknown'
            })
            raise

    async def _process_video_segments(self, project: SynthesisProject,
                                    progress_callback: Optional[Callable] = None) -> List[Any]:
        """处理视频片段"""
        video_clips = []

        for i, segment in enumerate(project.video_segments):
            try:
                if progress_callback:
                    progress = 0.1 + (i / len(project.video_segments)) * 0.2
                    progress_callback(progress, f"处理视频片段 {i+1}/{len(project.video_segments)}")

                # 加载视频片段
                clip = VideoFileClip(segment.file_path)

                # 设置时长
                if segment.duration < clip.duration:
                    clip = clip.subclip(0, segment.duration)
                elif segment.duration > clip.duration:
                    # 如果需要的时长超过原视频，循环播放
                    loops_needed = int(segment.duration / clip.duration) + 1
                    clip = concatenate_videoclips([clip] * loops_needed).subclip(0, segment.duration)

                # 设置开始时间
                clip = clip.set_start(segment.start_time)

                # 应用音量调整
                if hasattr(clip, 'audio') and clip.audio and segment.volume != 1.0:
                    clip = clip.set_audio(clip.audio.fx(volumex, segment.volume))

                # 应用淡入淡出效果
                if segment.fade_in > 0:
                    clip = clip.fx(fadein, segment.fade_in)
                if segment.fade_out > 0:
                    clip = clip.fx(fadeout, segment.fade_out)

                # 应用其他效果
                clip = self._apply_video_effects(clip, segment.effects)

                video_clips.append(clip)
                logger.info(f"处理视频片段完成: {segment.id}")

            except Exception as e:
                logger.error(f"处理视频片段失败 {segment.id}: {e}")
                continue

        return video_clips

    async def _process_image_segments(self, project: SynthesisProject,
                                    progress_callback: Optional[Callable] = None) -> List[Any]:
        """处理图像片段"""
        image_clips = []

        for i, segment in enumerate(project.image_segments):
            try:
                if progress_callback:
                    progress = 0.4 + (i / len(project.image_segments)) * 0.15
                    progress_callback(progress, f"处理图像片段 {i+1}/{len(project.image_segments)}")

                # 加载图像
                clip = ImageClip(segment.file_path, duration=segment.duration)

                # 调整尺寸以适应输出分辨率
                clip = clip.resize((project.config.output_width, project.config.output_height))

                # 设置开始时间
                clip = clip.set_start(segment.start_time)

                # 应用效果
                clip = self._apply_image_effects(clip, segment.effects)

                image_clips.append(clip)
                logger.info(f"处理图像片段完成: {segment.id}")

            except Exception as e:
                logger.error(f"处理图像片段失败 {segment.id}: {e}")
                continue

        return image_clips

    async def _process_audio_segments(self, project: SynthesisProject,
                                    progress_callback: Optional[Callable] = None) -> List[Any]:
        """处理音频片段"""
        audio_clips = []

        for i, segment in enumerate(project.audio_segments):
            try:
                if progress_callback:
                    progress = 0.6 + (i / len(project.audio_segments)) * 0.15
                    progress_callback(progress, f"处理音频片段 {i+1}/{len(project.audio_segments)}")

                # 加载音频片段
                clip = AudioFileClip(segment.file_path)

                # 设置时长
                if segment.duration < clip.duration:
                    clip = clip.subclip(0, segment.duration)
                elif segment.duration > clip.duration:
                    # 如果需要的时长超过原音频，循环播放
                    loops_needed = int(segment.duration / clip.duration) + 1
                    clip = concatenate_audioclips([clip] * loops_needed).subclip(0, segment.duration)

                # 设置开始时间
                clip = clip.set_start(segment.start_time)

                # 应用音量调整
                if segment.volume != 1.0:
                    clip = clip.fx(volumex, segment.volume)

                # 应用淡入淡出效果
                if segment.fade_in > 0:
                    clip = clip.audio_fadein(segment.fade_in)
                if segment.fade_out > 0:
                    clip = clip.audio_fadeout(segment.fade_out)

                audio_clips.append(clip)
                logger.info(f"处理音频片段完成: {segment.id}")

            except Exception as e:
                logger.error(f"处理音频片段失败 {segment.id}: {e}")
                continue

        return audio_clips

    async def _compose_final_video(self, video_clips: List[Any],
                                 image_clips: List[Any],
                                 audio_clips: List[Any],
                                 project: SynthesisProject,
                                 progress_callback: Optional[Callable] = None) -> Any:
        """合成最终视频"""
        try:
            # 合并所有视频片段（包括图像转视频）
            all_video_clips = video_clips + image_clips

            if not all_video_clips:
                # 如果没有视频片段，创建一个黑色背景
                bg_color = project.config.background_color
                background = ColorClip(
                    size=(project.config.output_width, project.config.output_height),
                    color=bg_color,
                    duration=project.total_duration
                )
                all_video_clips = [background]

            # 应用转场效果
            if project.config.enable_transitions and len(all_video_clips) > 1:
                all_video_clips = self._apply_transitions(all_video_clips, project.transitions)

            # 创建复合视频
            final_video = CompositeVideoClip(all_video_clips, size=(project.config.output_width, project.config.output_height))

            # 合并音频
            if audio_clips:
                final_audio = CompositeAudioClip(audio_clips)
                final_video = final_video.set_audio(final_audio)

            # 设置帧率
            final_video = final_video.set_fps(project.config.fps)

            return final_video

        except Exception as e:
            logger.error(f"合成最终视频失败: {e}")
            raise

    async def _export_video(self, video: Any, output_path: Path,
                           config: SynthesisConfig, progress_callback: Optional[Callable] = None):
        """导出视频文件"""
        try:
            # 构建导出参数（适配MoviePy 1.0.3）
            export_params = {
                'filename': str(output_path),
                'fps': config.fps,
                'codec': config.video_codec,
                'bitrate': config.video_bitrate,
                'audio_codec': config.audio_codec,
                'verbose': False,
                'logger': None  # 禁用MoviePy的日志输出
            }

            # MoviePy 1.0.3不支持某些参数，需要适配
            try:
                # 尝试添加preset参数
                export_params['preset'] = config.quality_preset
            except:
                pass

            # 导出视频
            video.write_videofile(**export_params)

            # 清理资源
            try:
                video.close()
            except:
                pass

            logger.info(f"视频导出完成: {output_path}")

        except Exception as e:
            logger.error(f"导出视频失败: {e}")
            raise

    def _apply_video_effects(self, clip: Any, effects: List[Dict[str, Any]]) -> Any:
        """应用视频效果"""
        for effect in effects:
            try:
                effect_type = effect.get('type')

                if effect_type == 'resize':
                    width = effect.get('width', clip.w)
                    height = effect.get('height', clip.h)
                    clip = clip.fx(resize, (width, height))

                elif effect_type == 'speed':
                    factor = effect.get('factor', 1.0)
                    clip = clip.fx(lambda c: c.speedx(factor))

                elif effect_type == 'brightness':
                    factor = effect.get('factor', 1.0)
                    clip = clip.fx(lambda c: c.fx(lambda img: img * factor))

                # 可以添加更多效果...

            except Exception as e:
                logger.warning(f"应用视频效果失败 {effect_type}: {e}")
                continue

        return clip

    def _apply_image_effects(self, clip: Any, effects: List[Dict[str, Any]]) -> Any:
        """应用图像效果"""
        for effect in effects:
            try:
                effect_type = effect.get('type')

                if effect_type == 'ken_burns':
                    # Ken Burns效果（缩放+平移）
                    zoom_factor = effect.get('zoom_factor', 1.2)
                    clip = clip.resize(zoom_factor).set_position('center')

                elif effect_type == 'fade':
                    fade_duration = effect.get('duration', 0.5)
                    clip = clip.fx(fadein, fade_duration).fx(fadeout, fade_duration)

                # 可以添加更多效果...

            except Exception as e:
                logger.warning(f"应用图像效果失败 {effect_type}: {e}")
                continue

        return clip

    def _apply_transitions(self, clips: List[Any],
                          transitions: List[TransitionEffect]) -> List[Any]:
        """应用转场效果"""
        if len(clips) <= 1:
            return clips

        processed_clips = [clips[0]]

        for i in range(1, len(clips)):
            current_clip = clips[i]

            # 查找适用的转场效果
            transition = None
            if i - 1 < len(transitions):
                transition = transitions[i - 1]

            if transition:
                # 应用转场效果
                current_clip = self._apply_single_transition(processed_clips[-1], current_clip, transition)

            processed_clips.append(current_clip)

        return processed_clips

    def _apply_single_transition(self, clip1: Any,
                               clip2: Any,
                               transition: TransitionEffect) -> Any:
        """应用单个转场效果"""
        try:
            handler = self.transition_handlers.get(transition.type, self._apply_cut_transition)
            return handler(clip1, clip2, transition)
        except Exception as e:
            logger.warning(f"应用转场效果失败 {transition.type}: {e}")
            return clip2

    def _apply_fade_transition(self, clip1: Any,
                             clip2: Any,
                             transition: TransitionEffect) -> Any:
        """应用淡入淡出转场"""
        if not MOVIEPY_AVAILABLE:
            return clip2

        duration = transition.duration

        # 调整第二个片段的开始时间，使其与第一个片段重叠
        overlap_start = clip1.start + clip1.duration - duration
        clip2 = clip2.set_start(overlap_start)

        # 应用淡出和淡入效果
        try:
            clip2 = clip2.fx(fadein, duration)
        except Exception as e:
            logger.warning(f"应用淡入效果失败: {e}")
            # 如果fx方法失败，尝试直接调用
            try:
                clip2 = fadein(clip2, duration)
            except Exception as e2:
                logger.warning(f"直接调用淡入效果也失败: {e2}")
                # 如果都失败了，返回原片段

        return clip2

    def _apply_dissolve_transition(self, clip1: Any,
                                 clip2: Any,
                                 transition: TransitionEffect) -> Any:
        """应用溶解转场"""
        if not MOVIEPY_AVAILABLE:
            return clip2

        duration = transition.duration

        # 创建交叉淡化效果
        overlap_start = clip1.start + clip1.duration - duration
        clip2 = clip2.set_start(overlap_start)
        try:
            clip2 = clip2.fx(crossfadein, duration)
        except Exception as e:
            logger.warning(f"应用交叉淡入效果失败: {e}")
            # 降级到普通淡入
            try:
                clip2 = clip2.fx(fadein, duration)
            except Exception as e2:
                logger.warning(f"降级淡入效果也失败: {e2}")
                # 如果都失败了，返回原片段

        return clip2

    def _apply_slide_transition(self, clip1: Any,
                              clip2: Any,
                              transition: TransitionEffect) -> Any:
        """应用滑动转场"""
        # 简化实现，实际可以更复杂
        return self._apply_fade_transition(clip1, clip2, transition)

    def _apply_zoom_transition(self, clip1: Any,
                             clip2: Any,
                             transition: TransitionEffect) -> Any:
        """应用缩放转场"""
        # 简化实现，实际可以更复杂
        return self._apply_fade_transition(clip1, clip2, transition)

    def _apply_cut_transition(self, clip1: Any,
                            clip2: Any,
                            transition: TransitionEffect) -> Any:
        """应用切换转场（无过渡）"""
        return clip2

    def save_project(self, project: SynthesisProject, file_path: str = None) -> str:
        """保存合成项目到文件"""
        if file_path is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            file_path = self.output_dir / f"{project.name}_{timestamp}.json"

        try:
            # 转换为可序列化的格式
            project_data = {
                'name': project.name,
                'video_segments': [
                    {
                        'id': seg.id,
                        'file_path': seg.file_path,
                        'start_time': seg.start_time,
                        'duration': seg.duration,
                        'volume': seg.volume,
                        'fade_in': seg.fade_in,
                        'fade_out': seg.fade_out,
                        'effects': seg.effects,
                        'metadata': seg.metadata
                    } for seg in project.video_segments
                ],
                'audio_segments': [
                    {
                        'id': seg.id,
                        'file_path': seg.file_path,
                        'start_time': seg.start_time,
                        'duration': seg.duration,
                        'volume': seg.volume,
                        'fade_in': seg.fade_in,
                        'fade_out': seg.fade_out,
                        'track_type': seg.track_type,
                        'metadata': seg.metadata
                    } for seg in project.audio_segments
                ],
                'image_segments': [
                    {
                        'id': seg.id,
                        'file_path': seg.file_path,
                        'start_time': seg.start_time,
                        'duration': seg.duration,
                        'effects': seg.effects,
                        'metadata': seg.metadata
                    } for seg in project.image_segments
                ],
                'transitions': [
                    {
                        'type': trans.type,
                        'duration': trans.duration,
                        'parameters': trans.parameters
                    } for trans in project.transitions
                ],
                'config': {
                    'output_width': project.config.output_width,
                    'output_height': project.config.output_height,
                    'fps': project.config.fps,
                    'video_codec': project.config.video_codec,
                    'audio_codec': project.config.audio_codec,
                    'video_bitrate': project.config.video_bitrate,
                    'audio_bitrate': project.config.audio_bitrate,
                    'background_color': project.config.background_color,
                    'enable_transitions': project.config.enable_transitions,
                    'quality_preset': project.config.quality_preset
                },
                'total_duration': project.total_duration,
                'metadata': project.metadata
            }

            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(project_data, f, ensure_ascii=False, indent=2)

            logger.info(f"项目已保存: {file_path}")
            return str(file_path)

        except Exception as e:
            logger.error(f"保存项目失败: {e}")
            raise

    def save_project(self, project: SynthesisProject, file_path: str = None) -> str:
        """保存合成项目到文件"""
        if file_path is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            file_path = self.output_dir / f"{project.name}_{timestamp}.json"

        try:
            # 转换为可序列化的格式
            project_data = {
                'name': project.name,
                'video_segments': [
                    {
                        'id': seg.id,
                        'file_path': seg.file_path,
                        'start_time': seg.start_time,
                        'duration': seg.duration,
                        'volume': seg.volume,
                        'fade_in': seg.fade_in,
                        'fade_out': seg.fade_out,
                        'effects': seg.effects,
                        'metadata': seg.metadata
                    } for seg in project.video_segments
                ],
                'audio_segments': [
                    {
                        'id': seg.id,
                        'file_path': seg.file_path,
                        'start_time': seg.start_time,
                        'duration': seg.duration,
                        'volume': seg.volume,
                        'fade_in': seg.fade_in,
                        'fade_out': seg.fade_out,
                        'track_type': seg.track_type,
                        'metadata': seg.metadata
                    } for seg in project.audio_segments
                ],
                'image_segments': [
                    {
                        'id': seg.id,
                        'file_path': seg.file_path,
                        'start_time': seg.start_time,
                        'duration': seg.duration,
                        'effects': seg.effects,
                        'metadata': seg.metadata
                    } for seg in project.image_segments
                ],
                'transitions': [
                    {
                        'type': trans.type,
                        'duration': trans.duration,
                        'parameters': trans.parameters
                    } for trans in project.transitions
                ],
                'config': {
                    'output_width': project.config.output_width,
                    'output_height': project.config.output_height,
                    'fps': project.config.fps,
                    'video_codec': project.config.video_codec,
                    'audio_codec': project.config.audio_codec,
                    'video_bitrate': project.config.video_bitrate,
                    'audio_bitrate': project.config.audio_bitrate,
                    'background_color': project.config.background_color,
                    'enable_transitions': project.config.enable_transitions,
                    'quality_preset': project.config.quality_preset
                },
                'total_duration': project.total_duration,
                'metadata': project.metadata
            }

            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(project_data, f, ensure_ascii=False, indent=2)

            logger.info(f"项目已保存: {file_path}")
            return str(file_path)

        except Exception as e:
            logger.error(f"保存项目失败: {e}")
            raise

    def load_project(self, file_path: str) -> SynthesisProject:
        """从文件加载合成项目"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                project_data = json.load(f)

            # 重建配置对象
            config_data = project_data.get('config', {})
            config = SynthesisConfig(
                output_width=config_data.get('output_width', 1920),
                output_height=config_data.get('output_height', 1080),
                fps=config_data.get('fps', 24),
                video_codec=config_data.get('video_codec', 'libx264'),
                audio_codec=config_data.get('audio_codec', 'aac'),
                video_bitrate=config_data.get('video_bitrate', '5000k'),
                audio_bitrate=config_data.get('audio_bitrate', '128k'),
                background_color=tuple(config_data.get('background_color', [0, 0, 0])),
                enable_transitions=config_data.get('enable_transitions', True),
                quality_preset=config_data.get('quality_preset', 'medium')
            )

            # 重建项目对象
            project = SynthesisProject(
                name=project_data['name'],
                config=config,
                total_duration=project_data.get('total_duration', 0.0),
                metadata=project_data.get('metadata', {})
            )

            # 重建视频片段
            for seg_data in project_data.get('video_segments', []):
                segment = VideoSegment(
                    id=seg_data['id'],
                    file_path=seg_data['file_path'],
                    start_time=seg_data['start_time'],
                    duration=seg_data['duration'],
                    volume=seg_data.get('volume', 1.0),
                    fade_in=seg_data.get('fade_in', 0.0),
                    fade_out=seg_data.get('fade_out', 0.0),
                    effects=seg_data.get('effects', []),
                    metadata=seg_data.get('metadata', {})
                )
                project.video_segments.append(segment)

            # 重建音频片段
            for seg_data in project_data.get('audio_segments', []):
                segment = AudioSegment(
                    id=seg_data['id'],
                    file_path=seg_data['file_path'],
                    start_time=seg_data['start_time'],
                    duration=seg_data['duration'],
                    volume=seg_data.get('volume', 1.0),
                    fade_in=seg_data.get('fade_in', 0.0),
                    fade_out=seg_data.get('fade_out', 0.0),
                    track_type=seg_data.get('track_type', 'voice'),
                    metadata=seg_data.get('metadata', {})
                )
                project.audio_segments.append(segment)

            # 重建图像片段
            for seg_data in project_data.get('image_segments', []):
                segment = ImageSegment(
                    id=seg_data['id'],
                    file_path=seg_data['file_path'],
                    start_time=seg_data['start_time'],
                    duration=seg_data['duration'],
                    effects=seg_data.get('effects', []),
                    metadata=seg_data.get('metadata', {})
                )
                project.image_segments.append(segment)

            # 重建转场效果
            for trans_data in project_data.get('transitions', []):
                transition = TransitionEffect(
                    type=trans_data['type'],
                    duration=trans_data['duration'],
                    parameters=trans_data.get('parameters', {})
                )
                project.transitions.append(transition)

            logger.info(f"项目已加载: {file_path}")
            return project

        except Exception as e:
            logger.error(f"加载项目失败: {e}")
            raise

    def get_project_info(self, project: SynthesisProject) -> Dict[str, Any]:
        """获取项目信息摘要"""
        return {
            'name': project.name,
            'total_duration': project.total_duration,
            'video_segments_count': len(project.video_segments),
            'audio_segments_count': len(project.audio_segments),
            'image_segments_count': len(project.image_segments),
            'transitions_count': len(project.transitions),
            'output_resolution': f"{project.config.output_width}x{project.config.output_height}",
            'fps': project.config.fps,
            'created_time': project.metadata.get('created_time'),
            'estimated_file_size': self._estimate_file_size(project)
        }

    def _estimate_file_size(self, project: SynthesisProject) -> str:
        """估算输出文件大小"""
        try:
            # 简单估算：基于分辨率、时长和比特率
            width = project.config.output_width
            height = project.config.output_height
            duration = project.total_duration

            # 提取比特率数值（去掉单位）
            video_bitrate = int(project.config.video_bitrate.replace('k', '').replace('M', '000'))
            audio_bitrate = int(project.config.audio_bitrate.replace('k', ''))

            # 估算大小（MB）
            estimated_mb = (video_bitrate + audio_bitrate) * duration / 8 / 1024

            if estimated_mb < 1024:
                return f"{estimated_mb:.1f} MB"
            else:
                return f"{estimated_mb/1024:.1f} GB"

        except Exception:
            return "未知"
