# AI视频生成器项目概览

## 项目简介

AI视频生成器是一个基于PyQt5的桌面应用程序，旨在通过AI技术实现从文本到视频的完整工作流程。项目采用模块化架构设计，支持多种AI服务提供商，具备智能分镜、图像生成、一致性控制等核心功能。

## 技术架构

### 核心技术栈
- **前端框架**: PyQt5 + PyQtWebEngine
- **后端架构**: 模块化设计，基于服务层和处理器层
- **AI服务**: 多提供商支持 (智谱AI、通义千问、Deepseek等)
- **图像生成**: Pollinations AI、ComfyUI、Stability AI
- **数据存储**: JSON文件 + 项目文件系统

### 架构设计

```
┌─────────────────────────────────────────────────────────────┐
│                    用户界面层 (GUI)                          │
├─────────────────────────────────────────────────────────────┤
│  主窗口  │ 五阶段分镜 │ 图像生成 │ 一致性控制 │ 设置面板    │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                    控制器层 (Core)                           │
├─────────────────────────────────────────────────────────────┤
│        应用控制器 (AppController)                            │
│        项目管理器 (ProjectManager)                           │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                    处理器层 (Processors)                     │
├─────────────────────────────────────────────────────────────┤
│  文本处理器 │ 图像处理器 │ 视频处理器 │ 一致性增强处理器    │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                    服务层 (Services)                         │
├─────────────────────────────────────────────────────────────┤
│   LLM服务   │  图像生成服务  │  翻译服务  │  语音合成服务    │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                    工具层 (Utils)                            │
├─────────────────────────────────────────────────────────────┤
│   日志系统  │  配置管理  │  文件管理  │  角色场景管理       │
└─────────────────────────────────────────────────────────────┘
```

## 核心功能模块

### 1. 五阶段分镜系统
- **阶段1**: 世界观构建 - 分析文本背景和设定
- **阶段2**: 角色管理 - 提取和管理角色信息
- **阶段3**: 场景分析 - 分析场景结构和关系
- **阶段4**: 分镜生成 - 生成详细分镜脚本
- **阶段5**: 优化建议 - 提供专业优化建议

### 2. 图像生成系统
- **多引擎支持**: Pollinations AI、ComfyUI、Stability AI
- **参数同步**: 设置界面与生成界面双向同步
- **批量处理**: 支持批量生成和管理
- **预览功能**: 实时预览和图像管理

### 3. 一致性控制系统
- **角色一致性**: 维护角色外观和特征
- **场景一致性**: 保持场景风格统一
- **智能增强**: LLM驱动的描述优化
- **数据同步**: 自动更新项目文件

### 4. 项目管理系统
- **统一数据存储**: 基于project.json的数据管理
- **版本控制**: 自动备份和恢复
- **导入导出**: 支持项目数据的导入导出
- **文件组织**: 规范的项目文件结构

## 数据流程

### 文本处理流程
```
原始文本 → LLM改写 → 五阶段分镜 → 分镜脚本 → 图像生成 → 视频合成
```

### 图像生成流程
```
分镜描述 → 一致性增强 → 翻译处理 → 图像生成引擎 → 图像后处理 → 项目存储
```

### 数据存储结构
```
project.json
├── project_info          # 项目基本信息
├── five_stage_storyboard # 五阶段分镜数据
├── image_generation      # 图像生成数据
├── consistency_config    # 一致性配置
├── shot_image_mappings   # 镜头图像映射
└── settings             # 各种设置信息
```

## 开发规范

### 代码组织
- **模块化设计**: 每个功能模块独立开发和测试
- **接口标准化**: 统一的API接口设计
- **错误处理**: 完善的异常处理和日志记录
- **配置管理**: 集中的配置文件管理

### 文件命名规范
- **Python文件**: 使用下划线命名 (snake_case)
- **类名**: 使用驼峰命名 (PascalCase)
- **函数名**: 使用下划线命名 (snake_case)
- **常量**: 使用大写字母和下划线 (UPPER_CASE)

### 文档规范
- **代码注释**: 中文注释，详细说明功能和参数
- **函数文档**: 使用docstring格式
- **模块文档**: 每个模块包含功能说明
- **用户文档**: 详细的使用说明和示例

## 扩展性设计

### 插件系统 (计划中)
- **AI服务插件**: 支持新的AI服务提供商
- **图像生成插件**: 支持新的图像生成引擎
- **处理器插件**: 支持自定义处理逻辑
- **UI插件**: 支持自定义界面组件

### API接口 (计划中)
- **RESTful API**: 提供HTTP接口
- **WebSocket**: 实时通信支持
- **批处理API**: 支持批量操作
- **状态查询API**: 查询处理状态

## 性能优化

### 内存管理
- **延迟加载**: 按需加载大型资源
- **缓存机制**: 智能缓存常用数据
- **垃圾回收**: 及时释放不用的资源
- **内存监控**: 实时监控内存使用

### 网络优化
- **连接池**: 复用网络连接
- **请求缓存**: 缓存API响应
- **超时控制**: 合理的超时设置
- **重试机制**: 自动重试失败请求

### 用户体验
- **异步处理**: 避免界面卡顿
- **进度显示**: 实时显示处理进度
- **错误提示**: 友好的错误信息
- **操作反馈**: 及时的操作反馈

## 质量保证

### 测试策略
- **单元测试**: 核心功能单元测试
- **集成测试**: 模块间集成测试
- **用户测试**: 真实场景用户测试
- **性能测试**: 性能和压力测试

### 代码质量
- **代码审查**: 定期代码审查
- **静态分析**: 使用工具检查代码质量
- **格式化**: 统一的代码格式
- **重构**: 持续改进代码结构

## 未来规划

### 短期目标 (1-3个月)
- [ ] 完善视频合成功能
- [ ] 优化图像生成性能
- [ ] 增加更多AI服务支持
- [ ] 改进用户界面体验

### 中期目标 (3-6个月)
- [ ] 开发插件系统
- [ ] 提供API接口
- [ ] 支持云端部署
- [ ] 增加协作功能

### 长期目标 (6-12个月)
- [ ] 开发Web版本
- [ ] 支持实时协作
- [ ] 集成更多AI能力
- [ ] 商业化部署方案
