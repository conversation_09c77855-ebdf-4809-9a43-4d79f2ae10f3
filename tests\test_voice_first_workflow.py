"""
配音优先工作流程测试脚本
用于验证新工作流程的正确性和性能
"""

import os
import sys
import json
import logging
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.core.voice_first_workflow import VoiceFirstWorkflow
from src.utils.audio_duration_analyzer import AudioDurationAnalyzer
from src.core.voice_image_sync import VoiceImageSyncManager

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class MockProjectManager:
    """模拟项目管理器用于测试"""
    
    def __init__(self):
        self.project_data = {
            'project_name': '测试项目',
            'project_dir': str(project_root / 'test_project'),
            'five_stage_storyboard': {
                'stage_data': {
                    '2': {
                        'character_scene_data': {
                            'characters': {
                                '李青山': {
                                    'appearance': '年轻男性，朴实的农村青年形象'
                                }
                            }
                        }
                    },
                    '3': {
                        'scenes_analysis': '场景1：东北小山村，冬天雪景，寒冷的环境'
                    }
                }
            }
        }
    
    def get_project_data(self):
        return self.project_data
    
    def save_project_data(self, data):
        self.project_data = data
        logger.info("项目数据已保存")

def create_test_voice_data():
    """创建测试配音数据"""
    return [
        {
            'index': 0,
            'scene_id': '场景1',
            'shot_id': '镜头1',
            'dialogue_text': '大家好，我是来自东北小山村的我没错，就是那个紧挨着漠河的地方，冬天冷得能把人冻成冰雕。',
            'audio_path': '',  # 测试中没有实际音频文件
            'content_type': '旁白',
            'sound_effect': '',
            'status': '已生成'
        },
        {
            'index': 1,
            'scene_id': '场景1',
            'shot_id': '镜头2',
            'dialogue_text': '但是，我没有被困难打倒。',
            'audio_path': '',
            'content_type': '旁白',
            'sound_effect': '',
            'status': '已生成'
        },
        {
            'index': 2,
            'scene_id': '场景2',
            'shot_id': '镜头3',
            'dialogue_text': '通过努力学习和不懈奋斗，我终于改变了自己的命运，走出了大山。',
            'audio_path': '',
            'content_type': '旁白',
            'sound_effect': '',
            'status': '已生成'
        }
    ]

def test_audio_duration_analyzer():
    """测试音频时长分析器"""
    logger.info("=== 测试音频时长分析器 ===")
    
    analyzer = AudioDurationAnalyzer()
    voice_data = create_test_voice_data()
    
    # 测试批量分析
    duration_map = analyzer.batch_analyze(voice_data)
    logger.info(f"时长分析结果: {duration_map}")
    
    # 测试图像需求计算
    image_requirements = analyzer.calculate_image_requirements(duration_map)
    logger.info(f"图像需求: {image_requirements}")
    
    # 生成分析报告
    report = analyzer.export_analysis_report(duration_map, image_requirements)
    logger.info(f"分析报告: {json.dumps(report, indent=2, ensure_ascii=False)}")
    
    return duration_map, image_requirements

def test_voice_first_workflow():
    """测试配音优先工作流程"""
    logger.info("=== 测试配音优先工作流程 ===")
    
    # 创建模拟项目管理器
    project_manager = MockProjectManager()
    
    # 创建工作流程管理器
    workflow = VoiceFirstWorkflow(project_manager)
    
    # 加载测试数据
    voice_data = create_test_voice_data()
    success = workflow.load_voice_data(voice_data)
    logger.info(f"加载配音数据: {'成功' if success else '失败'}")
    
    if not success:
        return None
    
    # 计算图像需求
    image_requirements = workflow.calculate_image_requirements()
    logger.info(f"计算图像需求: {len(image_requirements)} 个")
    
    # 增强提示词
    enhance_success = workflow.enhance_image_prompts()
    logger.info(f"增强提示词: {'成功' if enhance_success else '失败'}")
    
    # 导出数据
    generation_data = workflow.export_to_image_generation_format()
    logger.info(f"导出数据: {len(generation_data.get('storyboard_data', []))} 个镜头")
    
    # 保存数据
    save_success = workflow.save_workflow_data()
    logger.info(f"保存数据: {'成功' if save_success else '失败'}")
    
    return generation_data

def test_voice_image_sync():
    """测试配音-图像同步机制"""
    logger.info("=== 测试配音-图像同步机制 ===")
    
    # 创建模拟项目管理器
    project_manager = MockProjectManager()
    
    # 创建同步管理器
    sync_manager = VoiceImageSyncManager(project_manager)
    
    # 准备测试数据
    voice_data = create_test_voice_data()
    
    # 模拟图像需求数据
    image_requirements = [
        {
            'voice_segment_index': 0,
            'scene_id': '场景1',
            'shot_id': '镜头1',
            'image_index': 0,
            'image_path': '/test/image1_0.jpg'
        },
        {
            'voice_segment_index': 0,
            'scene_id': '场景1',
            'shot_id': '镜头1',
            'image_index': 1,
            'image_path': '/test/image1_1.jpg'
        },
        {
            'voice_segment_index': 1,
            'scene_id': '场景1',
            'shot_id': '镜头2',
            'image_index': 0,
            'image_path': '/test/image2_0.jpg'
        },
        {
            'voice_segment_index': 2,
            'scene_id': '场景2',
            'shot_id': '镜头3',
            'image_index': 0,
            'image_path': '/test/image3_0.jpg'
        },
        {
            'voice_segment_index': 2,
            'scene_id': '场景2',
            'shot_id': '镜头3',
            'image_index': 1,
            'image_path': '/test/image3_1.jpg'
        }
    ]
    
    # 添加时长信息
    for voice in voice_data:
        voice['duration'] = len(voice['dialogue_text']) / 4.0  # 简单估算
    
    # 创建同步时间轴
    success = sync_manager.create_sync_timeline(voice_data, image_requirements)
    logger.info(f"创建时间轴: {'成功' if success else '失败'}")
    
    if success:
        # 优化时间轴
        optimize_success = sync_manager.optimize_timeline()
        logger.info(f"优化时间轴: {'成功' if optimize_success else '失败'}")
        
        # 导出时间轴数据
        timeline_data = sync_manager.export_timeline_data()
        logger.info(f"时间轴数据: {json.dumps(timeline_data, indent=2, ensure_ascii=False)}")
        
        # 保存同步数据
        save_success = sync_manager.save_sync_data()
        logger.info(f"保存同步数据: {'成功' if save_success else '失败'}")
    
    return sync_manager

def test_integration():
    """集成测试：完整的配音优先工作流程"""
    logger.info("=== 集成测试：完整工作流程 ===")
    
    try:
        # 步骤1：分析音频时长
        duration_map, image_requirements = test_audio_duration_analyzer()
        
        # 步骤2：执行配音优先工作流程
        generation_data = test_voice_first_workflow()
        
        # 步骤3：创建同步时间轴
        sync_manager = test_voice_image_sync()
        
        # 验证结果
        if generation_data and sync_manager:
            logger.info("✅ 集成测试成功！配音优先工作流程运行正常")
            
            # 输出统计信息
            storyboard_data = generation_data.get('storyboard_data', [])
            logger.info(f"📊 统计信息:")
            logger.info(f"   - 配音段落: {generation_data.get('total_voice_segments', 0)} 个")
            logger.info(f"   - 图像需求: {generation_data.get('total_image_requirements', 0)} 个")
            logger.info(f"   - 分镜数据: {len(storyboard_data)} 个")
            
            return True
        else:
            logger.error("❌ 集成测试失败")
            return False
            
    except Exception as e:
        logger.error(f"❌ 集成测试异常: {e}")
        return False

def main():
    """主测试函数"""
    logger.info("🚀 开始配音优先工作流程测试")
    
    try:
        # 运行集成测试
        success = test_integration()
        
        if success:
            logger.info("🎉 所有测试通过！配音优先工作流程已准备就绪")
        else:
            logger.error("💥 测试失败，请检查错误信息")
            
    except Exception as e:
        logger.error(f"💥 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
