# AI视频生成器 🎬

一个功能强大的AI驱动视频生成工具，支持从文本到视频的完整工作流程。通过五阶段智能分镜系统、多引擎图像生成和一致性控制，为用户提供专业级的视频制作体验。

## ✨ 核心特性

### 🎯 五阶段智能分镜系统
- **阶段1**: 世界观构建 - 自动生成故事背景和设定
- **阶段2**: 角色管理 - 智能提取和管理角色信息
- **阶段3**: 场景分析 - 深度分析场景结构和关系
- **阶段4**: 分镜生成 - 生成详细的分镜脚本
- **阶段5**: 优化建议 - 提供专业的优化建议

### 🖼️ 多引擎图像生成
- **Pollinations AI** - 免费在线生成，支持多种模型
- **ComfyUI** - 本地高质量生成，支持自定义工作流
- **Stability AI** - 专业级图像生成服务
- **智能参数同步** - 设置界面与分镜生成界面双向同步
- **批量生成** - 支持一键批量生成所有镜头图像

### 🎨 一致性增强系统
- **角色一致性** - 自动维护角色外观和特征描述
- **场景一致性** - 保持场景风格和氛围统一
- **智能描述增强** - LLM驱动的描述优化和技术细节融合
- **实时预览** - 图像生成后自动刷新预览区域

### 🌐 多语言翻译支持
- **LLM翻译** - 高质量的上下文感知翻译
- **百度翻译** - 备用翻译方案，确保稳定性
- **自动切换** - 翻译失败时自动使用备用方案

### 🎙️ 语音合成 (开发中)
- **Edge TTS** - 高质量免费语音合成
- **多语言支持** - 支持中文、英文等多种语言
- **批量处理** - 支持批量语音生成

## 🚀 快速开始

### 环境要求
- Python 3.8+
- PyQt5 和 PyQtWebEngine
- 4GB+ RAM (推荐8GB)
- 稳定的网络连接 (用于AI服务调用)

### 安装步骤

1. **克隆项目**
```bash
git clone https://github.com/yourusername/AI_Video_Generator.git
cd AI_Video_Generator
```

2. **安装依赖**
```bash
pip install -r requirements.txt
```

3. **配置API密钥**

复制配置模板并填入您的API密钥：
```bash
# Windows
copy config\llm_config.example.json config\llm_config.json
copy config\baidu_translate_config.example.py config\baidu_translate_config.py

# Linux/Mac
cp config/llm_config.example.json config/llm_config.json
cp config/baidu_translate_config.example.py config/baidu_translate_config.py
```

编辑 `config/llm_config.json`：
```json
{
  "zhipu": {
    "api_key": "your-zhipu-api-key",
    "api_url": "https://open.bigmodel.cn/api/paas/v4/chat/completions"
  },
  "tongyi": {
    "api_key": "your-tongyi-api-key"
  },
  "deepseek": {
    "api_key": "your-deepseek-api-key"
  }
}
```

编辑 `config/baidu_translate_config.py`：
```python
BAIDU_TRANSLATE_CONFIG = {
    'app_id': 'your_app_id_here',
    'secret_key': 'your_secret_key_here',
    'api_url': 'https://fanyi-api.baidu.com/api/trans/vip/translate'
}
```

4. **启动应用**
```bash
python main.py
```

## 📖 使用指南

### 基础工作流程

1. **创建项目**
   - 点击"新建项目"创建新项目，或"打开项目"加载现有项目
   - 输入项目名称和描述信息

2. **文本处理**
   - 在"📝 文本处理"标签页输入原始文本
   - 选择合适的LLM模型和改写风格
   - 点击"改写文本"进行AI优化

3. **五阶段分镜生成**
   - 切换到"🎬 五阶段分镜"标签页
   - 按顺序完成五个阶段的生成：
     - 阶段1：世界观构建
     - 阶段2：角色管理
     - 阶段3：场景分析
     - 阶段4：分镜生成
     - 阶段5：优化建议

4. **图像生成**
   - 切换到"🖼️ 分镜图像生成"标签页
   - 在设置中配置图像生成引擎和参数
   - 使用"批量生成"或单独生成镜头图像
   - 支持预览、设为主图、删除图像等操作

5. **视频合成** (开发中)
   - 在"🎥 视频处理"标签页配置参数
   - 添加背景音乐和转场效果
   - 生成最终视频

### 高级功能

#### 一致性控制
- 在"一致性控制"面板管理角色和场景信息
- 使用"智能增强"功能优化描述内容
- 自动同步更新到项目JSON文件

#### 参数同步
- 在"⚙️ 设置"→"AI绘图"中配置图像生成参数
- 参数会自动双向同步到分镜图像生成界面
- 支持Pollinations AI、ComfyUI等多种引擎

#### 图像管理
- 自动预览生成的图像
- 支持设置主图功能
- 支持删除不满意的图像
- 批量生成时可跳过已生成的图像

## 🔧 配置说明

### 图像生成配置

#### Pollinations AI (推荐)
- **模型**: flux, flux-turbo, gptimage等多种模型
- **尺寸**: 支持1024x1024、1280x720等多种分辨率
- **种子值**: 随机/固定模式，程序自动处理实际数值
- **增强**: 可选启用AI增强功能
- **Logo**: 可选隐藏Pollinations水印

#### ComfyUI (本地部署)
- **服务器地址**: 默认 http://127.0.0.1:8188
- **工作流**: 支持自定义工作流文件
- **参数**: 完整的ComfyUI参数支持
- **模型**: 支持各种Stable Diffusion模型

#### Stability AI (需要API密钥)
- **模型**: SDXL、SD3等专业模型
- **高质量**: 专业级图像生成效果
- **商用**: 支持商业用途

### LLM配置
支持多种大语言模型提供商：
- **智谱AI (GLM-4)** - 推荐，性价比高
- **通义千问** - 阿里云服务
- **Deepseek** - 高性能模型
- **Google Gemini** - 谷歌服务

### 翻译配置
- **主要翻译**: LLM翻译，上下文感知，质量更高
- **备用翻译**: 百度翻译API，稳定可靠
- **自动切换**: 主翻译失败时自动使用备用方案

## 📁 项目结构

详细的项目结构说明请参考：[项目结构文档](docs/PROJECT_STRUCTURE.md)

```
AI_Video_Generator/
├── 📄 main.py              # 主程序入口
├── 📄 start.py             # 启动脚本
├── 📄 requirements.txt     # 依赖包列表
├── 📁 src/                 # 源代码目录
│   ├── core/              # 核心架构和控制器
│   ├── services/          # AI服务接口层
│   ├── processors/        # 数据处理器
│   ├── gui/              # 用户界面组件
│   ├── models/           # 数据模型定义
│   └── utils/            # 工具和辅助模块
├── 📁 config/              # 配置文件目录
├── 📁 docs/                # 文档目录
├── 📁 sound_library/       # 本地音效库
├── 📁 output/              # 项目输出目录
├── 📁 temp/                # 临时文件目录
├── 📁 logs/                # 日志文件目录
└── 📁 assets/              # 资源文件目录
```

## 🛠️ 故障排除

### 常见问题

1. **程序启动失败**
   - 检查Python版本 (需要3.8+)
   - 确认PyQt5和PyQtWebEngine已正确安装
   - 查看 `logs/system.log` 获取详细错误信息
   - 尝试重新安装依赖：`pip install -r requirements.txt`

2. **图像生成失败**
   - **Pollinations AI**: 检查网络连接，确认服务可访问
   - **ComfyUI**: 确认本地ComfyUI服务器正在运行
   - **Stability AI**: 检查API密钥配置是否正确
   - 查看日志中的具体错误信息

3. **LLM功能异常**
   - 检查 `config/llm_config.json` 中的API密钥配置
   - 验证API密钥是否有效且有足够余额
   - 确认网络连接正常，可访问对应的API服务

4. **翻译功能异常**
   - 检查LLM翻译配置
   - 验证百度翻译API密钥 (备用方案)
   - 确认网络连接正常

5. **界面显示问题**
   - 重启程序重新初始化界面
   - 检查是否有组件加载失败的错误信息
   - 确认PyQtWebEngine正确安装

### 日志查看
详细的运行日志保存在 `logs/system.log`，包含：
- 系统初始化和组件加载信息
- API调用记录和响应状态
- 错误详情和完整堆栈跟踪
- 图像生成和处理过程记录

## 🤝 贡献指南

欢迎提交Issue和Pull Request！

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 发起Pull Request

## 📄 许可证

本项目采用MIT许可证 - 详见 [LICENSE](LICENSE) 文件

## 🙏 致谢

感谢以下开源项目和服务：
- PyQt5 - 用户界面框架
- Pollinations AI - 免费图像生成服务
- ComfyUI - 强大的图像生成工具
- 各大LLM提供商的API支持

## 🎯 使用技巧

### 提高生成质量
1. **文本准备**
   - 使用清晰、具体的描述
   - 避免过于复杂的句子结构
   - 包含足够的细节信息

2. **分镜优化**
   - 合理控制每个镜头的时长
   - 注意镜头间的逻辑连贯性
   - 使用多样化的镜头语言

3. **图像生成**
   - 根据内容选择合适的引擎
   - 调整参数以获得最佳效果
   - 利用一致性功能保持风格统一

### 性能优化
- 使用SSD存储提高I/O性能
- 关闭不必要的后台程序
- 定期清理临时文件和缓存

## 📊 更新日志

### v4.0.0 (2025-06-19) - 当前版本
- ✨ 新增图像预览自动刷新功能
- 🖼️ 实现设为主图和删除图像功能
- 🔄 修复项目保存时的数据获取问题
- 🎯 优化图像生成后的用户体验
- 📁 重构项目结构，整理文档和依赖

### v3.0.0 (2025-06-17)
- ✨ 新增智能种子值管理系统
- 🔄 实现AI绘图设置与分镜生成的双向参数同步
- 🎨 优化Pollinations AI界面，移除无关状态显示
- 🛠️ 改进一致性描述增强功能
- 📝 完善用户文档和使用说明

### v2.1.0
- 🌐 优化LLM翻译提示词，增强翻译准确性
- 🔄 新增百度翻译作为备用翻译方案
- 🛡️ 改进翻译失败处理机制
- 📈 增强系统稳定性和容错能力

### v2.0.0
- 🏗️ 全新的模块化架构设计
- 🔌 多AI服务提供商支持
- ⚡ 异步处理和现代化UI界面
- 🎨 智能优化和一致性保持系统
- 📋 完整的错误处理和日志系统

## 🔗 相关文档

### 📋 项目文档
- [项目结构说明](docs/PROJECT_STRUCTURE.md)
- [项目概览](docs/PROJECT_OVERVIEW.md)
- [项目状态报告](docs/PROJECT_STATUS.md)
- [部署指南](docs/DEPLOYMENT.md)
- [更新日志](docs/CHANGELOG.md)
- [贡献指南](docs/CONTRIBUTING.md)

### 🛠️ 技术文档
- [一致性控制系统指南](docs/CONSISTENCY_SYSTEM_GUIDE.md)
- [ComfyUI安装指南](docs/ComfyUI_Setup_Guide.md)
- [语音生成指南](docs/voice_generation_guide.md)
- [统一数据管理指南](docs/UNIFIED_DATA_MANAGEMENT_GUIDE.md)

### 📖 使用说明
- [一致性描述增强功能使用说明](docs/一致性描述增强功能使用说明.md)
- [场景描述增强器使用说明](docs/场景描述增强器使用说明.md)
- [智能角色检测系统使用说明](docs/智能角色检测系统使用说明.md)

### 📁 更多文档
- [完整文档目录](docs/)

## 💡 常见用例

### 1. 短视频制作
- 输入故事大纲或剧本
- 自动生成分镜和图像
- 添加配音和背景音乐
- 导出适合社交媒体的视频

### 2. 教育内容制作
- 将教学内容转换为可视化视频
- 生成配套的图像和动画
- 添加解说和字幕
- 制作互动式学习材料

### 3. 营销视频制作
- 根据产品描述生成宣传视频
- 自动配置品牌风格
- 批量生成多版本内容
- 快速迭代和优化

### 4. 故事板制作
- 将小说或剧本可视化
- 生成详细的故事板
- 预览视觉效果
- 辅助影视制作前期规划

## 📞 支持与反馈

如果您在使用过程中遇到问题或有改进建议，欢迎：
- 提交 [GitHub Issues](https://github.com/yourusername/AI_Video_Generator/issues)
- 发起 [Pull Request](https://github.com/yourusername/AI_Video_Generator/pulls)
- 查看 [项目Wiki](https://github.com/yourusername/AI_Video_Generator/wiki)

---

**免责声明**: 本工具仅供学习和研究使用。使用本工具生成内容时，请遵守相关法律法规和平台使用条款。生成的内容仅供参考，请根据实际需要进行调整和优化。
