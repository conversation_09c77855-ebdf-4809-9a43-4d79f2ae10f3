# 项目加载和图像数据恢复修复报告

## 问题描述

用户反馈在重新启动程序加载项目后遇到以下问题：

1. **图像数据未成功加载**：日志显示"保存了 0 个镜头的图像数据"和"恢复了 0 个镜头的图像数据"
2. **设置保存失败**：在更改生图参数后点击保存设置时提示"没有打开的项目，无法保存设置"

## 根本原因分析

### 1. 项目管理器实例不一致

**问题**：应用中存在多个项目管理器实例，导致状态不同步
- `AppController` 创建了一个项目管理器实例
- `NewMainWindow` 又创建了另一个项目管理器实例
- `StoryboardImageGenerationTab` 使用的项目管理器可能与实际加载项目的实例不同

**影响**：分镜图像生成标签页检查项目状态时，使用的是没有加载项目的实例，因此总是提示"没有打开的项目"

### 2. 图像数据加载不完整

**问题**：项目加载时没有正确恢复图像生成相关数据
- `load_project` 方法在组合项目数据时遗漏了 `image_generation` 和 `image_generation_settings` 字段
- 导致已生成的图像数据和设置在项目重新加载后丢失

## 修复方案

### 1. 统一项目管理器实例

**修改文件**：
- `src/core/app_controller.py`
- `src/gui/new_main_window.py`
- `src/gui/storyboard_image_generation_tab.py`

**具体修改**：

1. **AppController 使用单例模式**：
```python
# 使用单例管理器获取项目管理器实例
from src.core.singleton_manager import get_singleton_service
self.project_manager = get_singleton_service(
    'project_manager',
    lambda: StoryboardProjectManager(config_dir)
)
```

2. **主窗口使用应用控制器的实例**：
```python
# 使用应用控制器的项目管理器实例（确保一致性）
self.project_manager = self.app_controller.project_manager
```

3. **分镜图像生成标签页优化项目检查逻辑**：
```python
# 优先使用传入的项目管理器，然后尝试从应用控制器获取
project_manager = self.project_manager
if not project_manager:
    from src.core.app_controller import AppController
    app_controller = AppController.get_instance()
    if app_controller and hasattr(app_controller, 'project_manager'):
        project_manager = app_controller.project_manager
```

### 2. 完善图像数据加载

**修改文件**：`src/utils/project_manager.py`

**具体修改**：

1. **在 load_project 方法中添加图像数据字段**：
```python
project_data = {
    # ... 其他字段
    'image_generation': config_data.get('image_generation', {}),  # 添加图像生成数据
    'image_generation_settings': config_data.get('image_generation_settings', {}),  # 添加图像生成设置
    # ... 其他字段
}
```

2. **优化图像数据保存和恢复逻辑**：
```python
def _preserve_existing_image_data(self):
    """保存现有的图像数据"""
    existing_data = {}
    try:
        # 如果storyboard_data为空，尝试从项目数据中加载图像信息
        if not hasattr(self, 'storyboard_data') or not self.storyboard_data:
            self._load_image_data_from_project()
        
        for shot_data in getattr(self, 'storyboard_data', []):
            # ... 保存逻辑
```

### 3. 增强项目加载流程

**修改文件**：`src/gui/new_main_window.py`

**具体修改**：

1. **在项目加载时更新分镜图像生成标签页**：
```python
def _load_complex_components(self, project_config):
    """分阶段加载复杂组件数据"""
    # ... 其他组件
    
    # 第二阶段：更新分镜图像生成标签页
    if hasattr(self, 'storyboard_image_tab') and self.storyboard_image_tab:
        logger.info("开始更新分镜图像生成标签页...")
        QTimer.singleShot(200, self._update_storyboard_image_tab)
```

2. **添加分镜图像标签页更新方法**：
```python
def _update_storyboard_image_tab(self):
    """更新分镜图像生成标签页"""
    try:
        if hasattr(self, 'storyboard_image_tab') and self.storyboard_image_tab:
            # 重新加载分镜数据
            self.storyboard_image_tab.load_storyboard_data()
            # 加载生成设置
            self.storyboard_image_tab.load_generation_settings()
    except Exception as e:
        logger.error(f"更新分镜图像生成标签页失败: {e}")
```

## 测试验证

### 测试用例

1. **项目管理器一致性测试**：验证所有组件使用同一个项目管理器实例
2. **图像数据保存和恢复测试**：验证图像数据的持久化功能
3. **用户场景模拟测试**：模拟完整的项目加载和设置保存流程

### 测试结果

```
=== 测试结果汇总 ===
通过: 2/2
✅ 所有用户场景测试通过！

修复总结:
1. ✅ 项目管理器实例一致性问题已修复
2. ✅ 图像数据保存和恢复逻辑已优化
3. ✅ 设置保存时的项目检查逻辑已改进
```

## 修复效果

### 解决的问题

1. **项目状态同步**：所有组件现在使用同一个项目管理器实例，确保项目状态一致
2. **图像数据恢复**：项目重新加载后能正确恢复已生成的图像数据和设置
3. **设置保存功能**：分镜图像生成标签页能正确识别已加载的项目并保存设置

### 用户体验改善

1. **无缝项目切换**：用户重新启动程序后，之前的工作状态能完整恢复
2. **数据持久化**：已生成的图像和配置设置不会丢失
3. **功能稳定性**：消除了"没有打开的项目"的错误提示

## 技术要点

### 单例模式应用

使用单例管理器确保关键服务（如项目管理器）在整个应用中只有一个实例，避免状态不一致问题。

### 数据完整性保证

在项目数据的保存和加载过程中，确保所有相关字段都被正确处理，避免数据丢失。

### 异步组件更新

使用 QTimer 实现分阶段的组件更新，确保 UI 完全初始化后再进行数据加载。

## 后续建议

1. **监控机制**：添加项目状态监控，及时发现数据不一致问题
2. **数据备份**：增强项目数据的备份机制，防止数据丢失
3. **错误恢复**：添加更多的错误恢复逻辑，提高系统健壮性

---

**修复完成时间**：2025-06-18  
**测试状态**：✅ 全部通过  
**影响范围**：项目管理、图像生成、数据持久化
