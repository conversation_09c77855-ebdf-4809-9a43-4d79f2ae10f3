#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
清理无用的自动生成场景数据
删除所有"镜头场景_"开头的场景数据
"""

import sys
import os
import json
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.utils.logger import logger

def clean_useless_scenes(project_dir):
    """清理指定项目目录下的无用场景数据"""
    try:
        scenes_file = os.path.join(project_dir, "data", "scenes.json")
        
        if not os.path.exists(scenes_file):
            print(f"场景文件不存在: {scenes_file}")
            return 0
        
        # 读取现有场景数据
        with open(scenes_file, 'r', encoding='utf-8') as f:
            scenes_db = json.load(f)
        
        original_scenes = scenes_db.get("scenes", {})
        original_count = len(original_scenes)
        
        print(f"原始场景数量: {original_count}")
        
        # 过滤掉无用的场景
        cleaned_scenes = {}
        removed_count = 0
        
        for scene_id, scene_data in original_scenes.items():
            scene_name = scene_data.get('name', '')
            
            # 检查是否是无用的自动生成场景
            is_useless = (
                scene_id.startswith('镜头场景_') or
                scene_id.startswith('分镜场景_') or
                '{' in scene_id or
                scene_name.startswith('scene_name_') or
                (scene_name.startswith('场景') and len(scene_name) > 2 and scene_name[2:].isdigit())
            )
            
            if is_useless:
                print(f"删除无用场景: {scene_id} - {scene_name}")
                removed_count += 1
            else:
                cleaned_scenes[scene_id] = scene_data
                print(f"保留有用场景: {scene_id} - {scene_name}")
        
        # 更新场景数据
        scenes_db["scenes"] = cleaned_scenes
        scenes_db["last_updated"] = "2025-06-24 11:10:00"
        
        # 保存清理后的数据
        with open(scenes_file, 'w', encoding='utf-8') as f:
            json.dump(scenes_db, f, ensure_ascii=False, indent=2)
        
        print(f"\n清理完成:")
        print(f"  - 原始场景数量: {original_count}")
        print(f"  - 删除场景数量: {removed_count}")
        print(f"  - 剩余场景数量: {len(cleaned_scenes)}")
        
        return removed_count
        
    except Exception as e:
        logger.error(f"清理场景数据失败: {e}")
        print(f"❌ 清理过程中出现错误: {e}")
        return 0

def main():
    """主函数"""
    print("=== 清理无用的自动生成场景数据 ===")
    
    # 清理天下无双项目的场景数据
    project_dir = "output/天下无双"
    
    if not os.path.exists(project_dir):
        print(f"❌ 项目目录不存在: {project_dir}")
        return
    
    print(f"正在清理项目: {project_dir}")
    
    removed_count = clean_useless_scenes(project_dir)
    
    if removed_count > 0:
        print(f"\n✅ 成功清理了 {removed_count} 个无用场景")
        print("\n📋 修复说明:")
        print("1. 已禁用一致性控制面板中的自动提取功能")
        print("2. 删除了所有'镜头场景_'开头的无用场景数据")
        print("3. 保留了用户手动创建的有用场景数据")
        print("4. 重新加载项目后不会再生成这些无用数据")
    else:
        print("\n✅ 没有发现需要清理的无用场景")
    
    print("\n🎯 核心修复:")
    print("- 禁用了 consistency_control_panel.py 中的自动提取功能")
    print("- 避免在加载分镜数据时生成无用的'镜头场景_'数据")
    print("- 用户可以手动在角色场景管理中进行必要的提取")
    
    print("\n✨ 修复完成！重新加载项目后不会再看到这些无用的场景数据。")

if __name__ == "__main__":
    main()
