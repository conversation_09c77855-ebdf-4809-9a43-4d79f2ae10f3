#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
视频生成UI界面测试脚本
独立测试视频生成设置和视频生成界面
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent))

from PyQt5.QtWidgets import QApplication, QMainWindow, QTabWidget, QVBoxLayout, QWidget
from PyQt5.QtCore import Qt

def test_video_generation_settings():
    """测试视频生成设置界面"""
    print("🔧 测试视频生成设置界面...")
    
    try:
        from src.gui.video_generation_settings_widget import VideoGenerationSettingsWidget
        
        app = QApplication(sys.argv)
        
        # 创建主窗口
        main_window = QMainWindow()
        main_window.setWindowTitle("视频生成设置测试")
        main_window.resize(800, 600)
        
        # 创建设置组件
        settings_widget = VideoGenerationSettingsWidget()
        main_window.setCentralWidget(settings_widget)
        
        # 显示窗口
        main_window.show()
        
        print("✅ 视频生成设置界面创建成功")
        print("💡 请在界面中测试各种设置功能")
        
        # 运行应用
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"❌ 视频生成设置界面测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_video_generation_tab():
    """测试视频生成主界面"""
    print("🎬 测试视频生成主界面...")
    
    try:
        from src.gui.video_generation_tab import VideoGenerationTab
        
        app = QApplication(sys.argv)
        
        # 创建主窗口
        main_window = QMainWindow()
        main_window.setWindowTitle("视频生成界面测试")
        main_window.resize(1200, 800)
        
        # 创建模拟的项目管理器
        class MockProjectManager:
            def __init__(self):
                self.current_project = "test_project"
                self.current_project_name = "测试项目"
            
            def get_project_data(self):
                return {
                    'scenes': [
                        {
                            'title': '场景1',
                            'shots': [
                                {
                                    'title': '镜头1',
                                    'description': '一个美丽的日出场景',
                                    'enhanced_description': '金色的阳光洒在宁静的湖面上，远山如黛，云彩飘逸'
                                },
                                {
                                    'title': '镜头2', 
                                    'description': '鸟儿在树枝上歌唱',
                                    'enhanced_description': '小鸟站在樱花盛开的枝头，清脆的歌声回荡在春日的空气中'
                                }
                            ]
                        },
                        {
                            'title': '场景2',
                            'shots': [
                                {
                                    'title': '镜头1',
                                    'description': '孩子们在公园里玩耍',
                                    'enhanced_description': '阳光明媚的公园里，孩子们欢快地奔跑，笑声如银铃般清脆'
                                }
                            ]
                        }
                    ],
                    'voices': {
                        'scene_0_shot_0': {'file_path': 'test_voice1.wav'},
                        'scene_0_shot_1': {'file_path': 'test_voice2.wav'},
                        'scene_1_shot_0': {'file_path': 'test_voice3.wav'}
                    },
                    'images': {
                        'scene_0_shot_0': {'file_path': 'test_image1.jpg'},
                        'scene_0_shot_1': {'file_path': 'test_image2.jpg'},
                        'scene_1_shot_0': {'file_path': 'test_image3.jpg'}
                    },
                    'videos': {},
                    'project_path': os.getcwd()
                }
        
        # 创建模拟的应用控制器
        class MockAppController:
            pass
        
        # 创建视频生成标签页
        mock_project_manager = MockProjectManager()
        mock_app_controller = MockAppController()
        
        video_tab = VideoGenerationTab(mock_app_controller, mock_project_manager)
        main_window.setCentralWidget(video_tab)
        
        # 显示窗口
        main_window.show()
        
        print("✅ 视频生成主界面创建成功")
        print("💡 请在界面中测试各种功能:")
        print("   - 查看场景列表")
        print("   - 选择场景和镜头")
        print("   - 调整生成参数")
        print("   - 测试生成功能")
        
        # 运行应用
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"❌ 视频生成主界面测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_combined_interface():
    """测试组合界面"""
    print("🎯 测试组合界面...")
    
    try:
        from src.gui.video_generation_settings_widget import VideoGenerationSettingsWidget
        from src.gui.video_generation_tab import VideoGenerationTab
        
        app = QApplication(sys.argv)
        
        # 创建主窗口
        main_window = QMainWindow()
        main_window.setWindowTitle("视频生成完整界面测试")
        main_window.resize(1400, 900)
        
        # 创建标签页容器
        tab_widget = QTabWidget()
        
        # 添加视频生成设置标签页
        settings_widget = VideoGenerationSettingsWidget()
        tab_widget.addTab(settings_widget, "🔧 视频生成设置")
        
        # 创建模拟数据
        class MockProjectManager:
            def __init__(self):
                self.current_project = "test_project"
                self.current_project_name = "测试项目"
            
            def get_project_data(self):
                return {
                    'scenes': [
                        {
                            'title': '开场场景',
                            'shots': [
                                {
                                    'title': '远景镜头',
                                    'description': '壮丽的山川景色',
                                    'enhanced_description': '连绵起伏的山峦在晨雾中若隐若现，金色的阳光穿透云层洒向大地'
                                },
                                {
                                    'title': '特写镜头',
                                    'description': '花朵上的露珠',
                                    'enhanced_description': '晶莹剔透的露珠在花瓣上滚动，折射出七彩的光芒'
                                }
                            ]
                        }
                    ],
                    'voices': {
                        'scene_0_shot_0': {'file_path': 'voice1.wav'},
                        'scene_0_shot_1': {'file_path': 'voice2.wav'}
                    },
                    'images': {
                        'scene_0_shot_0': {'file_path': 'image1.jpg'},
                        'scene_0_shot_1': {'file_path': 'image2.jpg'}
                    },
                    'videos': {},
                    'project_path': os.getcwd()
                }
        
        class MockAppController:
            pass
        
        # 添加视频生成主界面标签页
        mock_project_manager = MockProjectManager()
        mock_app_controller = MockAppController()
        
        video_tab = VideoGenerationTab(mock_app_controller, mock_project_manager)
        tab_widget.addTab(video_tab, "🎬 视频生成")
        
        # 设置为主窗口的中央组件
        main_window.setCentralWidget(tab_widget)
        
        # 显示窗口
        main_window.show()
        
        print("✅ 组合界面创建成功")
        print("💡 功能说明:")
        print("   📋 设置标签页:")
        print("      - CogVideoX-Flash引擎配置")
        print("      - API密钥设置")
        print("      - 生成参数调整")
        print("      - 连接测试功能")
        print("   📋 生成标签页:")
        print("      - 场景和镜头列表")
        print("      - 图像预览")
        print("      - 生成参数控制")
        print("      - 批量和单个生成")
        
        # 运行应用
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"❌ 组合界面测试失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("🎬 视频生成UI界面测试")
    print("=" * 50)
    
    if len(sys.argv) > 1:
        test_type = sys.argv[1]
    else:
        print("请选择测试类型:")
        print("1. 视频生成设置界面")
        print("2. 视频生成主界面")
        print("3. 组合界面")
        
        choice = input("请输入选择 (1-3): ").strip()
        test_type = choice
    
    if test_type == "1":
        test_video_generation_settings()
    elif test_type == "2":
        test_video_generation_tab()
    elif test_type == "3":
        test_combined_interface()
    else:
        print("❌ 无效的选择")
        print("使用方法: python test_video_generation_ui.py [1|2|3]")

if __name__ == "__main__":
    main()
