#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI视频生成器主程序
直接启动主窗口，无需登录验证
"""

import sys
import atexit
import traceback
from PyQt5.QtWidgets import QApplication, QMessageBox
from PyQt5.QtCore import Qt

def exit_handler():
    """
    程序退出处理函数
    """
    print("程序正在退出...")

def setup_global_exception_handler():
    """设置全局异常处理器"""
    def handle_exception(exc_type, exc_value, exc_traceback):
        """全局异常处理函数"""
        if issubclass(exc_type, KeyboardInterrupt):
            # 允许Ctrl+C正常退出
            sys.__excepthook__(exc_type, exc_value, exc_traceback)
            return

        # 记录异常信息
        error_msg = ''.join(traceback.format_exception(exc_type, exc_value, exc_traceback))
        print(f"未捕获的异常: {error_msg}", file=sys.stderr)

        # 尝试使用统一错误处理器
        try:
            from src.utils.error_handler import handle_error
            handle_error(exc_value, {
                'source': 'global_exception_handler',
                'type': exc_type.__name__,
                'traceback': error_msg
            })
        except Exception as handler_error:
            print(f"错误处理器失败: {handler_error}", file=sys.stderr)
            # 降级到基本错误显示
            QMessageBox.critical(None, "严重错误",  # type: ignore
                               f"程序遇到未处理的异常:\n{exc_value}\n\n"
                               f"请查看控制台获取详细信息。")

    # 设置全局异常处理器
    sys.excepthook = handle_exception

if __name__ == "__main__":
    # 注册退出处理函数
    atexit.register(exit_handler)

    # 设置全局异常处理器
    setup_global_exception_handler()

    # 设置Qt属性以支持QtWebEngine
    try:
        QApplication.setAttribute(Qt.AA_ShareOpenGLContexts)  # type: ignore
    except AttributeError:
        # 如果属性不存在，忽略错误
        pass

    app = QApplication(sys.argv)

    try:
        # 导入并创建主窗口
        from src.gui.new_main_window import NewMainWindow

        print("正在启动主程序...")
        main_window = NewMainWindow()
        main_window.show()
        print("主程序窗口已显示")

        # 启动事件循环
        sys.exit(app.exec_())

    except ImportError as e:
        error_msg = f"导入主窗口失败: {e}"
        print(error_msg, file=sys.stderr)

        # 尝试使用统一错误处理器
        try:
            from src.utils.error_handler import handle_error
            handle_error(e, {
                'source': 'main_import_error',
                'module': 'src.gui.new_main_window'
            })
        except Exception:
            # 降级到基本错误显示
            QMessageBox.critical(None, "导入错误",  # type: ignore
                               f"无法启动主程序:\n{e}\n\n"
                               f"请检查依赖包是否正确安装。")
        sys.exit(1)

    except Exception as e:
        error_msg = f"启动主程序失败: {e}"
        print(error_msg, file=sys.stderr)
        print(traceback.format_exc(), file=sys.stderr)

        # 尝试使用统一错误处理器
        try:
            from src.utils.error_handler import handle_error
            handle_error(e, {
                'source': 'main_startup_error',
                'traceback': traceback.format_exc()
            })
        except Exception:
            # 降级到基本错误显示
            QMessageBox.critical(None, "启动错误",  # type: ignore
                               f"启动主程序时发生错误:\n{e}\n\n"
                               f"请查看控制台获取详细信息。")
        sys.exit(1)