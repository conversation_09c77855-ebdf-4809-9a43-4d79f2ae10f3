"""
配音优先工作流程集成测试
验证新的工作流程是否按预期工作
"""

import sys
import json
import logging
from pathlib import Path

# 添加项目根目录到路径
sys.path.append('.')

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_voice_first_workflow():
    """测试配音优先工作流程"""
    try:
        print("🚀 开始测试配音优先工作流程")
        
        # 导入核心模块
        from src.core.voice_first_workflow import VoiceFirstWorkflow
        from src.utils.audio_duration_analyzer import AudioDurationAnalyzer
        from src.core.voice_image_sync import VoiceImageSyncManager
        
        print("✅ 所有核心模块导入成功")
        
        # 创建测试数据
        test_voice_data = [
            {
                'index': 0,
                'scene_id': '场景1',
                'shot_id': '镜头1',
                'dialogue_text': '大家好，我是来自东北小山村的我没错，就是那个紧挨着漠河的地方，冬天冷得能把人冻成冰雕。',
                'audio_path': '',
                'content_type': '旁白',
                'sound_effect': '',
                'status': '已生成'
            },
            {
                'index': 1,
                'scene_id': '场景1',
                'shot_id': '镜头2',
                'dialogue_text': '但是，我没有被困难打倒。',
                'audio_path': '',
                'content_type': '旁白',
                'sound_effect': '',
                'status': '已生成'
            },
            {
                'index': 2,
                'scene_id': '场景2',
                'shot_id': '镜头3',
                'dialogue_text': '通过努力学习和不懈奋斗，我终于改变了自己的命运，走出了大山。',
                'audio_path': '',
                'content_type': '旁白',
                'sound_effect': '',
                'status': '已生成'
            }
        ]
        
        print(f"📝 创建测试数据：{len(test_voice_data)} 个配音段落")
        
        # 测试1：音频时长分析
        print("\n🔍 测试1：音频时长分析")
        analyzer = AudioDurationAnalyzer()
        duration_map = analyzer.batch_analyze(test_voice_data)
        
        print(f"   时长分析结果：")
        for i, duration in duration_map.items():
            text = test_voice_data[i]['dialogue_text']
            print(f"   - 镜头{i+1}: {duration:.1f}秒 ({text[:20]}...)")
        
        # 计算图像需求
        image_requirements = analyzer.calculate_image_requirements(duration_map)
        total_images = sum(req['image_count'] for req in image_requirements.values())
        print(f"   📊 总计需要生成 {total_images} 张图片")
        
        # 测试2：配音优先工作流程
        print("\n🔄 测试2：配音优先工作流程")
        workflow = VoiceFirstWorkflow()
        
        # 加载配音数据
        if workflow.load_voice_data(test_voice_data):
            print("   ✅ 配音数据加载成功")
        else:
            print("   ❌ 配音数据加载失败")
            return False
        
        # 计算图像需求
        requirements = workflow.calculate_image_requirements()
        print(f"   📸 计算图像需求：{len(requirements)} 个")
        
        # 增强提示词
        if workflow.enhance_image_prompts():
            print("   ✅ 图像提示词增强成功")
        else:
            print("   ⚠️ 图像提示词增强失败（可能是LLM未配置）")
        
        # 导出数据
        generation_data = workflow.export_to_image_generation_format()
        if generation_data:
            storyboard_count = len(generation_data.get('storyboard_data', []))
            print(f"   📤 导出图像生成数据：{storyboard_count} 个分镜")
        else:
            print("   ❌ 导出图像生成数据失败")
            return False
        
        # 测试3：配音-图像同步
        print("\n🎬 测试3：配音-图像同步机制")
        sync_manager = VoiceImageSyncManager()
        
        # 准备图像需求数据
        mock_image_requirements = []
        for i, voice_data in enumerate(test_voice_data):
            duration = duration_map[i]
            image_count = image_requirements[i]['image_count']
            
            for img_idx in range(image_count):
                mock_image_requirements.append({
                    'voice_segment_index': i,
                    'scene_id': voice_data['scene_id'],
                    'shot_id': voice_data['shot_id'],
                    'image_index': img_idx,
                    'image_path': f'/test/image_{i}_{img_idx}.jpg'
                })
        
        # 添加时长信息
        for voice in test_voice_data:
            voice['duration'] = duration_map[voice['index']]
        
        # 创建同步时间轴
        if sync_manager.create_sync_timeline(test_voice_data, mock_image_requirements):
            print("   ✅ 同步时间轴创建成功")
            
            # 优化时间轴
            if sync_manager.optimize_timeline():
                print("   ✅ 时间轴优化成功")
            
            # 导出时间轴数据
            timeline_data = sync_manager.export_timeline_data()
            total_duration = timeline_data.get('total_duration', 0)
            segment_count = timeline_data.get('segment_count', 0)
            print(f"   📊 时间轴统计：{segment_count} 个时间段，总时长 {total_duration:.1f}秒")
            
        else:
            print("   ❌ 同步时间轴创建失败")
            return False
        
        # 测试结果总结
        print("\n🎉 测试结果总结：")
        print(f"   📝 配音段落：{len(test_voice_data)} 个")
        print(f"   🎵 总配音时长：{sum(duration_map.values()):.1f}秒")
        print(f"   📸 需要图片：{total_images} 张")
        print(f"   🎬 时间段：{segment_count} 个")
        print(f"   ⏱️ 总时长：{total_duration:.1f}秒")
        
        # 验证核心逻辑
        print("\n🔍 验证核心逻辑：")
        
        # 验证时长匹配
        for i, duration in duration_map.items():
            expected_images = image_requirements[i]['image_count']
            if duration <= 3.0:
                expected = 1
            elif duration <= 6.0:
                expected = 2
            else:
                expected = max(2, int(duration / 3.0))
            
            if expected_images == expected:
                print(f"   ✅ 镜头{i+1}: {duration:.1f}秒 → {expected_images}张图片 (正确)")
            else:
                print(f"   ❌ 镜头{i+1}: {duration:.1f}秒 → {expected_images}张图片 (期望{expected}张)")
                return False
        
        print("\n🎊 所有测试通过！配音优先工作流程运行正常！")
        return True
        
    except Exception as e:
        print(f"\n💥 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🎬 AI视频生成器 - 配音优先工作流程测试")
    print("=" * 60)
    
    success = test_voice_first_workflow()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 测试完成：配音优先工作流程已准备就绪！")
        print("\n📋 下一步操作：")
        print("1. 在AI配音界面完成配音生成")
        print("2. 点击'🎬 发送到图像生成'按钮")
        print("3. 系统将自动切换到配音优先模式")
        print("4. 根据配音时长生成匹配的图像")
    else:
        print("💥 测试失败：请检查错误信息并修复问题")
    print("=" * 60)

if __name__ == "__main__":
    main()
