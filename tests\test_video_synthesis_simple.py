#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的视频合成功能测试
"""

import os
import sys
import asyncio
import tempfile
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.processors.video_synthesis_processor import (
    VideoSynthesisProcessor, SynthesisProject, SynthesisConfig
)
from src.utils.logger import logger

def create_test_image(file_path: str, width: int = 640, height: int = 480, color: tuple = (255, 0, 0)):
    """创建测试图像"""
    try:
        from PIL import Image
        
        # 创建纯色图像
        image = Image.new('RGB', (width, height), color)
        image.save(file_path)
        return True
    except ImportError:
        logger.warning("PIL不可用，无法创建测试图像")
        return False

async def test_basic_functionality():
    """测试基本功能"""
    print("🧪 开始测试基本功能...")
    
    try:
        # 创建临时目录
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            
            # 创建测试图像
            image_path = temp_path / "test_image.png"
            if not create_test_image(str(image_path), color=(0, 255, 0)):  # 绿色
                print("❌ 无法创建测试图像")
                return False
            
            # 创建视频合成处理器
            processor = VideoSynthesisProcessor(output_dir=str(temp_path))
            
            # 创建项目
            config = SynthesisConfig(
                output_width=640,
                output_height=480,
                fps=24,
                quality_preset="fast"
            )
            
            project = processor.create_project("simple_test", config)
            print(f"✅ 项目已创建: {project.name}")
            
            # 添加图像片段
            processor.add_image_segment(project, str(image_path), 0.0, 2.0)
            print("✅ 图像片段已添加")
            
            # 保存项目
            project_file = processor.save_project(project)
            print(f"✅ 项目已保存: {project_file}")
            
            # 加载项目
            loaded_project = processor.load_project(project_file)
            print(f"✅ 项目已加载: {loaded_project.name}")
            
            # 获取项目信息
            info = processor.get_project_info(loaded_project)
            print(f"📊 项目信息:")
            for key, value in info.items():
                print(f"  {key}: {value}")
            
            # 检查MoviePy是否可用
            try:
                import moviepy.editor
                print("✅ MoviePy可用，尝试视频合成...")
                
                # 进度回调函数
                def progress_callback(progress: float, message: str):
                    print(f"📈 {progress*100:.1f}% - {message}")
                
                # 执行视频合成
                output_path = await processor.synthesize_video(
                    loaded_project, 
                    "simple_test.mp4",
                    progress_callback
                )
                
                print(f"✅ 视频合成完成: {output_path}")
                
                # 检查输出文件
                if os.path.exists(output_path):
                    file_size = os.path.getsize(output_path)
                    print(f"📁 输出文件大小: {file_size / 1024:.2f} KB")
                    return True
                else:
                    print("❌ 输出文件不存在")
                    return False
                    
            except ImportError:
                print("⚠️ MoviePy不可用，跳过视频合成")
                return True  # 其他功能测试通过
                
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        logger.error(f"简化测试失败: {e}")
        return False

def test_project_management():
    """测试项目管理功能"""
    print("\n🧪 开始测试项目管理...")
    
    try:
        with tempfile.TemporaryDirectory() as temp_dir:
            processor = VideoSynthesisProcessor(output_dir=temp_dir)
            
            # 创建项目
            config = SynthesisConfig(output_width=1280, output_height=720)
            project = processor.create_project("management_test", config)
            
            # 保存项目
            project_file = processor.save_project(project)
            
            # 加载项目
            loaded_project = processor.load_project(project_file)
            
            # 验证数据一致性
            assert project.name == loaded_project.name
            assert project.config.output_width == loaded_project.config.output_width
            assert project.config.output_height == loaded_project.config.output_height
            
            print("✅ 项目管理功能测试通过")
            return True
            
    except Exception as e:
        print(f"❌ 项目管理测试失败: {e}")
        return False

def test_error_handling():
    """测试错误处理"""
    print("\n🧪 开始测试错误处理...")
    
    try:
        processor = VideoSynthesisProcessor()
        project = processor.create_project("error_test")
        
        # 测试添加不存在的文件
        try:
            processor.add_image_segment(project, "nonexistent.jpg", 0.0, 5.0)
            print("❌ 应该抛出FileNotFoundError")
            return False
        except FileNotFoundError:
            print("✅ 正确处理了不存在的文件")
        
        print("✅ 错误处理测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 错误处理测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("🎬 AI视频生成器 - 简化视频合成功能测试")
    print("=" * 50)
    
    test_results = []
    
    # 运行测试
    test_results.append(await test_basic_functionality())
    test_results.append(test_project_management())
    test_results.append(test_error_handling())
    
    # 汇总结果
    print("\n" + "=" * 50)
    print("📊 测试结果汇总:")
    
    passed = sum(test_results)
    total = len(test_results)
    
    print(f"✅ 通过: {passed}/{total}")
    print(f"❌ 失败: {total - passed}/{total}")
    
    if passed == total:
        print("🎉 所有测试通过！视频合成功能基本正常。")
        return True
    else:
        print("⚠️ 部分测试失败，请检查相关功能。")
        return False

if __name__ == "__main__":
    # 运行测试
    result = asyncio.run(main())
    sys.exit(0 if result else 1)
