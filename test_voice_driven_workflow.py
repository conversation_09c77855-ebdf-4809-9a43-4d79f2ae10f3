"""
配音驱动工作流程测试
验证基于配音内容的五阶段分镜生成
"""

import sys
import json
import logging
from pathlib import Path

# 添加项目根目录到路径
sys.path.append('.')

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_voice_driven_workflow():
    """测试配音驱动工作流程"""
    try:
        print("🎭 开始测试配音驱动工作流程")
        
        # 导入核心模块
        from src.core.voice_driven_storyboard import VoiceDrivenStoryboardSystem
        
        print("✅ 配音驱动分镜系统导入成功")
        
        # 创建测试配音数据（基于您的示例）
        test_voice_data = [
            {
                'index': 0,
                'dialogue_text': '嘿，大家好让我跟你们聊聊我的故事',
                'audio_path': 'test_audio_001.mp3',
                'duration': 4.2,
                'content_type': '旁白',
                'sound_effect': '',
                'status': '已生成'
            },
            {
                'index': 1,
                'dialogue_text': '曾经有七年时间，我被困在一个地方，不过还好表现不错，提前出来了',
                'audio_path': 'test_audio_002.mp3',
                'duration': 6.8,
                'content_type': '旁白',
                'sound_effect': '',
                'status': '已生成'
            },
            {
                'index': 2,
                'dialogue_text': '出来第一天，就有好几个老板打电话给我，有的开价一个月十万还送车',
                'audio_path': 'test_audio_003.mp3',
                'duration': 7.5,
                'content_type': '旁白',
                'sound_effect': '电话铃声',
                'status': '已生成'
            },
            {
                'index': 3,
                'dialogue_text': '有的直接说一个月二十万加股份',
                'audio_path': 'test_audio_004.mp3',
                'duration': 3.8,
                'content_type': '旁白',
                'sound_effect': '',
                'status': '已生成'
            }
        ]
        
        print(f"📝 创建测试配音数据：{len(test_voice_data)} 个段落")
        for i, voice in enumerate(test_voice_data):
            print(f"   段落{i+1}: {voice['duration']}秒 - {voice['dialogue_text'][:30]}...")
        
        # 创建配音驱动分镜系统
        print("\n🔧 创建配音驱动分镜系统")
        voice_driven_system = VoiceDrivenStoryboardSystem()
        
        # 测试1：加载配音数据
        print("\n📥 测试1：加载配音数据")
        if voice_driven_system.load_voice_data(test_voice_data):
            print("   ✅ 配音数据加载成功")
            print(f"   📊 加载了 {len(voice_driven_system.voice_segments)} 个配音段落")
            total_duration = sum(seg.duration for seg in voice_driven_system.voice_segments)
            print(f"   ⏱️ 总时长：{total_duration:.1f}秒")
        else:
            print("   ❌ 配音数据加载失败")
            return False
        
        # 测试2：智能场景分析
        print("\n🎬 测试2：智能场景分析")
        if voice_driven_system.analyze_voice_driven_scenes():
            print("   ✅ 配音场景分析成功")
            print(f"   📊 识别了 {len(voice_driven_system.voice_driven_scenes)} 个场景")
            
            for i, scene in enumerate(voice_driven_system.voice_driven_scenes):
                print(f"   场景{i+1}: {scene.scene_name}")
                print(f"     - 时长: {scene.total_duration:.1f}秒")
                print(f"     - 段落数: {len(scene.voice_segments)}个")
                print(f"     - 情感: {scene.emotional_tone}")
                print(f"     - 描述: {scene.scene_description}")
        else:
            print("   ❌ 配音场景分析失败")
            return False
        
        # 测试3：生成五阶段分镜数据
        print("\n📋 测试3：生成五阶段分镜数据")
        if voice_driven_system.generate_voice_driven_storyboard():
            print("   ✅ 五阶段分镜数据生成成功")
            
            storyboard_data = voice_driven_system.storyboard_data
            print(f"   📊 生成了 {len(storyboard_data['stage_data'])} 个阶段的数据")
            
            # 检查第5阶段数据
            stage_5_data = storyboard_data['stage_data'].get('5', {})
            final_storyboard = stage_5_data.get('final_storyboard', [])
            print(f"   🎬 最终分镜：{len(final_storyboard)} 个镜头")
            
            # 显示分镜详情
            for i, shot in enumerate(final_storyboard):
                print(f"   镜头{i+1}: {shot['shot_id']}")
                print(f"     - 配音内容: {shot['voice_content'][:40]}...")
                print(f"     - 配音时长: {shot['voice_duration']}秒")
                print(f"     - 内容类型: {shot['content_type']}")
        else:
            print("   ❌ 五阶段分镜数据生成失败")
            return False
        
        # 测试4：验证内容一致性
        print("\n🔍 测试4：验证内容一致性")
        
        # 检查每个分镜是否都有对应的配音内容
        stage_5_data = voice_driven_system.storyboard_data['stage_data']['5']
        final_storyboard = stage_5_data['final_storyboard']
        
        consistency_check = True
        for i, shot in enumerate(final_storyboard):
            original_voice = test_voice_data[i]['dialogue_text']
            shot_voice = shot['voice_content']
            
            if original_voice == shot_voice:
                print(f"   ✅ 镜头{i+1}: 内容完全一致")
            else:
                print(f"   ❌ 镜头{i+1}: 内容不一致")
                print(f"      原始: {original_voice}")
                print(f"      分镜: {shot_voice}")
                consistency_check = False
        
        if consistency_check:
            print("   🎉 所有镜头内容完全一致！")
        else:
            print("   ⚠️ 发现内容不一致问题")
        
        # 测试5：时长匹配验证
        print("\n⏱️ 测试5：时长匹配验证")
        
        original_total = sum(voice['duration'] for voice in test_voice_data)
        storyboard_total = sum(shot['voice_duration'] for shot in final_storyboard)
        
        print(f"   原始配音总时长: {original_total:.1f}秒")
        print(f"   分镜配音总时长: {storyboard_total:.1f}秒")
        
        if abs(original_total - storyboard_total) < 0.1:
            print("   ✅ 时长完全匹配！")
        else:
            print(f"   ⚠️ 时长差异: {abs(original_total - storyboard_total):.1f}秒")
        
        # 测试结果总结
        print("\n" + "="*60)
        print("🎊 配音驱动工作流程测试结果总结")
        print("="*60)
        
        print(f"📝 配音段落数：{len(test_voice_data)}")
        print(f"🎬 识别场景数：{len(voice_driven_system.voice_driven_scenes)}")
        print(f"📋 生成镜头数：{len(final_storyboard)}")
        print(f"⏱️ 总时长：{original_total:.1f}秒")
        print(f"✅ 内容一致性：{'完美' if consistency_check else '需要优化'}")
        print(f"⏱️ 时长匹配：{'完美' if abs(original_total - storyboard_total) < 0.1 else '需要优化'}")
        
        # 展示工作流程优势
        print("\n🎯 配音驱动工作流程的优势：")
        print("1. ✅ 分镜内容完全基于实际配音")
        print("2. ✅ 时长精确匹配，无需后期调整")
        print("3. ✅ 智能场景分割，符合配音节奏")
        print("4. ✅ 情感基调与配音语调同步")
        print("5. ✅ 自动化生成，提高制作效率")
        
        print("\n🚀 下一步建议：")
        print("1. 在实际项目中测试配音驱动工作流程")
        print("2. 基于配音内容生成匹配的图像")
        print("3. 验证最终视频的内容一致性")
        
        return True
        
    except Exception as e:
        print(f"\n💥 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🎭 AI视频生成器 - 配音驱动工作流程测试")
    print("=" * 60)
    
    success = test_voice_driven_workflow()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 测试完成：配音驱动工作流程运行正常！")
        print("\n📋 实际使用步骤：")
        print("1. 完成文本改写或AI创作")
        print("2. 生成所有配音段落")
        print("3. 点击'🎭 生成配音驱动分镜'按钮")
        print("4. 系统自动生成基于配音的五阶段分镜")
        print("5. 进行图像生成，确保内容完全匹配")
    else:
        print("💥 测试失败：请检查错误信息并修复问题")
    print("=" * 60)

if __name__ == "__main__":
    main()
