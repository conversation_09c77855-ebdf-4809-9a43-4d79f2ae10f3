# 五阶段分镜系统失败检测机制

## 概述

为了解决用户在使用程序过程中遇到的与大模型通讯超时导致分镜或增强描述失败的问题，我们在五阶段分镜系统中加入了智能失败检测机制。该机制能够自动检测失败的分镜生成和增强描述，并提供便捷的重试功能。

## 功能特性

### 1. 分镜生成失败检测
- **自动检测**：系统会自动检测每个场景的分镜生成是否成功
- **失败判断标准**：
  - API返回错误信息（如超时、网络错误、API密钥错误等）
  - 返回内容过短（少于50字符）
  - 缺少基本分镜结构（如"镜头"、"画面描述"等关键词）
  - 返回空值或非字符串类型

### 2. 增强描述失败检测
- **逐场景检测**：对每个场景的增强描述生成进行独立检测
- **失败判断标准**：
  - 增强器返回空结果或非字典类型
  - 增强内容为空或过短（少于20字符）
  - 包含错误信息关键词
  - 增强器抛出异常

### 3. 智能重试机制
- **选择性重试**：用户可以选择需要重试的特定场景
- **批量重试**：支持一键重试所有失败项目
- **进度显示**：重试过程中显示详细进度信息
- **结果反馈**：重试完成后提供详细的成功/失败统计

## 使用流程

### 1. 自动检测
当用户执行分镜生成（阶段4）或增强描述时，系统会自动进行失败检测：

```
用户点击"生成分镜脚本" → 系统生成分镜 → 自动检测失败项目 → 如有失败则弹出检测对话框
用户点击"增强描述" → 系统增强描述 → 自动检测失败项目 → 如有失败则弹出检测对话框
```

### 2. 失败检测对话框
当检测到失败项目时，系统会自动弹出失败检测对话框，显示：

- **分镜失败标签页**：列出所有分镜生成失败的场景
- **增强描述失败标签页**：列出所有增强描述失败的场景
- **详细错误信息**：鼠标悬停在失败项目上可查看具体错误原因
- **选择功能**：支持全选/取消全选，或手动选择特定项目

### 3. 重试操作
用户可以通过以下方式进行重试：

- **重试选中项目**：只重试当前标签页中选中的项目
- **重试全部**：重试当前标签页中的所有失败项目
- **取消操作**：在重试过程中可以随时取消

## 技术实现

### 1. 失败检测类
```python
class FailureDetectionResult:
    """失败检测结果"""
    def __init__(self):
        self.failed_storyboards = []  # 失败的分镜列表
        self.failed_enhancements = []  # 失败的增强描述列表
        self.has_failures = False
        self.error_details = {}
```

### 2. 检测方法
- `_is_storyboard_generation_failed()`: 检测分镜生成是否失败
- `_is_enhancement_successful()`: 检测增强描述是否成功

### 3. 重试方法
- `_retry_single_storyboard()`: 重试单个分镜生成
- `_retry_single_enhancement()`: 重试单个增强描述

### 4. 信号连接
```python
# 分镜失败信号
self.worker_thread.storyboard_failed.connect(self.on_storyboard_failed)

# 增强描述失败信号
self.enhancement_thread.enhancement_failed.connect(self.on_enhancement_failed)
```

## 错误类型识别

### 1. 网络相关错误
- `请求超时`、`连接超时`
- `网络错误`、`network error`
- `timeout error`

### 2. API相关错误
- `api错误`、`api密钥`
- `invalid api key`
- `api调用失败`

### 3. 内容质量错误
- 返回内容过短
- 缺少必要的结构元素
- 返回空值或错误类型

## 用户体验优化

### 1. 友好的错误提示
- 清晰的错误分类和描述
- 具体的失败原因说明
- 操作建议和解决方案

### 2. 灵活的重试选项
- 支持选择性重试
- 批量操作功能
- 实时进度反馈

### 3. 状态保持
- 重试成功后自动更新原始数据
- 保持用户的工作进度
- 无缝集成到现有工作流程

## 注意事项

1. **网络环境**：确保网络连接稳定，避免频繁超时
2. **API配置**：检查LLM API配置是否正确
3. **重试限制**：避免过度重试，建议检查根本问题
4. **数据备份**：重要项目建议定期保存

## 故障排除

### 1. 重试仍然失败
- 检查网络连接
- 验证API密钥配置
- 查看系统日志获取详细错误信息

### 2. 检测误报
- 检查检测标准是否过于严格
- 查看具体的失败内容
- 联系技术支持获取帮助

### 3. 性能问题
- 避免同时重试过多项目
- 检查系统资源使用情况
- 考虑分批处理大量失败项目
