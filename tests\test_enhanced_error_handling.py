#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强错误处理系统测试
"""

import os
import sys
import time
import asyncio
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.utils.enhanced_error_handler import (
    enhanced_error_handler, handle_errors, handle_async_errors,
    ErrorDomain, ErrorLevel, ErrorContext, handle_exception
)
from src.utils.error_integration import (
    initialize_error_handling, report_error, get_error_summary,
    video_error_handler, api_error_handler, file_error_handler
)
from src.utils.logger import logger

def test_basic_error_handling():
    """测试基础错误处理"""
    print("🧪 测试基础错误处理...")
    
    try:
        # 测试文件不存在错误
        try:
            with open("nonexistent_file.txt", "r") as f:
                content = f.read()
        except Exception as e:
            error_record = handle_exception(
                e, 
                ErrorContext(user_action="读取配置文件"),
                "读取配置文件",
                show_to_user=False
            )
            
            assert error_record.domain == ErrorDomain.FILE_IO
            assert error_record.level == ErrorLevel.ERROR
            assert "nonexistent_file.txt" in error_record.user_message
            print("✅ 文件不存在错误处理正确")
        
        # 测试网络错误
        try:
            import requests
            response = requests.get("http://nonexistent-domain-12345.com", timeout=1)
        except Exception as e:
            error_record = handle_exception(
                e,
                ErrorContext(user_action="连接API服务"),
                "连接API服务",
                show_to_user=False
            )
            
            assert error_record.domain == ErrorDomain.NETWORK
            print("✅ 网络错误处理正确")
        
        # 测试数据验证错误
        try:
            int("not_a_number")
        except Exception as e:
            error_record = handle_exception(
                e,
                ErrorContext(user_action="解析用户输入"),
                "解析用户输入",
                show_to_user=False
            )
            
            assert error_record.domain == ErrorDomain.VALIDATION
            print("✅ 数据验证错误处理正确")
        
        return True
        
    except Exception as e:
        print(f"❌ 基础错误处理测试失败: {e}")
        return False

def test_error_decorators():
    """测试错误处理装饰器"""
    print("\n🧪 测试错误处理装饰器...")
    
    try:
        @file_error_handler
        def read_file(filename):
            with open(filename, "r") as f:
                return f.read()
        
        @api_error_handler
        async def call_api(url):
            import aiohttp
            async with aiohttp.ClientSession() as session:
                async with session.get(url) as response:
                    return await response.text()
        
        # 测试同步装饰器
        result = read_file("nonexistent.txt")
        assert result is None  # 默认返回值
        print("✅ 同步错误装饰器工作正常")
        
        # 测试异步装饰器
        async def test_async():
            result = await call_api("http://nonexistent-domain.com")
            assert result is None
            print("✅ 异步错误装饰器工作正常")

        # 创建新的事件循环来避免冲突
        import asyncio
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            loop.run_until_complete(test_async())
        finally:
            loop.close()
        
        return True
        
    except Exception as e:
        print(f"❌ 错误装饰器测试失败: {e}")
        return False

def test_error_statistics():
    """测试错误统计功能"""
    print("\n🧪 测试错误统计功能...")
    
    try:
        # 生成一些测试错误
        for i in range(3):
            try:
                raise FileNotFoundError(f"test_file_{i}.txt")
            except Exception as e:
                handle_exception(e, show_to_user=False)
        
        for i in range(2):
            try:
                raise ConnectionError(f"Connection failed {i}")
            except Exception as e:
                handle_exception(e, show_to_user=False)
        
        # 获取统计信息
        stats = enhanced_error_handler.get_error_statistics()
        
        assert stats['total_errors'] >= 5
        assert 'file_io' in stats['domain_statistics']
        assert 'network' in stats['domain_statistics']
        
        print(f"✅ 错误统计正常: 总错误数 {stats['total_errors']}")
        print(f"   域统计: {stats['domain_statistics']}")
        print(f"   级别统计: {stats['level_statistics']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 错误统计测试失败: {e}")
        return False

def test_error_suppression():
    """测试错误抑制功能"""
    print("\n🧪 测试错误抑制功能...")
    
    try:
        # 生成大量相同错误以触发抑制
        for i in range(10):
            try:
                raise ValueError("Repeated error for suppression test")
            except Exception as e:
                handle_exception(e, show_to_user=False)
        
        # 检查是否有错误被抑制
        stats = enhanced_error_handler.get_error_statistics()
        
        if stats['suppressed_error_types'] > 0:
            print("✅ 错误抑制功能正常工作")
        else:
            print("⚠️ 错误抑制功能未触发（可能需要更多重复错误）")
        
        return True
        
    except Exception as e:
        print(f"❌ 错误抑制测试失败: {e}")
        return False

def test_error_recovery():
    """测试错误恢复功能"""
    print("\n🧪 测试错误恢复功能...")
    
    try:
        # 测试可恢复的网络错误
        recovery_manager = enhanced_error_handler.recovery_manager
        
        # 模拟网络错误
        network_error = ConnectionError("Network connection failed")
        can_recover = recovery_manager.can_recover(ErrorDomain.NETWORK, network_error)
        
        if can_recover:
            recovered = recovery_manager.attempt_recovery(ErrorDomain.NETWORK, network_error)
            print(f"✅ 网络错误恢复测试: 可恢复={can_recover}, 恢复成功={recovered}")
        else:
            print("⚠️ 网络错误被判定为不可恢复")
        
        # 测试不可恢复的文件错误
        file_error = FileNotFoundError("File not found")
        can_recover_file = recovery_manager.can_recover(ErrorDomain.FILE_IO, file_error)
        print(f"✅ 文件错误恢复测试: 可恢复={can_recover_file} (应该为False)")
        
        return True
        
    except Exception as e:
        print(f"❌ 错误恢复测试失败: {e}")
        return False

def test_error_integration():
    """测试错误处理集成"""
    print("\n🧪 测试错误处理集成...")
    
    try:
        # 初始化错误处理集成
        success = initialize_error_handling()
        
        if success:
            print("✅ 错误处理集成初始化成功")
        else:
            print("⚠️ 错误处理集成初始化失败")
        
        # 测试错误报告功能
        try:
            raise RuntimeError("Test integration error")
        except Exception as e:
            error_id = report_error(e, "测试集成功能")
            print(f"✅ 错误报告功能正常，错误ID: {error_id}")
        
        # 测试错误摘要
        summary = get_error_summary()
        print(f"✅ 错误摘要: {summary}")
        
        return True
        
    except Exception as e:
        print(f"❌ 错误处理集成测试失败: {e}")
        return False

def test_error_logging():
    """测试错误日志功能"""
    print("\n🧪 测试错误日志功能...")
    
    try:
        # 检查日志文件是否创建
        log_file = enhanced_error_handler.log_file
        
        # 生成一个测试错误
        try:
            raise RuntimeError("Test logging error")
        except Exception as e:
            handle_exception(e, show_to_user=False)
        
        # 检查日志文件
        if log_file.exists():
            print(f"✅ 错误日志文件已创建: {log_file}")
            
            # 检查文件内容
            import json
            with open(log_file, 'r', encoding='utf-8') as f:
                records = json.load(f)
            
            if records:
                print(f"✅ 日志文件包含 {len(records)} 条记录")
                latest_record = records[-1]
                print(f"   最新记录: {latest_record['level']} - {latest_record['message']}")
            else:
                print("⚠️ 日志文件为空")
        else:
            print("⚠️ 错误日志文件未创建")
        
        return True
        
    except Exception as e:
        print(f"❌ 错误日志测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("🎯 增强错误处理系统测试")
    print("=" * 50)
    
    test_results = []
    
    # 运行所有测试
    test_results.append(test_basic_error_handling())
    test_results.append(test_error_decorators())
    test_results.append(test_error_statistics())
    test_results.append(test_error_suppression())
    test_results.append(test_error_recovery())
    test_results.append(test_error_integration())
    test_results.append(test_error_logging())
    
    # 汇总结果
    print("\n" + "=" * 50)
    print("📊 测试结果汇总:")
    
    passed = sum(test_results)
    total = len(test_results)
    
    print(f"✅ 通过: {passed}/{total}")
    print(f"❌ 失败: {total - passed}/{total}")
    
    if passed == total:
        print("🎉 所有测试通过！增强错误处理系统工作正常。")
        
        # 显示最终统计
        final_stats = enhanced_error_handler.get_error_statistics()
        print(f"\n📈 最终错误统计:")
        print(f"   总错误数: {final_stats['total_errors']}")
        print(f"   解决率: {final_stats['resolution_rate']:.2%}")
        print(f"   抑制的错误类型: {final_stats['suppressed_error_types']}")
        
        return True
    else:
        print("⚠️ 部分测试失败，请检查相关功能。")
        return False

if __name__ == "__main__":
    # 运行测试
    result = asyncio.run(main())
    sys.exit(0 if result else 1)
