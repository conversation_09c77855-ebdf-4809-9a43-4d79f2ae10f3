#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试场景清理功能和自动提取禁用
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.utils.character_scene_manager import CharacterSceneManager

def test_scene_cleanup():
    """测试场景清理功能"""

    # 创建测试用的角色场景管理器
    project_root = "output/天下无双"  # 使用现有项目进行测试

    if not os.path.exists(project_root):
        print(f"项目目录不存在: {project_root}")
        return

    cs_manager = CharacterSceneManager(project_root)

    print("=== 清理前的场景数据 ===")
    all_scenes_before = cs_manager._load_json(cs_manager.scenes_file).get("scenes", {})
    print(f"总场景数量: {len(all_scenes_before)}")

    for scene_id, scene_data in all_scenes_before.items():
        scene_name = scene_data.get('name', '未命名')
        print(f"- {scene_id}: {scene_name}")

    print("\n=== 执行清理 ===")
    cleaned_count = cs_manager.clean_auto_generated_scenes()
    print(f"清理了 {cleaned_count} 个临时场景")

    print("\n=== 清理后的场景数据 ===")
    all_scenes_after = cs_manager.get_all_scenes()
    print(f"剩余场景数量: {len(all_scenes_after)}")

    for scene_id, scene_data in all_scenes_after.items():
        scene_name = scene_data.get('name', '未命名')
        print(f"- {scene_id}: {scene_name}")

    print("\n=== 测试自动提取功能（应该正常工作） ===")
    result = cs_manager.auto_extract_and_save("桓温是一位将军，他在军营中思考战略。军营位于山谷中，夜晚时分灯火通明。")
    print(f"自动提取结果: {result}")

    print("\n=== 测试场景提取功能（应该正常工作） ===")
    scenes = cs_manager.extract_scenes_from_text("桓温在军营中，军营是一个重要的场景。")
    print(f"场景提取结果: {len(scenes)} 个场景")
    for scene in scenes:
        print(f"  - {scene.get('name', '未命名')}")

    print("\n=== 测试LLM场景提取功能（应该正常工作） ===")
    llm_scenes = cs_manager._extract_scenes_with_llm("桓温在宫殿中接见大臣。")
    print(f"LLM场景提取结果: {len(llm_scenes)} 个场景")
    for scene in llm_scenes:
        print(f"  - {scene.get('name', '未命名')}")

if __name__ == "__main__":
    test_scene_cleanup()
