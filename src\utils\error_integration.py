#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
错误处理集成模块
将增强的错误处理系统集成到现有的AI视频生成器中
"""

import sys
import functools
from typing import Any, Callable, Optional

from src.utils.enhanced_error_handler import (
    enhanced_error_handler, handle_errors, handle_async_errors,
    ErrorDomain, ErrorLevel, ErrorContext, handle_exception
)
from src.utils.logger import logger

class ErrorIntegration:
    """错误处理集成器"""
    
    def __init__(self):
        self.is_integrated = False
        self.original_excepthook = None
    
    def integrate_global_error_handling(self):
        """集成全局错误处理"""
        if self.is_integrated:
            return
        
        # 保存原始的异常钩子
        self.original_excepthook = sys.excepthook
        
        # 设置新的异常钩子
        sys.excepthook = self._global_exception_handler
        
        self.is_integrated = True
        logger.info("全局错误处理已集成")
    
    def _global_exception_handler(self, exc_type, exc_value, exc_traceback):
        """全局异常处理器"""
        try:
            # 创建错误上下文
            context = ErrorContext(
                module="global",
                function="main",
                user_action="程序运行"
            )
            
            # 处理异常
            enhanced_error_handler.handle_exception(
                exc_value, context, "程序运行", show_to_user=True
            )
            
        except Exception as e:
            logger.critical(f"全局异常处理器失败: {e}")
        
        # 调用原始异常钩子
        if self.original_excepthook:
            self.original_excepthook(exc_type, exc_value, exc_traceback)
    
    def patch_existing_modules(self):
        """为现有模块添加错误处理"""
        # 为视频合成处理器添加错误处理
        self._patch_video_synthesis_processor()
        
        # 为图像处理器添加错误处理
        self._patch_image_processor()
        
        # 为文本处理器添加错误处理
        self._patch_text_processor()
        
        # 为API服务添加错误处理
        self._patch_api_services()
        
        logger.info("现有模块错误处理补丁已应用")
    
    def _patch_video_synthesis_processor(self):
        """为视频合成处理器添加错误处理"""
        try:
            from src.processors.video_synthesis_processor import VideoSynthesisProcessor
            
            # 装饰关键方法
            original_synthesize = VideoSynthesisProcessor.synthesize_video
            VideoSynthesisProcessor.synthesize_video = handle_async_errors(
                domain=ErrorDomain.VIDEO,
                user_action="合成视频",
                show_to_user=True
            )(original_synthesize)
            
            original_add_video = VideoSynthesisProcessor.add_video_segment
            VideoSynthesisProcessor.add_video_segment = handle_errors(
                domain=ErrorDomain.VIDEO,
                user_action="添加视频片段",
                show_to_user=True
            )(original_add_video)
            
            original_add_audio = VideoSynthesisProcessor.add_audio_segment
            VideoSynthesisProcessor.add_audio_segment = handle_errors(
                domain=ErrorDomain.AUDIO,
                user_action="添加音频片段",
                show_to_user=True
            )(original_add_audio)
            
            logger.info("视频合成处理器错误处理已集成")
            
        except ImportError as e:
            logger.warning(f"无法为视频合成处理器添加错误处理: {e}")
    
    def _patch_image_processor(self):
        """为图像处理器添加错误处理"""
        try:
            from src.processors.image_processor import ImageProcessor
            
            # 装饰关键方法
            original_generate = ImageProcessor.generate_image
            ImageProcessor.generate_image = handle_async_errors(
                domain=ErrorDomain.IMAGE,
                user_action="生成图像",
                show_to_user=True
            )(original_generate)
            
            original_process = ImageProcessor.process_image
            ImageProcessor.process_image = handle_errors(
                domain=ErrorDomain.IMAGE,
                user_action="处理图像",
                show_to_user=True
            )(original_process)
            
            logger.info("图像处理器错误处理已集成")
            
        except ImportError as e:
            logger.warning(f"无法为图像处理器添加错误处理: {e}")
    
    def _patch_text_processor(self):
        """为文本处理器添加错误处理"""
        try:
            from src.processors.text_processor import TextProcessor
            
            # 装饰关键方法
            original_process = TextProcessor.process_text
            TextProcessor.process_text = handle_async_errors(
                domain=ErrorDomain.AI_SERVICE,
                user_action="处理文本",
                show_to_user=True
            )(original_process)
            
            logger.info("文本处理器错误处理已集成")
            
        except ImportError as e:
            logger.warning(f"无法为文本处理器添加错误处理: {e}")
    
    def _patch_api_services(self):
        """为API服务添加错误处理"""
        try:
            # LLM服务
            from src.services.llm_service import LLMService
            
            original_call = LLMService.call_api
            LLMService.call_api = handle_async_errors(
                domain=ErrorDomain.API,
                user_action="调用LLM API",
                show_to_user=True
            )(original_call)
            
            logger.info("LLM服务错误处理已集成")
            
        except ImportError as e:
            logger.warning(f"无法为LLM服务添加错误处理: {e}")
        
        try:
            # 图像生成服务
            from src.services.image_service import ImageService
            
            original_generate = ImageService.generate_image
            ImageService.generate_image = handle_async_errors(
                domain=ErrorDomain.AI_SERVICE,
                user_action="生成图像",
                show_to_user=True
            )(original_generate)
            
            logger.info("图像生成服务错误处理已集成")
            
        except ImportError as e:
            logger.warning(f"无法为图像生成服务添加错误处理: {e}")

# 创建错误处理装饰器的便捷别名
def video_error_handler(func):
    """视频处理错误装饰器"""
    return handle_errors(
        domain=ErrorDomain.VIDEO,
        user_action="视频处理操作",
        show_to_user=True
    )(func)

def audio_error_handler(func):
    """音频处理错误装饰器"""
    return handle_errors(
        domain=ErrorDomain.AUDIO,
        user_action="音频处理操作",
        show_to_user=True
    )(func)

def image_error_handler(func):
    """图像处理错误装饰器"""
    return handle_errors(
        domain=ErrorDomain.IMAGE,
        user_action="图像处理操作",
        show_to_user=True
    )(func)

def api_error_handler(func):
    """API调用错误装饰器"""
    return handle_async_errors(
        domain=ErrorDomain.API,
        user_action="API调用",
        show_to_user=True
    )(func)

def file_error_handler(func):
    """文件操作错误装饰器"""
    return handle_errors(
        domain=ErrorDomain.FILE_IO,
        user_action="文件操作",
        show_to_user=True
    )(func)

def ui_error_handler(func):
    """UI操作错误装饰器"""
    return handle_errors(
        domain=ErrorDomain.UI,
        user_action="界面操作",
        show_to_user=True
    )(func)

# 全局错误集成实例
error_integration = ErrorIntegration()

def initialize_error_handling():
    """初始化错误处理系统"""
    try:
        # 集成全局错误处理
        error_integration.integrate_global_error_handling()
        
        # 为现有模块添加错误处理
        error_integration.patch_existing_modules()
        
        logger.info("增强错误处理系统初始化完成")
        return True
        
    except Exception as e:
        logger.error(f"错误处理系统初始化失败: {e}")
        return False

# 错误报告函数
def report_error(exception: Exception, user_action: str = "",
                context_info: dict = None) -> str:
    """报告错误并返回错误ID"""
    from src.utils.enhanced_error_handler import ErrorContext as EErrorContext

    context = EErrorContext(
        user_action=user_action,
        system_state=context_info or {}
    )

    error_record = handle_exception(exception, context, user_action, True)
    return error_record.id

def get_error_summary() -> dict:
    """获取错误摘要"""
    stats = enhanced_error_handler.get_error_statistics()
    recent_errors = enhanced_error_handler.get_recent_errors(24)
    
    return {
        'total_errors_24h': len(recent_errors),
        'critical_errors_24h': len([e for e in recent_errors if e.level == ErrorLevel.CRITICAL]),
        'resolution_rate': stats['resolution_rate'],
        'most_common_domain': max(stats['domain_statistics'].items(), 
                                key=lambda x: x[1]['count'])[0] if stats['domain_statistics'] else 'none',
        'suppressed_types': stats['suppressed_error_types']
    }

# 上下文管理器
class ErrorContextManager:
    """错误上下文管理器"""

    def __init__(self, user_action: str, domain: ErrorDomain = ErrorDomain.UNKNOWN):
        self.user_action = user_action
        self.domain = domain

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_value, traceback):
        if exc_value:
            from src.utils.enhanced_error_handler import ErrorContext as EErrorContext
            context = EErrorContext(
                user_action=self.user_action
            )
            handle_exception(exc_value, context, self.user_action, True)
        return False  # 不抑制异常
