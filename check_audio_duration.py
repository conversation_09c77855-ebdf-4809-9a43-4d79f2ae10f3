#!/usr/bin/env python3
"""检查音频文件时长的脚本"""

import os
import sys

def get_audio_duration(audio_path):
    """获取音频文件时长"""
    try:
        # 检查文件是否存在
        if not audio_path or not os.path.exists(audio_path):
            return 0.0

        # 方法1：使用mutagen
        try:
            from mutagen import File
            audio_file = File(audio_path)
            if audio_file is not None and hasattr(audio_file, 'info') and hasattr(audio_file.info, 'length'):
                duration = float(audio_file.info.length)
                return duration
        except Exception as e:
            print(f"mutagen获取音频时长失败: {e}")

        # 方法2：使用wave模块（仅支持wav文件）
        try:
            import wave
            if audio_path.lower().endswith('.wav'):
                with wave.open(audio_path, 'r') as wav_file:
                    frames = wav_file.getnframes()
                    rate = wav_file.getframerate()
                    duration = frames / float(rate)
                    return duration
        except Exception as e:
            print(f"wave获取音频时长失败: {e}")

        # 如果所有方法都失败，返回默认值
        print(f"无法获取音频时长，使用默认值5秒: {audio_path}")
        return 5.0

    except Exception as e:
        print(f"获取音频时长失败: {e}")
        return 5.0

def main():
    # 检查第27行镜头5的音频文件
    audio_file = r"D:\AI_Video_Generator\output\天下无双\audio\edge_tts\segment_027_镜头5.mp3"
    
    print(f"检查音频文件: {audio_file}")
    print(f"文件是否存在: {os.path.exists(audio_file)}")
    
    if os.path.exists(audio_file):
        file_size = os.path.getsize(audio_file)
        print(f"文件大小: {file_size} bytes")
        
        duration = get_audio_duration(audio_file)
        print(f"音频时长: {duration:.1f} 秒")
    
    # 检查其他几个音频文件作为对比
    test_files = [
        r"D:\AI_Video_Generator\output\天下无双\audio\edge_tts\segment_001_镜头1.mp3",
        r"D:\AI_Video_Generator\output\天下无双\audio\edge_tts\segment_002_镜头2.mp3",
        r"D:\AI_Video_Generator\output\天下无双\audio\edge_tts\segment_026_镜头4.mp3",
    ]
    
    print("\n对比其他音频文件:")
    for file_path in test_files:
        if os.path.exists(file_path):
            duration = get_audio_duration(file_path)
            file_size = os.path.getsize(file_path)
            print(f"{os.path.basename(file_path)}: {duration:.1f}s ({file_size} bytes)")

if __name__ == "__main__":
    main()
