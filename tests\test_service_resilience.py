#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI服务弹性系统测试
"""

import os
import sys
import time
import asyncio
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.core.service_resilience import (
    service_monitor, service_orchestrator, degradation_manager,
    ServiceConfig, RetryConfig, CircuitBreakerConfig, ServiceStatus, FailureType
)
from src.core.resilience_integration import (
    initialize_service_resilience, get_service_health_summary
)
from src.utils.logger import logger

def test_circuit_breaker():
    """测试熔断器功能"""
    print("🧪 测试熔断器功能...")
    
    try:
        # 创建测试服务配置
        config = ServiceConfig(
            service_name="test_service",
            providers=["test_provider"],
            primary_provider="test_provider",
            circuit_breaker=CircuitBreakerConfig(failure_threshold=3, recovery_timeout=5.0)
        )
        
        service_monitor.register_service(config)
        
        # 模拟连续失败
        for i in range(5):
            service_monitor.record_request("test_service", False, 1.0, FailureType.CONNECTION_ERROR)
            status = service_monitor.get_service_status("test_service")
            print(f"  失败 {i+1} 次后状态: {status.value}")
        
        # 检查熔断器是否开启
        can_call = service_monitor.can_call_service("test_service")
        print(f"  熔断器状态 - 可调用: {can_call}")
        
        if not can_call:
            print("✅ 熔断器正常工作")
        else:
            print("❌ 熔断器未正常工作")
            return False
        
        # 等待恢复时间
        print("  等待恢复时间...")
        time.sleep(6)
        
        # 检查是否可以恢复
        can_call_after_wait = service_monitor.can_call_service("test_service")
        print(f"  等待后可调用: {can_call_after_wait}")
        
        if can_call_after_wait:
            print("✅ 熔断器恢复机制正常")
        else:
            print("⚠️ 熔断器恢复机制可能有问题")
        
        return True
        
    except Exception as e:
        print(f"❌ 熔断器测试失败: {e}")
        return False

async def test_retry_mechanism():
    """测试重试机制"""
    print("\n🧪 测试重试机制...")
    
    try:
        # 创建测试函数
        call_count = 0
        
        async def failing_function():
            nonlocal call_count
            call_count += 1
            if call_count < 3:
                raise ConnectionError(f"模拟失败 {call_count}")
            return f"成功调用，总共尝试了 {call_count} 次"
        
        # 注册重试配置
        retry_config = RetryConfig(max_retries=3, base_delay=0.1, backoff_factor=1.0)
        service_orchestrator.retry_manager.register_retry_config("test_retry_service", retry_config)
        
        # 注册服务
        config = ServiceConfig(
            service_name="test_retry_service",
            providers=["test_provider"],
            primary_provider="test_provider"
        )
        service_monitor.register_service(config)
        
        # 执行带重试的函数
        result = await service_orchestrator.retry_manager.execute_with_retry(
            "test_retry_service", failing_function
        )
        
        print(f"  重试结果: {result}")
        print(f"  总调用次数: {call_count}")
        
        if call_count == 3 and "成功调用" in result:
            print("✅ 重试机制正常工作")
            return True
        else:
            print("❌ 重试机制未正常工作")
            return False
        
    except Exception as e:
        print(f"❌ 重试机制测试失败: {e}")
        return False

async def test_degradation_strategy():
    """测试降级策略"""
    print("\n🧪 测试降级策略...")
    
    try:
        # 注册降级策略
        async def test_degradation_strategy(*args, **kwargs):
            return "降级响应：服务暂时不可用"
        
        degradation_manager.register_degradation_strategy("test_degradation_service", test_degradation_strategy)
        
        # 注册服务并设置为降级状态
        config = ServiceConfig(
            service_name="test_degradation_service",
            providers=["test_provider"],
            primary_provider="test_provider"
        )
        service_monitor.register_service(config)
        
        # 模拟服务降级
        for i in range(3):
            service_monitor.record_request("test_degradation_service", False, 1.0, FailureType.SERVER_ERROR)
        
        # 测试降级执行
        async def normal_function():
            raise Exception("正常服务不可用")
        
        result = await degradation_manager.execute_with_degradation(
            "test_degradation_service", normal_function
        )
        
        print(f"  降级结果: {result}")
        
        if "降级响应" in str(result):
            print("✅ 降级策略正常工作")
            return True
        else:
            print("❌ 降级策略未正常工作")
            return False
        
    except Exception as e:
        print(f"❌ 降级策略测试失败: {e}")
        return False

def test_service_metrics():
    """测试服务指标"""
    print("\n🧪 测试服务指标...")
    
    try:
        # 创建测试服务
        config = ServiceConfig(
            service_name="test_metrics_service",
            providers=["test_provider"],
            primary_provider="test_provider"
        )
        service_monitor.register_service(config)
        
        # 模拟一些请求
        service_monitor.record_request("test_metrics_service", True, 1.0)
        service_monitor.record_request("test_metrics_service", True, 2.0)
        service_monitor.record_request("test_metrics_service", False, 3.0, FailureType.TIMEOUT)
        service_monitor.record_request("test_metrics_service", True, 1.5)
        
        # 获取指标
        metrics = service_monitor.get_service_metrics("test_metrics_service")
        
        print(f"  总请求数: {metrics.total_requests}")
        print(f"  成功请求数: {metrics.successful_requests}")
        print(f"  失败请求数: {metrics.failed_requests}")
        print(f"  成功率: {metrics.success_rate:.2%}")
        print(f"  平均响应时间: {metrics.average_response_time:.2f}s")
        
        # 验证指标
        expected_total = 4
        expected_success = 3
        expected_failure = 1
        expected_success_rate = 0.75
        
        if (metrics.total_requests == expected_total and
            metrics.successful_requests == expected_success and
            metrics.failed_requests == expected_failure and
            abs(metrics.success_rate - expected_success_rate) < 0.01):
            print("✅ 服务指标计算正确")
            return True
        else:
            print("❌ 服务指标计算错误")
            return False
        
    except Exception as e:
        print(f"❌ 服务指标测试失败: {e}")
        return False

def test_service_integration():
    """测试服务集成"""
    print("\n🧪 测试服务集成...")
    
    try:
        # 初始化弹性系统
        success = initialize_service_resilience()
        
        if success:
            print("✅ 服务弹性系统初始化成功")
        else:
            print("⚠️ 服务弹性系统初始化失败")
        
        # 获取健康摘要
        summary = get_service_health_summary()
        
        print("📊 服务健康摘要:")
        for service_name, info in summary.items():
            print(f"  {service_name}:")
            print(f"    状态: {info['status']}")
            print(f"    成功率: {info['success_rate']:.2%}")
            print(f"    总请求数: {info['total_requests']}")
            print(f"    推荐提供商: {info['recommended_provider']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 服务集成测试失败: {e}")
        return False

def test_provider_recommendation():
    """测试提供商推荐"""
    print("\n🧪 测试提供商推荐...")
    
    try:
        # 创建有多个提供商的服务
        config = ServiceConfig(
            service_name="test_provider_service",
            providers=["provider_a", "provider_b", "provider_c"],
            primary_provider="provider_a",
            fallback_providers=["provider_b", "provider_c"]
        )
        service_monitor.register_service(config)
        
        # 正常情况下应该推荐主要提供商
        recommended = service_monitor.get_recommended_provider("test_provider_service")
        print(f"  正常状态推荐提供商: {recommended}")
        
        if recommended == "provider_a":
            print("✅ 正常状态提供商推荐正确")
        else:
            print("❌ 正常状态提供商推荐错误")
            return False
        
        # 模拟主要提供商故障
        for i in range(6):  # 超过失败阈值
            service_monitor.record_request("test_provider_service", False, 1.0, FailureType.CONNECTION_ERROR)
        
        # 检查是否切换到备用提供商
        recommended_after_failure = service_monitor.get_recommended_provider("test_provider_service")
        print(f"  故障后推荐提供商: {recommended_after_failure}")
        
        if recommended_after_failure in ["provider_b", "provider_c"]:
            print("✅ 故障切换提供商推荐正确")
            return True
        else:
            print("❌ 故障切换提供商推荐错误")
            return False
        
    except Exception as e:
        print(f"❌ 提供商推荐测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("🛡️ AI服务弹性系统测试")
    print("=" * 50)
    
    test_results = []
    
    # 运行所有测试
    test_results.append(test_circuit_breaker())
    test_results.append(await test_retry_mechanism())
    test_results.append(await test_degradation_strategy())
    test_results.append(test_service_metrics())
    test_results.append(test_service_integration())
    test_results.append(test_provider_recommendation())
    
    # 汇总结果
    print("\n" + "=" * 50)
    print("📊 测试结果汇总:")
    
    passed = sum(test_results)
    total = len(test_results)
    
    print(f"✅ 通过: {passed}/{total}")
    print(f"❌ 失败: {total - passed}/{total}")
    
    if passed == total:
        print("🎉 所有测试通过！AI服务弹性系统工作正常。")
        
        # 显示最终服务状态
        print(f"\n🔍 最终服务状态:")
        summary = get_service_health_summary()
        for service_name, info in summary.items():
            print(f"  {service_name}: {info['status']} (成功率: {info['success_rate']:.2%})")
        
        return True
    else:
        print("⚠️ 部分测试失败，请检查相关功能。")
        return False

if __name__ == "__main__":
    # 运行测试
    result = asyncio.run(main())
    sys.exit(0 if result else 1)
