# 分镜图像生成失败检测机制

## 概述

为了解决用户在分镜图像生成过程中遇到的网络超时、HTTP错误等问题，我们在分镜图像生成系统中实现了智能失败检测机制。该机制能够自动检测失败的图像生成，并提供便捷的重试功能和智能跳过已生成图片的选项。

## 功能特性

### 1. 图像生成失败检测
- **自动检测**：系统会自动检测每个镜头的图像生成是否成功
- **失败判断标准**：
  - HTTP错误（502、503、500、404等）
  - 网络超时和连接问题
  - API调用失败
  - 服务器错误
  - 其他生成异常

### 2. 已生成图片检测
- **智能识别**：自动检测哪些镜头已经生成了图片
- **多格式支持**：支持PNG、JPG、JPEG、WebP等格式
- **多目录检查**：检查各个引擎目录（pollinations、comfyui、stable_diffusion）
- **灵活命名**：支持多种文件命名格式

### 3. 智能跳过功能
- **可选跳过**：用户可选择是否跳过已生成图片的镜头
- **批量优化**：大幅减少重复生成，提高效率
- **状态提示**：显示跳过的镜头数量和将要生成的镜头数量

### 4. 失败重试机制
- **选择性重试**：用户可以选择需要重试的特定镜头
- **批量重试**：支持一键重试所有失败项目
- **智能选项**：提供延迟重试和引擎切换选项

## 界面功能

### 1. 生成控制面板
```
[生成选中项] [生成全部] [停止生成]
☑ 跳过已生成图片的镜头    [检测已生成]
```

- **跳过已生成图片的镜头**：勾选后批量生图时自动跳过已有图片的镜头
- **检测已生成**：手动检测当前项目中哪些镜头已经生成了图片

### 2. 失败检测对话框
当检测到图像生成失败时，系统会自动弹出失败检测对话框，包含：

- **失败统计**：显示总失败数量、网络错误、超时错误等分类统计
- **失败列表**：列出所有失败的镜头，显示错误类型和详细信息
- **重试选项**：
  - ☑ 重试时增加延迟（减少网络压力）
  - ☐ 尝试切换到备用引擎
- **操作按钮**：
  - [重试选中项目]：只重试选中的失败项目
  - [重试全部]：重试所有失败项目
  - [关闭]：关闭对话框

## 使用流程

### 1. 正常批量生图
1. 在分镜图像生成标签页中选择要生成的镜头
2. 根据需要勾选"跳过已生成图片的镜头"
3. 点击"生成选中项"或"生成全部"
4. 系统自动开始生成，如果启用跳过功能会先显示跳过提示

### 2. 失败检测和重试
1. 批量生成完成后，如果有失败项目会自动弹出失败检测对话框
2. 查看失败统计和具体的失败原因
3. 选择需要重试的项目（默认全选）
4. 根据需要调整重试选项
5. 点击重试按钮开始重试

### 3. 手动检测已生成图片
1. 点击"检测已生成"按钮
2. 系统会扫描项目目录中的图片文件
3. 显示检测结果：总镜头数、已生成数、未生成数

## 技术实现

### 1. 失败检测逻辑
```python
def _is_image_generation_failed(self, error_message):
    failure_patterns = [
        'http 502', 'http 503', 'http 500', 'http 404',
        'timeout', '超时', 'timed out',
        'connection', '连接', 'network error', '网络错误',
        'failed to generate', '生成失败',
        'api error', 'api错误', 'api调用失败',
        'invalid response', '无效响应',
        'server error', '服务器错误'
    ]
    return any(pattern in error_message.lower() for pattern in failure_patterns)
```

### 2. 已生成图片检测
```python
def _has_generated_image(self, item_index, item_data):
    # 检查多种文件名格式
    possible_names = [
        f"shot_{item_index + 1}",
        f"scene_{item_data.get('scene', 1)}_shot_{item_data.get('sequence', item_index + 1)}",
        f"{item_data.get('sequence', item_index + 1)}",
        f"image_{item_index + 1}",
    ]
    
    # 检查多种图片格式和目录
    for name in possible_names:
        for ext in ['.png', '.jpg', '.jpeg', '.webp']:
            for engine_dir in ['pollinations', 'comfyui', 'stable_diffusion']:
                if image_path.exists():
                    return True
    return False
```

### 3. 智能跳过逻辑
```python
def start_batch_generation(self, items):
    if self.skip_existing_cb.isChecked():
        filtered_items = []
        skipped_count = 0
        
        for i, item in enumerate(items):
            original_index = self.storyboard_data.index(item)
            if self._has_generated_image(original_index, item):
                skipped_count += 1
            else:
                filtered_items.append(item)
        
        if skipped_count > 0:
            QMessageBox.information(self, "跳过提示", 
                f"已跳过{skipped_count}个已生成图片的镜头")
```

## 错误类型识别

### 1. 网络相关错误
- `HTTP 502`、`HTTP 503`、`HTTP 500`、`HTTP 404`
- `connection`、`连接`、`network error`、`网络错误`

### 2. 超时相关错误
- `timeout`、`超时`、`timed out`

### 3. API相关错误
- `api error`、`api错误`、`api调用失败`
- `invalid response`、`无效响应`

### 4. 服务器相关错误
- `server error`、`服务器错误`
- `failed to generate`、`生成失败`

## 用户体验优化

### 1. 智能提示
- 跳过已生成图片时显示具体数量
- 失败检测对话框显示详细统计
- 重试完成后显示成功/失败统计

### 2. 灵活选择
- 可选择性重试特定镜头
- 可调整重试选项（延迟、引擎切换）
- 可选择是否跳过已生成图片

### 3. 状态保持
- 重试成功后自动更新镜头状态
- 保持用户的工作进度
- 无缝集成到现有工作流程

## 注意事项

### 1. 网络环境
- 确保网络连接稳定
- 避免在网络高峰期进行大量生成
- 考虑启用重试延迟选项

### 2. 存储空间
- 确保项目目录有足够的存储空间
- 定期清理不需要的图片文件
- 注意不同引擎生成的图片可能存储在不同目录

### 3. 性能考虑
- 大量镜头建议分批生成
- 启用跳过已生成图片功能可显著提高效率
- 重试时建议启用延迟选项

## 故障排除

### 1. 检测误报
- 检查网络连接状态
- 验证API配置是否正确
- 查看详细的错误日志

### 2. 跳过功能异常
- 确认项目目录结构正确
- 检查图片文件是否存在
- 验证文件权限设置

### 3. 重试失败
- 检查根本问题是否已解决
- 尝试手动生成单个镜头
- 考虑切换到其他生成引擎

## 总结

图像生成失败检测机制显著提升了分镜图像生成的可靠性和用户体验。通过智能检测、自动跳过和便捷重试，用户可以更高效地完成大量镜头的图像生成工作，同时减少因网络问题导致的重复劳动。
