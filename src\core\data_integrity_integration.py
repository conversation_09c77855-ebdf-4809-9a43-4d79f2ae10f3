#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据完整性集成模块
将数据完整性管理器集成到现有系统中
"""

import os
import functools
from typing import Any, Callable, Optional, Dict
from pathlib import Path

from src.core.data_integrity_manager import (
    data_integrity_manager, DataIntegrityLevel, BackupType
)
from src.utils.logger import logger

class DataIntegrityIntegration:
    """数据完整性集成器"""
    
    def __init__(self):
        self.is_integrated = False
        self.monitored_operations = set()
    
    def integrate_data_protection(self):
        """集成数据保护功能"""
        if self.is_integrated:
            return
        
        try:
            # 集成项目管理器
            self._integrate_project_manager()
            
            # 集成配置管理器
            self._integrate_config_manager()
            
            # 集成文件操作
            self._integrate_file_operations()
            
            # 设置关键文件监控
            self._setup_critical_file_monitoring()
            
            self.is_integrated = True
            logger.info("数据完整性系统集成完成")
            
        except Exception as e:
            logger.error(f"数据完整性系统集成失败: {e}")
            raise
    
    def _integrate_project_manager(self):
        """集成项目管理器"""
        try:
            from src.core.project_manager import ProjectManager
            
            # 保存原始方法
            original_save_project = ProjectManager.save_project
            original_load_project = ProjectManager.load_project
            
            # 创建带数据保护的包装器
            def protected_save_project(self, project_data: Dict[str, Any], file_path: str = None):
                """带数据保护的项目保存"""
                # 创建事务性备份
                with data_integrity_manager.transaction("保存项目"):
                    result = original_save_project(self, project_data, file_path)
                    
                    # 更新文件校验和
                    if file_path and os.path.exists(file_path):
                        data_integrity_manager.update_file_checksum(file_path)
                    
                    return result
            
            def protected_load_project(self, file_path: str):
                """带完整性检查的项目加载"""
                # 验证文件完整性
                if not data_integrity_manager.verify_file_integrity(file_path):
                    logger.warning(f"项目文件完整性检查失败: {file_path}")
                    
                    # 尝试从备份恢复
                    backup_id = data_integrity_manager._find_latest_backup_for_file(file_path)
                    if backup_id:
                        logger.info(f"尝试从备份恢复项目文件: {backup_id}")
                        if data_integrity_manager.restore_backup(backup_id, file_path):
                            logger.info("项目文件已从备份恢复")
                        else:
                            logger.error("从备份恢复项目文件失败")
                
                return original_load_project(self, file_path)
            
            # 替换方法
            ProjectManager.save_project = protected_save_project
            ProjectManager.load_project = protected_load_project
            
            logger.info("项目管理器数据保护已集成")
            
        except ImportError as e:
            logger.warning(f"无法集成项目管理器数据保护: {e}")
    
    def _integrate_config_manager(self):
        """集成配置管理器"""
        try:
            from src.core.config_manager import ConfigManager
            
            # 保存原始方法
            original_save_config = ConfigManager.save_config
            original_load_config = ConfigManager.load_config
            
            # 创建带数据保护的包装器
            def protected_save_config(self, config_data: Dict[str, Any], config_file: str = None):
                """带数据保护的配置保存"""
                with data_integrity_manager.transaction("保存配置"):
                    result = original_save_config(self, config_data, config_file)
                    
                    # 更新文件校验和
                    if config_file and os.path.exists(config_file):
                        data_integrity_manager.update_file_checksum(config_file)
                    
                    return result
            
            def protected_load_config(self, config_file: str):
                """带完整性检查的配置加载"""
                # 验证文件完整性
                if not data_integrity_manager.verify_file_integrity(config_file):
                    logger.warning(f"配置文件完整性检查失败: {config_file}")
                
                return original_load_config(self, config_file)
            
            # 替换方法
            ConfigManager.save_config = protected_save_config
            ConfigManager.load_config = protected_load_config
            
            logger.info("配置管理器数据保护已集成")
            
        except ImportError as e:
            logger.warning(f"无法集成配置管理器数据保护: {e}")
    
    def _integrate_file_operations(self):
        """集成文件操作"""
        try:
            # 监控重要的文件操作
            import shutil
            
            # 保存原始函数
            original_copy2 = shutil.copy2
            original_move = shutil.move
            
            def protected_copy2(src, dst, **kwargs):
                """带校验和更新的文件复制"""
                result = original_copy2(src, dst, **kwargs)
                
                # 更新目标文件校验和
                if os.path.exists(dst):
                    data_integrity_manager.update_file_checksum(dst)
                
                return result
            
            def protected_move(src, dst, **kwargs):
                """带校验和更新的文件移动"""
                # 如果源文件有校验和记录，先删除
                if os.path.exists(src):
                    abs_src = str(Path(src).resolve())
                    if abs_src in data_integrity_manager.checksums:
                        del data_integrity_manager.checksums[abs_src]
                
                result = original_move(src, dst, **kwargs)
                
                # 更新目标文件校验和
                if os.path.exists(dst):
                    data_integrity_manager.update_file_checksum(dst)
                
                return result
            
            # 替换函数
            shutil.copy2 = protected_copy2
            shutil.move = protected_move
            
            logger.info("文件操作数据保护已集成")
            
        except Exception as e:
            logger.warning(f"集成文件操作数据保护失败: {e}")
    
    def _setup_critical_file_monitoring(self):
        """设置关键文件监控"""
        try:
            # 初始化关键文件的校验和
            critical_files = [
                "project.json",
                "config/app_config.json",
                "config/service_config.json"
            ]
            
            for file_pattern in critical_files:
                files = data_integrity_manager._expand_file_pattern(file_pattern)
                for file_path in files:
                    if os.path.exists(file_path):
                        data_integrity_manager.update_file_checksum(file_path)
                        logger.info(f"已初始化关键文件监控: {file_path}")
            
            # 创建初始备份
            for file_pattern in critical_files:
                files = data_integrity_manager._expand_file_pattern(file_pattern)
                for file_path in files:
                    if os.path.exists(file_path):
                        backup_id = data_integrity_manager.create_backup(
                            file_path,
                            BackupType.MANUAL,
                            "初始化备份"
                        )
                        if backup_id:
                            logger.info(f"已创建初始备份: {file_path} -> {backup_id}")
            
        except Exception as e:
            logger.error(f"设置关键文件监控失败: {e}")

# 数据保护装饰器
def with_data_protection(backup_description: str = "", critical: bool = False):
    """数据保护装饰器"""
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            if critical:
                # 关键操作使用事务保护
                with data_integrity_manager.transaction(backup_description or f"执行 {func.__name__}"):
                    return func(*args, **kwargs)
            else:
                # 普通操作直接执行
                return func(*args, **kwargs)
        return wrapper
    return decorator

def with_integrity_check(file_path_arg: str = None):
    """完整性检查装饰器"""
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # 获取文件路径
            if file_path_arg and file_path_arg in kwargs:
                file_path = kwargs[file_path_arg]
            elif len(args) > 0 and isinstance(args[0], str):
                file_path = args[0]
            else:
                file_path = None
            
            # 执行完整性检查
            if file_path and os.path.exists(file_path):
                if not data_integrity_manager.verify_file_integrity(file_path):
                    logger.warning(f"文件完整性检查失败: {file_path}")
            
            return func(*args, **kwargs)
        return wrapper
    return decorator

def auto_backup(backup_type: BackupType = BackupType.AUTO):
    """自动备份装饰器"""
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            result = func(*args, **kwargs)
            
            # 执行后创建备份
            if hasattr(args[0], '__dict__') and 'file_path' in args[0].__dict__:
                file_path = args[0].file_path
                if os.path.exists(file_path):
                    data_integrity_manager.create_backup(
                        file_path,
                        backup_type,
                        f"自动备份 - {func.__name__}"
                    )
            
            return result
        return wrapper
    return decorator

# 全局集成实例
data_integrity_integration = DataIntegrityIntegration()

def initialize_data_integrity():
    """初始化数据完整性系统"""
    try:
        data_integrity_integration.integrate_data_protection()
        logger.info("数据完整性系统初始化完成")
        return True
    except Exception as e:
        logger.error(f"数据完整性系统初始化失败: {e}")
        return False

def create_manual_backup(file_path: str, description: str = "") -> Optional[str]:
    """创建手动备份"""
    return data_integrity_manager.create_backup(file_path, BackupType.MANUAL, description)

def restore_from_backup(backup_id: str, target_path: str = None) -> bool:
    """从备份恢复"""
    return data_integrity_manager.restore_backup(backup_id, target_path)

def check_data_integrity(level: DataIntegrityLevel = DataIntegrityLevel.STANDARD):
    """检查数据完整性"""
    return data_integrity_manager.perform_integrity_check(level)

def get_data_integrity_status() -> Dict[str, Any]:
    """获取数据完整性状态"""
    return data_integrity_manager.get_integrity_summary()

def list_backups() -> Dict[str, Any]:
    """列出所有备份"""
    backups = {}
    for backup_id, record in data_integrity_manager.backup_records.items():
        backups[backup_id] = {
            'backup_type': record.backup_type.value,
            'source_path': record.source_path,
            'created_time': record.created_time.isoformat(),
            'size_mb': record.size / (1024 * 1024),
            'description': record.description
        }
    return backups
