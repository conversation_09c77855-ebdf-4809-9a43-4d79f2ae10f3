#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试CogVideoX-Flash支持的FPS值
"""

import asyncio
import aiohttp
import json

# 智谱AI API配置
API_KEY = "ed7ffe9976bb4484ab16647564c0cb27.rI1BXVjDDDURMtJY"
BASE_URL = "https://open.bigmodel.cn/api/paas/v4"

async def test_fps_values():
    """测试不同的FPS值"""
    print("🎬 测试CogVideoX-Flash支持的FPS值...")
    
    headers = {
        'Authorization': f'Bearer {API_KEY}',
        'Content-Type': 'application/json'
    }
    
    # 测试不同的FPS值
    fps_tests = [8, 16, 24, 25, 30, 60]
    
    async with aiohttp.ClientSession() as session:
        for fps in fps_tests:
            print(f"\n📋 测试FPS: {fps}")
            
            test_data = {
                "model": "cogvideox-flash",
                "prompt": "一朵花在微风中摇摆",
                "fps": fps
            }
            
            try:
                url = f"{BASE_URL}/videos/generations"
                
                async with session.post(url, headers=headers, json=test_data) as response:
                    print(f"   状态码: {response.status}")
                    
                    if response.status == 200:
                        result = await response.json()
                        print(f"   ✅ FPS {fps} 支持")
                    else:
                        response_text = await response.text()
                        print(f"   ❌ FPS {fps} 不支持: {response_text[:100]}")
                        
                        # 检查是否是FPS错误
                        if "1214" in response_text or "fps" in response_text.lower():
                            print(f"   💡 确认FPS {fps} 不被支持")
                        
            except Exception as e:
                print(f"   ❌ 测试异常: {e}")
            
            await asyncio.sleep(1)

async def test_without_fps():
    """测试不传FPS参数"""
    print("\n🔍 测试不传FPS参数...")
    
    headers = {
        'Authorization': f'Bearer {API_KEY}',
        'Content-Type': 'application/json'
    }
    
    test_data = {
        "model": "cogvideox-flash",
        "prompt": "一朵花在微风中摇摆"
    }
    
    async with aiohttp.ClientSession() as session:
        try:
            url = f"{BASE_URL}/videos/generations"
            
            async with session.post(url, headers=headers, json=test_data) as response:
                print(f"状态码: {response.status}")
                
                if response.status == 200:
                    result = await response.json()
                    print(f"✅ 不传FPS参数成功")
                    print(f"响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
                else:
                    response_text = await response.text()
                    print(f"❌ 不传FPS参数失败: {response_text}")
                    
        except Exception as e:
            print(f"❌ 测试异常: {e}")

async def main():
    """主测试函数"""
    print("=" * 60)
    print("🧪 CogVideoX-Flash FPS值测试")
    print("=" * 60)
    
    # 测试不同FPS值
    await test_fps_values()
    
    # 测试不传FPS参数
    await test_without_fps()
    
    print("\n" + "=" * 60)
    print("📋 测试完成")
    print("=" * 60)

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n👋 测试已取消")
    except Exception as e:
        print(f"\n❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
