#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
翻译问题快速修复脚本
用于诊断和修复翻译服务问题
"""

import sys
import os

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def main():
    print("🔧 AI视频生成器 - 翻译问题修复工具")
    print("=" * 50)
    
    try:
        from src.utils.translation_status_checker import TranslationStatusChecker
        
        # 检查翻译服务状态
        checker = TranslationStatusChecker()
        results = checker.check_all_services()
        
        # 显示状态报告
        checker.print_status_report()
        
        # 提供修复建议
        print("\n🛠️ 修复建议:")
        print("-" * 30)
        
        baidu_status = results.get('baidu', {})
        google_status = results.get('google', {})
        llm_status = results.get('llm', {})
        
        if not any(service.get('available', False) for service in [baidu_status, google_status, llm_status]):
            print("⚠️  所有翻译服务都不可用，建议采取以下措施：")
            print()
            
            # 百度翻译修复建议
            if baidu_status.get('configured'):
                error = baidu_status.get('error', '')
                if '54004' in error or 'recharge' in error.lower():
                    print("💰 百度翻译账户余额不足：")
                    print("   1. 访问：https://fanyi-api.baidu.com/")
                    print("   2. 登录您的百度开发者账号")
                    print("   3. 查看账户余额并充值")
                    print("   4. 百度翻译API价格便宜，建议充值使用")
                    print()
            
            # Google翻译修复建议
            if '429' in str(google_status.get('error', '')):
                print("🌐 Google翻译频率限制：")
                print("   1. 等待1-2小时后重试")
                print("   2. 程序已自动添加重试机制")
                print("   3. 避免短时间内大量翻译请求")
                print()
            
            # LLM翻译配置建议
            if not llm_status.get('configured'):
                print("🤖 配置LLM翻译作为备用：")
                print("   1. 确保已配置LLM API（通义千问、智谱AI等）")
                print("   2. LLM翻译质量较好，可作为备用方案")
                print()
            
            # 临时解决方案
            print("🚀 临时解决方案：")
            print("   1. 禁用翻译功能：")
            print("      编辑 config/translation_config.py")
            print("      设置 ENABLE_TRANSLATION = False")
            print("   2. 程序将使用原始中文提示词，不影响基本功能")
            print()
            
            # 提供快速禁用选项
            try:
                choice = input("是否现在禁用翻译功能？(y/N): ").strip().lower()
                if choice in ['y', 'yes']:
                    disable_translation()
                    print("✅ 翻译功能已禁用，程序将使用原始中文提示词")
            except KeyboardInterrupt:
                print("\n操作已取消")
        
        else:
            available_services = [name for name, service in 
                                [('百度翻译', baidu_status), ('Google翻译', google_status), ('LLM翻译', llm_status)]
                                if service.get('available', False)]
            print(f"✅ 可用的翻译服务：{', '.join(available_services)}")
            print("翻译功能正常，无需修复")
    
    except Exception as e:
        print(f"❌ 检查过程中出现错误：{e}")
        print("\n建议手动禁用翻译功能：")
        print("编辑 config/translation_config.py，设置 ENABLE_TRANSLATION = False")

def disable_translation():
    """禁用翻译功能"""
    try:
        config_path = os.path.join(current_dir, 'config', 'translation_config.py')
        
        if os.path.exists(config_path):
            # 读取配置文件
            with open(config_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 替换配置
            content = content.replace('ENABLE_TRANSLATION = True', 'ENABLE_TRANSLATION = False')
            
            # 写回文件
            with open(config_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print(f"✅ 已更新配置文件：{config_path}")
        else:
            print(f"⚠️  配置文件不存在：{config_path}")
            
    except Exception as e:
        print(f"❌ 禁用翻译功能失败：{e}")

if __name__ == "__main__":
    main()
