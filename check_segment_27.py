#!/usr/bin/env python3
"""检查第27行镜头5的数据"""

import json
import os

def main():
    project_file = r"D:\AI_Video_Generator\output\天下无双\project.json"
    
    with open(project_file, 'r', encoding='utf-8') as f:
        data = json.load(f)

    # 查找第27行镜头5的数据
    voice_segments = data.get('voice_generation', {}).get('voice_segments', [])
    print(f'总共有 {len(voice_segments)} 个配音段落')

    # 查找第27个段落（索引26）
    if len(voice_segments) > 26:
        segment_27 = voice_segments[26]
        print(f'第27个段落:')
        print(f'  文本: {segment_27.get("text", "")}')
        print(f'  音频路径: {segment_27.get("audio_path", "")}')
        print(f'  镜头ID: {segment_27.get("shot_id", "")}')
        print(f'  场景ID: {segment_27.get("scene_id", "")}')
        
        # 检查音频文件是否存在
        audio_path = segment_27.get("audio_path", "")
        if audio_path:
            print(f'  音频文件存在: {os.path.exists(audio_path)}')
            if os.path.exists(audio_path):
                file_size = os.path.getsize(audio_path)
                print(f'  文件大小: {file_size} bytes')
    else:
        print('没有第27个段落')

    # 检查前几个段落的音频路径
    print('\n前5个段落的音频路径:')
    for i in range(min(5, len(voice_segments))):
        segment = voice_segments[i]
        print(f'  段落{i+1}: {segment.get("audio_path", "")}')

if __name__ == "__main__":
    main()
