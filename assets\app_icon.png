<svg width="64" height="64" viewBox="0 0 64 64" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4A90E2;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#357ABD;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景圆形 -->
  <circle cx="32" cy="32" r="30" fill="url(#grad1)" stroke="#2C5F8A" stroke-width="2"/>
  
  <!-- 视频图标 -->
  <rect x="18" y="20" width="28" height="20" rx="2" fill="white" opacity="0.9"/>
  <rect x="20" y="22" width="24" height="16" rx="1" fill="#2C5F8A"/>
  
  <!-- 播放按钮 -->
  <polygon points="26,26 26,34 34,30" fill="white"/>
  
  <!-- AI 装饰元素 -->
  <circle cx="48" cy="16" r="4" fill="#FFD700" opacity="0.8"/>
  <circle cx="16" cy="48" r="3" fill="#FF6B6B" opacity="0.8"/>
  <circle cx="50" cy="50" r="2" fill="#4ECDC4" opacity="0.8"/>
</svg>