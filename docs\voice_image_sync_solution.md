# 配音与图像数量同步解决方案

## 问题描述

在AI视频生成流程中，可能出现分镜图像数量与配音段落数量不匹配的问题：
- 图像生成阶段：生成了10个镜头画面
- 配音生成阶段：只匹配到9个或11个配音段落
- 影响：导致视频合成时画面与配音不同步

## 解决方案架构

### 1. 数量检测机制

```python
def _get_expected_shot_count(self, project_data):
    """获取预期的镜头数量（基于图像生成数据）"""
    # 方法1：从shot_image_mappings获取（最准确）
    # 方法2：从五阶段分镜数据计算
    # 方法3：从真实场景文件计算
```

### 2. 自动修复策略

#### 配音段落少于图像数量
- **策略**：智能扩展配音段落
- **方法**：
  1. 重新分段原始文本，精确匹配镜头数量
  2. 保留现有段落的基本信息
  3. 为新增段落分配合理的场景/镜头ID

#### 配音段落多于图像数量
- **策略**：合并多余的配音段落
- **方法**：
  1. 保留前N个段落（N=图像数量）
  2. 将多余段落的文本内容合并到最后一个保留段落
  3. 确保不丢失重要的配音内容

### 3. 用户交互界面

```python
def _show_count_mismatch_dialog(self, actual_count, expected_count):
    """显示用户友好的修复选项对话框"""
    # 显示详细的不匹配信息
    # 提供自动修复和跳过选项
    # 说明修复策略和可能影响
```

## 实现特点

### 1. 多数据源检测
- 优先使用`shot_image_mappings`（最准确）
- 降级到分镜脚本计算
- 最后使用场景文件统计

### 2. 智能文本分段
```python
def _create_precise_text_segments(self, original_text, total_shots):
    """根据实际镜头数量创建精确的文本分段"""
    # 按句子分段，然后平均分配给镜头
    # 处理句子数与镜头数的各种比例关系
```

### 3. 数据一致性保证
- 保持场景/镜头ID的连续性
- 维护配音段落的元数据完整性
- 确保与图像生成数据的键值匹配

## 使用流程

### 1. 自动检测
```python
# 在配音数据解析时自动触发
expected_shot_count = self._get_expected_shot_count(project_data)
actual_voice_count = len(self.voice_segments)

if expected_shot_count > 0 and actual_voice_count != expected_shot_count:
    # 触发修复流程
```

### 2. 用户确认
- 显示详细的不匹配信息
- 解释修复策略和影响
- 用户可选择自动修复或跳过

### 3. 自动修复
- 根据数量差异选择扩展或合并策略
- 保持数据完整性和一致性
- 记录修复过程和结果

## 优势

### 1. 预防性检测
- 在配音阶段就发现并解决问题
- 避免在视频合成阶段出现同步问题

### 2. 智能修复
- 基于原始文本的智能分段
- 保留重要的配音内容
- 维护场景结构的逻辑性

### 3. 用户友好
- 清晰的问题说明和修复建议
- 用户可控的修复流程
- 详细的日志记录

### 4. 数据一致性
- 确保与图像生成数据的完美匹配
- 为后续视频合成提供可靠基础
- 支持项目数据的完整性验证

## 扩展性

### 1. 支持多种修复策略
- 可根据项目类型选择不同的分段策略
- 支持用户自定义修复规则

### 2. 集成到工作流
- 可扩展到字幕生成、视频合成等环节
- 建立统一的数量一致性检查机制

### 3. 性能优化
- 缓存计算结果，避免重复检测
- 支持大型项目的高效处理

## 总结

这个解决方案通过多层次的检测机制、智能的修复策略和用户友好的交互界面，有效解决了配音与图像数量不匹配的问题，确保了AI视频生成流程的数据一致性和最终质量。
