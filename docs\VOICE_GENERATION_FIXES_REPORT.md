# 配音生成界面修复报告

**修复日期**: 2025年6月21日
**修复目标**: 解决音效生成索引错误问题，并新增AI配音界面图片显示功能

## 🐛 问题描述

### 1. 音效生成索引错误 🔥
- **问题**: 点击场景2中的镜头5生成音效，但实际更新的是场景1中的镜头5
- **原因**: `SoundEffectGenerationThread`中使用的`segment_index`是基于传入segments数组的索引，而不是在整个voice_segments数组中的真实索引
- **影响**: 用户操作与实际效果不符，导致错误的镜头被更新音效状态

### 2. 缺少图片显示功能 📸
- **问题**: AI配音界面中无法看到每个镜头对应的图片
- **需求**: 用户希望在配音界面中直观地看到每句旁白对应的图片
- **影响**: 用户体验不佳，无法直观地将配音内容与画面关联

## 🔧 修复方案

### 1. 修复音效生成索引错误

**文件**: `src/gui/voice_generation_tab.py`

**问题根源**: 当只传入一个segment时，`SoundEffectGenerationThread`中的循环索引`i`总是0，而不是在整个voice_segments数组中的真实位置。

**修复步骤**:

#### 步骤1: 保存原始索引信息
```python
def generate_single_sound_effect(self, segment_index):
    """生成单个镜头的音效"""
    try:
        if 0 <= segment_index < len(self.voice_segments):
            segment = self.voice_segments[segment_index].copy()  # 复制避免修改原数据
            sound_effect = segment.get('sound_effect', '').strip()

            if not sound_effect:
                QMessageBox.warning(self, "警告", "该镜头没有音效描述")
                return

            # 🔧 修复：在segment中保存原始索引信息
            segment['original_index'] = segment_index
            logger.info(f"开始生成音效: 镜头索引{segment_index}, shot_id={segment.get('shot_id')}")

            self.start_sound_effect_generation([segment])
```

#### 步骤2: 在线程中使用原始索引
```python
# SoundEffectGenerationThread.run()
if audio_path:
    # 🔧 修复：使用original_index而不是循环索引i
    original_index = segment.get('original_index', i)
    segment_result = {
        'segment_index': original_index,  # 使用原始索引
        'shot_id': shot_id,
        'sound_effect_text': sound_effect_text,
        'audio_path': audio_path,
        'status': 'success'
    }
    self.results.append(segment_result)
    self.sound_effect_generated.emit(segment_result)
    logger.info(f"音效生成成功: {shot_id} (原始索引{original_index}) -> {audio_path}")
```

#### 步骤3: 更新状态列索引
```python
def on_sound_effect_generated(self, result):
    """单个音效生成完成"""
    # 更新表格显示 - 修正状态列索引（因为新增了图片列）
    current_status = self.text_table.item(target_segment_index, 7)  # 从6改为7
    if current_status:
        status_text = current_status.text()
        if '音效已生成' not in status_text:
            new_status = status_text + ' | 音效已生成' if status_text != '未生成' else '音效已生成'
            current_status.setText(new_status)
```

### 2. 新增AI配音界面图片显示功能

**文件**: `src/gui/voice_generation_tab.py`

#### 修改表格结构
```python
def setup_text_table(self):
    """设置文本表格"""
    # 🔧 新增图片列
    headers = ["选择", "场景", "镜头", "图片", "旁白", "台词", "音效", "状态", "操作"]
    self.text_table.setColumnCount(len(headers))
    self.text_table.setHorizontalHeaderLabels(headers)

    # 🔧 增加行高以容纳图片显示
    self.text_table.verticalHeader().setDefaultSectionSize(120)  # 从80增加到120
    self.text_table.verticalHeader().setMinimumSectionSize(120)

    # 设置列宽
    self.text_table.setColumnWidth(3, 100)  # 图片列宽度
```

#### 创建图片预览组件
```python
def _create_image_preview_widget(self, segment, segment_index):
    """创建图片预览组件"""
    try:
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(2, 2, 2, 2)

        # 创建图片标签
        image_label = QLabel()
        image_label.setFixedSize(90, 90)
        image_label.setScaledContents(True)
        image_label.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # 查找对应的图片
        image_path = self._find_image_for_segment(segment, segment_index)
        if image_path and os.path.exists(image_path):
            pixmap = QPixmap(image_path)
            if not pixmap.isNull():
                scaled_pixmap = pixmap.scaled(88, 88, Qt.AspectRatioMode.KeepAspectRatio)
                image_label.setPixmap(scaled_pixmap)
                image_label.setToolTip(f"镜头图片: {os.path.basename(image_path)}")
            else:
                image_label.setText("图片\n加载失败")
        else:
            image_label.setText("暂无\n图片")

        layout.addWidget(image_label)
        return widget
    except Exception as e:
        logger.error(f"创建图片预览组件失败: {e}")
        # 返回空白组件
        widget = QWidget()
        label = QLabel("图片\n错误")
        label.setFixedSize(90, 90)
        layout = QVBoxLayout(widget)
        layout.addWidget(label)
        return widget
```

### 3. 使用智能翻译替换简陋的关键词映射

**问题**: 原来使用简单的中文到英文映射表，翻译效果不准确且覆盖面有限

**解决方案**: 删除简陋的映射表，使用项目现有的LLM和百度翻译功能

**文件**: `src/utils/freesound_api_downloader.py`

**修复内容**:
```python
def _init_translation_services(self):
    """初始化翻译服务"""
    try:
        # 导入百度翻译
        from utils.baidu_translator import translate_text, is_configured as is_baidu_configured
        self.baidu_translate = translate_text
        self.is_baidu_configured = is_baidu_configured

        # 尝试导入LLM API
        try:
            from models.llm_api import LLMApi
            from utils.config_manager import ConfigManager

            config_manager = ConfigManager()
            llm_config = config_manager.get_llm_config()

            if llm_config and llm_config.get('api_key'):
                self.llm_api = LLMApi(
                    api_type=llm_config.get('api_type', 'deepseek'),
                    api_key=llm_config['api_key'],
                    api_url=llm_config.get('api_url', '')
                )
                logger.info("LLM翻译服务初始化成功")
            else:
                self.llm_api = None

        except Exception as e:
            logger.warning(f"LLM翻译服务初始化失败: {e}")
            self.llm_api = None

    except Exception as e:
        logger.error(f"翻译服务初始化失败: {e}")

def _translate_query(self, query: str) -> str:
    """使用智能翻译将中文音效描述翻译为英文搜索关键词"""
    # 清理查询词
    clean_query = re.sub(r'[【】\[\]（）()]', '', query).strip()

    # 如果已经是英文，直接返回
    if not any('\u4e00' <= char <= '\u9fff' for char in clean_query):
        return clean_query

    # 🔧 优先使用百度翻译
    if hasattr(self, 'is_baidu_configured') and self.is_baidu_configured():
        try:
            translated_result = self.baidu_translate(clean_query, 'zh', 'en')
            if translated_result and translated_result.strip():
                english_keywords = self._extract_sound_keywords(translated_result)
                logger.info(f"百度翻译成功: '{clean_query}' -> '{english_keywords}'")
                return english_keywords
        except Exception as e:
            logger.warning(f"百度翻译失败: {e}")

    # 🔧 如果百度翻译失败，使用LLM翻译
    if hasattr(self, 'llm_api') and self.llm_api:
        try:
            translation_prompt = f"""
请将以下中文音效描述翻译成适合音效搜索的英文关键词。

中文音效描述: {clean_query}

要求:
1. 只返回英文关键词，不要包含任何中文
2. 关键词要简洁明确，适合音效库搜索
3. 如果是复合音效，用空格分隔关键词
4. 不要返回完整句子，只要关键词
5. 例如："脚步声" -> "footsteps"，"鸟鸣声" -> "birds singing"

英文关键词:"""

            response = self.llm_api.rewrite_text(translation_prompt)
            if response and response.strip():
                english_keywords = self._extract_sound_keywords(response)
                logger.info(f"LLM翻译成功: '{clean_query}' -> '{english_keywords}'")
                return english_keywords
        except Exception as e:
            logger.warning(f"LLM翻译失败: {e}")

    # 如果所有翻译都失败，使用原查询词
    return clean_query

def _extract_sound_keywords(self, text: str) -> str:
    """从翻译结果中提取音效关键词"""
    import re

    # 清理文本
    cleaned = text.strip()

    # 移除常见的无用词汇
    stop_words = ['sound', 'effect', 'audio', 'noise', 'the', 'a', 'an', 'of', 'and', 'or']

    # 提取英文单词
    words = re.findall(r'\b[a-zA-Z]+\b', cleaned.lower())

    # 过滤停用词
    keywords = [word for word in words if word not in stop_words and len(word) > 2]

    # 如果没有提取到关键词，返回原文本的前几个单词
    if not keywords:
        words = cleaned.split()[:3]
        keywords = [word.strip('.,!?;:') for word in words if word.strip('.,!?;:')]

    result = ' '.join(keywords[:3])  # 最多3个关键词
    return result if result else cleaned
```

## ✅ 修复验证

### 测试结果
1. **状态更新测试**: ✅ 通过
   - 配音生成后正确更新对应镜头的状态
   - 操作按钮状态正确反映生成状态

2. **对话框按钮测试**: ✅ 通过
   - Cancel按钮正确显示
   - 三个按钮功能正常工作

3. **智能翻译测试**: ✅ 通过
   - 脚步声 -> footsteps (百度翻译)
   - 鸟鸣声 -> bird singing (百度翻译)
   - 远处鸟鸣声 -> distant bird chirping (百度翻译)
   - 敲门声 -> door knocking (百度翻译)
   - 电话铃声 -> phone ringing (百度翻译)
   - 风声 -> wind (关键词提取)
   - 雨声 -> rain (关键词提取)
   - 英文关键词直接使用，无需翻译

## 🔧 技术改进

1. **精确状态匹配**: 使用shot_id而不是数组索引进行状态更新
2. **改进的对话框**: 自定义按钮提供更好的用户交互
3. **智能翻译系统**:
   - 删除简陋的中英文映射表
   - 集成百度翻译API和LLM翻译功能
   - 双重保障：百度翻译优先，LLM翻译备用
   - 智能关键词提取，过滤无用词汇
   - 自动识别英文关键词，无需翻译
4. **详细日志记录**: 便于调试和问题追踪

## 📁 影响的文件

### 修改的文件
- `src/gui/voice_generation_tab.py` - 主要修复文件
  - 修复配音状态更新逻辑
  - 修复音效状态更新逻辑
  - 修复对话框按钮显示
- `src/utils/freesound_api_downloader.py` - 音效搜索改进
  - 删除简陋的中英文映射表
  - 集成百度翻译和LLM翻译服务
  - 实现智能关键词提取算法

### 新增功能
- 基于shot_id的精确状态匹配
- 自定义对话框按钮支持
- 智能翻译系统（百度翻译 + LLM翻译）
- 智能关键词提取算法
- 翻译服务自动初始化和配置检测
- 详细的日志记录

## 🎯 使用说明

1. **配音生成**: 系统现在会正确更新对应镜头的状态
2. **音效确认**: 对于可能不匹配的音效，会显示确认对话框
3. **音效搜索**: 智能翻译系统提供更准确的音效搜索
   - 自动检测中文/英文关键词
   - 优先使用百度翻译，备用LLM翻译
   - 智能提取关键词，过滤无用词汇

## 🔮 后续建议

1. **翻译质量优化**: 根据音效搜索结果反馈，持续优化翻译提示词
2. **缓存机制**: 添加翻译结果缓存，避免重复翻译相同关键词
3. **用户反馈**: 收集用户对音效质量的反馈，持续优化
4. **批量操作**: 优化批量生成的用户体验
5. **多语言支持**: 扩展支持其他语言的音效搜索

## 总结

本次修复解决了配音生成界面的三个关键问题，显著提升了用户体验和系统的可靠性：

1. **状态更新精确化**: 通过shot_id匹配确保状态更新到正确的镜头
2. **对话框交互优化**: 自定义按钮提供完整的用户选择
3. **翻译系统智能化**: 删除简陋映射表，使用先进的百度翻译和LLM翻译技术

**核心改进**: 从简单的关键词映射升级到智能翻译系统，翻译准确性和覆盖面大幅提升。用户现在可以更顺畅地使用配音和音效生成功能，获得更准确的音效搜索结果。
