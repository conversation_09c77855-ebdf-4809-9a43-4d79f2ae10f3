"""
测试新的项目数据结构和语音时长限制
"""

import os
import sys
import json
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.core.project_manager_v2 import ProjectManagerV2
from src.utils.logger import logger


def test_new_project_structure():
    """测试新的项目数据结构"""
    print("🔧 测试新的项目数据结构...")
    
    # 创建项目管理器
    pm = ProjectManagerV2("test_output")
    
    # 创建测试项目
    project_name = "测试项目_优化结构"
    success = pm.create_project(project_name, "测试优化后的项目数据结构")
    
    if not success:
        print("❌ 项目创建失败")
        return False
    
    print(f"✅ 项目创建成功: {project_name}")
    
    # 添加测试镜头
    test_shots = [
        {
            "shot_id": "shot_001",
            "scene_id": "scene_001", 
            "title": "开场镜头",
            "description": "在一个阳光明媚的早晨，小明走出家门。"
        },
        {
            "shot_id": "shot_002",
            "scene_id": "scene_001",
            "title": "街道镜头", 
            "description": "他沿着熟悉的街道慢慢走向学校，心情愉快。"
        },
        {
            "shot_id": "shot_003",
            "scene_id": "scene_002",
            "title": "学校门口",
            "description": "到达学校门口时，他看到了好朋友小红正在等他。"
        }
    ]
    
    # 添加镜头
    for shot in test_shots:
        success = pm.add_shot(
            shot["shot_id"],
            shot["scene_id"], 
            shot["title"],
            shot["description"]
        )
        if success:
            print(f"✅ 镜头添加成功: {shot['shot_id']}")
        else:
            print(f"❌ 镜头添加失败: {shot['shot_id']}")
    
    # 模拟更新镜头数据
    pm.update_shot_voice("shot_001", "audio/shot_001.wav", 4.5, "completed")
    pm.update_shot_image("shot_001", "images/shot_001.jpg", "completed")
    pm.update_shot_video("shot_001", "videos/shot_001.mp4", "completed")
    
    pm.update_shot_voice("shot_002", "audio/shot_002.wav", 6.2, "completed")
    pm.update_shot_image("shot_002", "images/shot_002.jpg", "completed")
    
    # 保存项目
    pm.save_project()
    
    # 显示项目数据结构
    project_data = pm.get_project_data()
    print("\n📋 项目数据结构:")
    print(json.dumps(project_data, ensure_ascii=False, indent=2))
    
    # 显示进度统计
    progress = pm.get_progress()
    print(f"\n📊 项目进度:")
    print(f"语音: {progress['voice']['completed']}/{progress['voice']['total']}")
    print(f"图像: {progress['image']['completed']}/{progress['image']['total']}")
    print(f"视频: {progress['video']['completed']}/{progress['video']['total']}")
    
    return True


def test_voice_duration_limit():
    """测试语音时长限制功能"""
    print("\n🎵 测试语音时长限制...")
    
    # 测试文本分割逻辑
    test_texts = [
        "这是一个短文本。",  # 短文本，应该保持不变
        "这是一个比较长的文本，包含多个句子。它应该被分割成多个较短的段落。每个段落的时长应该控制在5秒以内。",  # 长文本，应该被分割
        "在一个阳光明媚的早晨，小明走出家门，沿着熟悉的街道慢慢走向学校。他的心情非常愉快，因为今天是他期待已久的春游日。",  # 超长文本
    ]
    
    for i, text in enumerate(test_texts, 1):
        print(f"\n测试文本 {i}: {text}")
        print(f"原文长度: {len(text)} 字符")
        print(f"估算时长: {len(text) / 4.0:.1f} 秒")
        
        # 模拟分割逻辑
        max_chars = 20  # 约5秒
        if len(text) <= max_chars:
            segments = [text]
            print("✅ 文本较短，无需分割")
        else:
            # 按句号分割
            sentences = text.split('。')
            sentences = [s.strip() for s in sentences if s.strip()]
            
            segments = []
            current_segment = ""
            
            for sentence in sentences:
                if len(current_segment + sentence) <= max_chars:
                    current_segment += sentence + '。'
                else:
                    if current_segment:
                        segments.append(current_segment.rstrip('。'))
                    current_segment = sentence + '。'
            
            if current_segment:
                segments.append(current_segment.rstrip('。'))
            
            print(f"🔧 分割为 {len(segments)} 段:")
            for j, segment in enumerate(segments, 1):
                duration = len(segment) / 4.0
                print(f"  段落 {j}: {segment} (长度: {len(segment)}, 估算时长: {duration:.1f}s)")
    
    return True


def test_project_upgrade():
    """测试项目结构升级功能"""
    print("\n🔄 测试项目结构升级...")
    
    # 创建一个模拟的旧版本项目数据
    old_project_data = {
        "project_name": "旧版本项目",
        "description": "这是一个旧版本的项目",
        "created_time": "2024-01-01T00:00:00",
        "project_dir": "test_output/旧版本项目",
        "original_text": "这是原始文本内容。",
        "rewritten_text": "这是改写后的文本内容。",
        "voice_generation": {
            "provider": "edge_tts",
            "settings": {"voice": "zh-CN-YunxiNeural", "speed": 1.0},
            "voice_segments": [
                {
                    "shot_id": "shot_001",
                    "scene_id": "scene_001",
                    "original_text": "这是第一个镜头的文本。",
                    "audio_path": "audio/shot_001.wav",
                    "status": "completed"
                }
            ]
        },
        "image_generation": {
            "settings": {"engine": "pollinations", "style": "realistic"}
        },
        "video_generation": {
            "settings": {"engine": "cogvideox_flash", "duration": 5.0}
        }
    }
    
    # 保存旧版本项目文件
    old_project_dir = "test_output/旧版本项目"
    os.makedirs(old_project_dir, exist_ok=True)
    old_project_file = os.path.join(old_project_dir, "project.json")
    
    with open(old_project_file, 'w', encoding='utf-8') as f:
        json.dump(old_project_data, f, ensure_ascii=False, indent=2)
    
    print(f"✅ 创建旧版本项目文件: {old_project_file}")
    
    # 使用新的项目管理器加载旧项目
    pm = ProjectManagerV2("test_output")
    success = pm.load_project(old_project_file)
    
    if success:
        print("✅ 旧项目加载成功，数据结构已自动升级")
        
        # 显示升级后的数据结构
        new_data = pm.get_project_data()
        print("\n📋 升级后的项目数据结构:")
        print(json.dumps(new_data, ensure_ascii=False, indent=2))
        
        # 验证关键数据是否正确迁移
        project_info = pm.get_project_info()
        print(f"\n✅ 项目信息: {project_info['name']} (版本: {project_info['version']})")
        
        shots = pm.get_shots()
        print(f"✅ 镜头数据: {len(shots)} 个镜头")
        
        settings = pm.get_settings()
        print(f"✅ 设置数据: 语音引擎={settings['voice']['provider']}")
        
    else:
        print("❌ 旧项目加载失败")
        return False
    
    return True


if __name__ == "__main__":
    print("🚀 开始测试优化后的项目数据结构...")
    
    try:
        # 测试新项目结构
        if test_new_project_structure():
            print("\n✅ 新项目数据结构测试通过")
        else:
            print("\n❌ 新项目数据结构测试失败")
        
        # 测试语音时长限制
        if test_voice_duration_limit():
            print("\n✅ 语音时长限制测试通过")
        else:
            print("\n❌ 语音时长限制测试失败")
        
        # 测试项目升级
        if test_project_upgrade():
            print("\n✅ 项目结构升级测试通过")
        else:
            print("\n❌ 项目结构升级测试失败")
        
        print("\n🎉 所有测试完成！")
        
    except Exception as e:
        print(f"\n💥 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
