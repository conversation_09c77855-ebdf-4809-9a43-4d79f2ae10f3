#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试CogVideoX-Flash图生视频API参数格式
"""

import asyncio
import aiohttp
import json
import time
import base64
import os

# 智谱AI API配置
API_KEY = "ed7ffe9976bb4484ab16647564c0cb27.rI1BXVjDDDURMtJY"
BASE_URL = "https://open.bigmodel.cn/api/paas/v4"

def image_to_base64(image_path):
    """将图像转换为base64格式"""
    try:
        with open(image_path, 'rb') as f:
            image_data = f.read()
        
        # 获取文件扩展名
        ext = os.path.splitext(image_path)[1].lower()
        if ext == '.png':
            mime_type = 'image/png'
        elif ext in ['.jpg', '.jpeg']:
            mime_type = 'image/jpeg'
        else:
            mime_type = 'image/png'  # 默认
        
        base64_str = base64.b64encode(image_data).decode('utf-8')
        return f"data:{mime_type};base64,{base64_str}"
    except Exception as e:
        print(f"图像转换失败: {e}")
        return None

async def test_image_to_video_params():
    """测试不同的图生视频参数格式"""
    print("🎬 测试图生视频参数格式...")
    
    # 查找测试图像
    test_image_paths = [
        "D:\\AI_Video_Generator\\output\\视频生成测试\\images\\pollinations\\pollinations_1750834422274_0.png",
        "test_image.png",
        "sample.jpg"
    ]
    
    test_image = None
    for path in test_image_paths:
        if os.path.exists(path):
            test_image = path
            break
    
    if not test_image:
        print("❌ 未找到测试图像")
        return False
    
    print(f"📷 使用测试图像: {test_image}")
    
    # 转换图像为base64
    image_base64 = image_to_base64(test_image)
    if not image_base64:
        return False
    
    headers = {
        'Authorization': f'Bearer {API_KEY}',
        'Content-Type': 'application/json'
    }
    
    # 测试不同的参数组合
    test_cases = [
        {
            "name": "基本图生视频",
            "data": {
                "model": "cogvideox-flash",
                "prompt": "让图像中的场景动起来",
                "image_url": image_base64
            }
        },
        {
            "name": "带时长参数",
            "data": {
                "model": "cogvideox-flash", 
                "prompt": "让图像中的场景动起来",
                "image_url": image_base64,
                "duration": 5
            }
        },
        {
            "name": "带FPS参数",
            "data": {
                "model": "cogvideox-flash",
                "prompt": "让图像中的场景动起来", 
                "image_url": image_base64,
                "fps": 24
            }
        },
        {
            "name": "带尺寸参数",
            "data": {
                "model": "cogvideox-flash",
                "prompt": "让图像中的场景动起来",
                "image_url": image_base64,
                "size": "960x1280"
            }
        },
        {
            "name": "完整参数",
            "data": {
                "model": "cogvideox-flash",
                "prompt": "让图像中的场景动起来",
                "image_url": image_base64,
                "duration": 5,
                "fps": 24,
                "size": "960x1280"
            }
        }
    ]
    
    async with aiohttp.ClientSession() as session:
        for i, test_case in enumerate(test_cases):
            print(f"\n📋 测试 {i+1}/{len(test_cases)}: {test_case['name']}")
            print(f"   参数: {list(test_case['data'].keys())}")
            
            try:
                url = f"{BASE_URL}/videos/generations"
                
                async with session.post(url, headers=headers, json=test_case['data']) as response:
                    print(f"   状态码: {response.status}")
                    
                    if response.status == 200:
                        result = await response.json()
                        task_id = result.get('id')
                        print(f"   ✅ 成功提交，任务ID: {task_id}")
                        
                        # 简单等待一下看状态
                        await asyncio.sleep(5)
                        
                        # 查询状态
                        status_url = f"{BASE_URL}/async-result/{task_id}"
                        async with session.get(status_url, headers=headers) as status_response:
                            if status_response.status == 200:
                                status_result = await status_response.json()
                                task_status = status_result.get('task_status', 'UNKNOWN')
                                print(f"   状态: {task_status}")
                                
                                if task_status == 'SUCCESS':
                                    print(f"   🎉 {test_case['name']} 参数格式正确!")
                                    return True
                                elif task_status in ['PROCESSING', 'SUBMITTED']:
                                    print(f"   ⏳ {test_case['name']} 参数格式正确，正在处理中")
                                    return True
                                elif task_status == 'FAIL':
                                    error_info = status_result.get('error', {})
                                    print(f"   ❌ 生成失败: {error_info}")
                                else:
                                    print(f"   ⚠️ 未知状态: {task_status}")
                    else:
                        response_text = await response.text()
                        print(f"   ❌ 请求失败: {response_text}")
                        
                        # 分析错误
                        if "1210" in response_text:
                            print(f"   💡 参数格式错误，尝试下一组参数...")
                        
            except Exception as e:
                print(f"   ❌ 测试异常: {e}")
            
            # 等待一下避免请求过快
            await asyncio.sleep(2)
    
    return False

async def test_duration_formats():
    """测试不同的duration格式"""
    print("\n⏱️ 测试duration参数格式...")
    
    headers = {
        'Authorization': f'Bearer {API_KEY}',
        'Content-Type': 'application/json'
    }
    
    duration_tests = [
        {"duration": 5, "desc": "整数"},
        {"duration": 5.0, "desc": "浮点数"},
        {"duration": "5", "desc": "字符串"},
        {"duration": 5.5, "desc": "小数"},
    ]
    
    async with aiohttp.ClientSession() as session:
        for test in duration_tests:
            print(f"\n📋 测试duration: {test['duration']} ({test['desc']})")
            
            test_data = {
                "model": "cogvideox-flash",
                "prompt": "一朵花在微风中摇摆",
                "duration": test['duration']
            }
            
            try:
                url = f"{BASE_URL}/videos/generations"
                
                async with session.post(url, headers=headers, json=test_data) as response:
                    print(f"   状态码: {response.status}")
                    
                    if response.status == 200:
                        result = await response.json()
                        print(f"   ✅ duration {test['duration']} 格式正确")
                    else:
                        response_text = await response.text()
                        print(f"   ❌ duration {test['duration']} 格式错误: {response_text[:100]}")
                        
            except Exception as e:
                print(f"   ❌ 测试异常: {e}")
            
            await asyncio.sleep(1)

async def main():
    """主测试函数"""
    print("=" * 60)
    print("🧪 CogVideoX-Flash 图生视频参数格式测试")
    print("=" * 60)
    
    # 测试图生视频参数
    await test_image_to_video_params()
    
    # 测试duration格式
    await test_duration_formats()
    
    print("\n" + "=" * 60)
    print("📋 测试完成")
    print("=" * 60)

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n👋 测试已取消")
    except Exception as e:
        print(f"\n❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
