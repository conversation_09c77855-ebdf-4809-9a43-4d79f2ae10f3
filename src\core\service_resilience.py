#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI服务弹性和风险控制系统
提供服务降级、重试机制、熔断器、健康检查等功能
"""

import asyncio
import time
import json
from typing import Dict, List, Optional, Any, Callable, Union
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime, timedelta
from pathlib import Path

from PyQt5.QtCore import QObject, pyqtSignal, QTimer

from src.utils.logger import logger
from src.core.service_base import ServiceResult
from src.utils.enhanced_error_handler import enhanced_error_handler, ErrorDomain

class ServiceStatus(Enum):
    """服务状态"""
    HEALTHY = "healthy"           # 健康
    DEGRADED = "degraded"         # 降级
    UNHEALTHY = "unhealthy"       # 不健康
    CIRCUIT_OPEN = "circuit_open" # 熔断开启
    MAINTENANCE = "maintenance"   # 维护中

class FailureType(Enum):
    """故障类型"""
    TIMEOUT = "timeout"           # 超时
    CONNECTION_ERROR = "connection_error"  # 连接错误
    API_ERROR = "api_error"       # API错误
    RATE_LIMIT = "rate_limit"     # 频率限制
    QUOTA_EXCEEDED = "quota_exceeded"  # 配额超限
    AUTHENTICATION = "authentication"  # 认证失败
    SERVER_ERROR = "server_error" # 服务器错误
    UNKNOWN = "unknown"           # 未知错误

@dataclass
class ServiceMetrics:
    """服务指标"""
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    average_response_time: float = 0.0
    last_request_time: Optional[datetime] = None
    last_success_time: Optional[datetime] = None
    last_failure_time: Optional[datetime] = None
    consecutive_failures: int = 0
    
    @property
    def success_rate(self) -> float:
        """成功率"""
        if self.total_requests == 0:
            return 1.0
        return self.successful_requests / self.total_requests
    
    @property
    def failure_rate(self) -> float:
        """失败率"""
        return 1.0 - self.success_rate

@dataclass
class CircuitBreakerConfig:
    """熔断器配置"""
    failure_threshold: int = 5      # 失败阈值
    timeout_threshold: float = 30.0 # 超时阈值（秒）
    recovery_timeout: float = 60.0  # 恢复超时（秒）
    half_open_max_calls: int = 3    # 半开状态最大调用次数

@dataclass
class RetryConfig:
    """重试配置"""
    max_retries: int = 3
    base_delay: float = 1.0
    max_delay: float = 60.0
    backoff_factor: float = 2.0
    jitter: bool = True

@dataclass
class ServiceConfig:
    """服务配置"""
    service_name: str
    providers: List[str]
    primary_provider: str
    fallback_providers: List[str] = field(default_factory=list)
    circuit_breaker: CircuitBreakerConfig = field(default_factory=CircuitBreakerConfig)
    retry_config: RetryConfig = field(default_factory=RetryConfig)
    health_check_interval: float = 30.0  # 健康检查间隔（秒）
    degradation_threshold: float = 0.7   # 降级阈值（成功率）

class CircuitBreaker:
    """熔断器"""
    
    def __init__(self, config: CircuitBreakerConfig):
        self.config = config
        self.state = ServiceStatus.HEALTHY
        self.failure_count = 0
        self.last_failure_time = None
        self.half_open_calls = 0
    
    def can_execute(self) -> bool:
        """判断是否可以执行请求"""
        if self.state == ServiceStatus.HEALTHY:
            return True
        elif self.state == ServiceStatus.CIRCUIT_OPEN:
            # 检查是否可以进入半开状态
            if (time.time() - self.last_failure_time) > self.config.recovery_timeout:
                self.state = ServiceStatus.DEGRADED  # 半开状态
                self.half_open_calls = 0
                logger.info("熔断器进入半开状态")
                return True
            return False
        elif self.state == ServiceStatus.DEGRADED:  # 半开状态
            return self.half_open_calls < self.config.half_open_max_calls
        
        return False
    
    def record_success(self):
        """记录成功"""
        if self.state == ServiceStatus.DEGRADED:  # 半开状态
            self.half_open_calls += 1
            if self.half_open_calls >= self.config.half_open_max_calls:
                self.state = ServiceStatus.HEALTHY
                self.failure_count = 0
                logger.info("熔断器恢复到健康状态")
        elif self.state == ServiceStatus.HEALTHY:
            self.failure_count = 0
    
    def record_failure(self):
        """记录失败"""
        self.failure_count += 1
        self.last_failure_time = time.time()
        
        if self.state == ServiceStatus.DEGRADED:  # 半开状态失败
            self.state = ServiceStatus.CIRCUIT_OPEN
            logger.warning("熔断器重新开启")
        elif self.failure_count >= self.config.failure_threshold:
            self.state = ServiceStatus.CIRCUIT_OPEN
            logger.warning(f"熔断器开启，连续失败次数: {self.failure_count}")

class ServiceHealthMonitor(QObject):
    """服务健康监控器"""
    
    # 信号
    service_status_changed = pyqtSignal(str, str)  # service_name, status
    service_degraded = pyqtSignal(str)  # service_name
    service_recovered = pyqtSignal(str)  # service_name
    
    def __init__(self):
        super().__init__()
        self.services: Dict[str, ServiceConfig] = {}
        self.metrics: Dict[str, ServiceMetrics] = {}
        self.circuit_breakers: Dict[str, CircuitBreaker] = {}
        self.service_status: Dict[str, ServiceStatus] = {}
        
        # 健康检查定时器
        self.health_check_timer = QTimer()
        self.health_check_timer.timeout.connect(self._perform_health_checks)
        self.health_check_timer.start(30000)  # 30秒检查一次
        
        # 配置文件路径
        self.config_file = Path("config/service_resilience.json")
        self.metrics_file = Path("logs/service_metrics.json")
        
        # 加载配置
        self._load_configuration()
    
    def register_service(self, config: ServiceConfig):
        """注册服务"""
        self.services[config.service_name] = config
        self.metrics[config.service_name] = ServiceMetrics()
        self.circuit_breakers[config.service_name] = CircuitBreaker(config.circuit_breaker)
        self.service_status[config.service_name] = ServiceStatus.HEALTHY
        
        logger.info(f"服务已注册: {config.service_name}")
    
    def record_request(self, service_name: str, success: bool, response_time: float = 0.0, 
                      failure_type: FailureType = None):
        """记录请求结果"""
        if service_name not in self.metrics:
            logger.warning(f"未注册的服务: {service_name}")
            return
        
        metrics = self.metrics[service_name]
        circuit_breaker = self.circuit_breakers[service_name]
        
        # 更新指标
        metrics.total_requests += 1
        metrics.last_request_time = datetime.now()
        
        if success:
            metrics.successful_requests += 1
            metrics.last_success_time = datetime.now()
            metrics.consecutive_failures = 0
            
            # 更新平均响应时间
            if metrics.total_requests == 1:
                metrics.average_response_time = response_time
            else:
                metrics.average_response_time = (
                    (metrics.average_response_time * (metrics.total_requests - 1) + response_time) 
                    / metrics.total_requests
                )
            
            circuit_breaker.record_success()
        else:
            metrics.failed_requests += 1
            metrics.last_failure_time = datetime.now()
            metrics.consecutive_failures += 1
            
            circuit_breaker.record_failure()
            
            # 记录错误到增强错误处理系统
            if failure_type:
                self._report_service_error(service_name, failure_type)
        
        # 检查服务状态
        self._update_service_status(service_name)
        
        # 保存指标
        self._save_metrics()
    
    def can_call_service(self, service_name: str) -> bool:
        """检查是否可以调用服务"""
        if service_name not in self.circuit_breakers:
            return True
        
        return self.circuit_breakers[service_name].can_execute()
    
    def get_service_status(self, service_name: str) -> ServiceStatus:
        """获取服务状态"""
        return self.service_status.get(service_name, ServiceStatus.HEALTHY)
    
    def get_service_metrics(self, service_name: str) -> Optional[ServiceMetrics]:
        """获取服务指标"""
        return self.metrics.get(service_name)
    
    def get_recommended_provider(self, service_name: str) -> Optional[str]:
        """获取推荐的服务提供商"""
        if service_name not in self.services:
            return None
        
        config = self.services[service_name]
        status = self.service_status[service_name]
        
        # 如果主要提供商健康，使用主要提供商
        if status in [ServiceStatus.HEALTHY, ServiceStatus.DEGRADED]:
            return config.primary_provider
        
        # 否则使用备用提供商
        for fallback in config.fallback_providers:
            # 这里可以添加更复杂的逻辑来选择最佳备用提供商
            return fallback
        
        return config.primary_provider  # 如果没有备用，仍然返回主要提供商
    
    def _update_service_status(self, service_name: str):
        """更新服务状态"""
        metrics = self.metrics[service_name]
        config = self.services[service_name]
        circuit_breaker = self.circuit_breakers[service_name]
        
        old_status = self.service_status[service_name]
        new_status = old_status
        
        # 根据熔断器状态确定服务状态
        if circuit_breaker.state == ServiceStatus.CIRCUIT_OPEN:
            new_status = ServiceStatus.CIRCUIT_OPEN
        elif circuit_breaker.state == ServiceStatus.DEGRADED:
            new_status = ServiceStatus.DEGRADED
        elif metrics.success_rate < config.degradation_threshold:
            new_status = ServiceStatus.DEGRADED
        else:
            new_status = ServiceStatus.HEALTHY
        
        if new_status != old_status:
            self.service_status[service_name] = new_status
            logger.info(f"服务 {service_name} 状态变更: {old_status.value} -> {new_status.value}")
            
            # 发送信号
            self.service_status_changed.emit(service_name, new_status.value)
            
            if new_status == ServiceStatus.DEGRADED:
                self.service_degraded.emit(service_name)
            elif old_status in [ServiceStatus.DEGRADED, ServiceStatus.CIRCUIT_OPEN] and new_status == ServiceStatus.HEALTHY:
                self.service_recovered.emit(service_name)
    
    def _perform_health_checks(self):
        """执行健康检查"""
        for service_name in self.services:
            # 这里可以添加主动健康检查逻辑
            # 例如发送ping请求到服务端点
            pass
    
    def _report_service_error(self, service_name: str, failure_type: FailureType):
        """向错误处理系统报告服务错误"""
        try:
            error_message = f"服务 {service_name} 出现 {failure_type.value} 错误"
            
            # 创建模拟异常
            if failure_type == FailureType.TIMEOUT:
                exception = TimeoutError(error_message)
            elif failure_type == FailureType.CONNECTION_ERROR:
                exception = ConnectionError(error_message)
            else:
                exception = Exception(error_message)
            
            enhanced_error_handler.handle_exception(
                exception, 
                show_to_user=False  # 服务错误不直接显示给用户
            )
        except Exception as e:
            logger.error(f"报告服务错误失败: {e}")
    
    def _load_configuration(self):
        """加载配置"""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                
                for service_data in config_data.get('services', []):
                    config = ServiceConfig(**service_data)
                    self.register_service(config)
                
                logger.info(f"已加载 {len(self.services)} 个服务配置")
        except Exception as e:
            logger.error(f"加载服务配置失败: {e}")
    
    def _save_metrics(self):
        """保存指标到文件"""
        try:
            metrics_data = {}
            for service_name, metrics in self.metrics.items():
                metrics_data[service_name] = {
                    'total_requests': metrics.total_requests,
                    'successful_requests': metrics.successful_requests,
                    'failed_requests': metrics.failed_requests,
                    'average_response_time': metrics.average_response_time,
                    'success_rate': metrics.success_rate,
                    'consecutive_failures': metrics.consecutive_failures,
                    'last_request_time': metrics.last_request_time.isoformat() if metrics.last_request_time else None,
                    'last_success_time': metrics.last_success_time.isoformat() if metrics.last_success_time else None,
                    'last_failure_time': metrics.last_failure_time.isoformat() if metrics.last_failure_time else None
                }
            
            self.metrics_file.parent.mkdir(exist_ok=True)
            with open(self.metrics_file, 'w', encoding='utf-8') as f:
                json.dump(metrics_data, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            logger.error(f"保存服务指标失败: {e}")

class RetryManager:
    """重试管理器"""

    def __init__(self):
        self.retry_configs: Dict[str, RetryConfig] = {}

    def register_retry_config(self, service_name: str, config: RetryConfig):
        """注册重试配置"""
        self.retry_configs[service_name] = config

    async def execute_with_retry(self, service_name: str, func: Callable, *args, **kwargs) -> Any:
        """带重试的执行函数"""
        config = self.retry_configs.get(service_name, RetryConfig())

        last_exception = None

        for attempt in range(config.max_retries + 1):
            try:
                start_time = time.time()

                # 检查熔断器状态
                if not service_monitor.can_call_service(service_name):
                    raise Exception(f"服务 {service_name} 熔断器开启，拒绝请求")

                # 执行函数
                if asyncio.iscoroutinefunction(func):
                    result = await func(*args, **kwargs)
                else:
                    result = func(*args, **kwargs)

                # 记录成功
                response_time = time.time() - start_time
                service_monitor.record_request(service_name, True, response_time)

                return result

            except Exception as e:
                last_exception = e
                response_time = time.time() - start_time

                # 分析错误类型
                failure_type = self._classify_error(e)

                # 记录失败
                service_monitor.record_request(service_name, False, response_time, failure_type)

                # 如果是最后一次尝试，抛出异常
                if attempt == config.max_retries:
                    logger.error(f"服务 {service_name} 重试 {config.max_retries} 次后仍然失败: {e}")
                    break

                # 计算延迟时间
                delay = self._calculate_delay(config, attempt)
                logger.warning(f"服务 {service_name} 第 {attempt + 1} 次尝试失败，{delay:.2f}秒后重试: {e}")

                await asyncio.sleep(delay)

        # 所有重试都失败了
        raise last_exception

    def _classify_error(self, exception: Exception) -> FailureType:
        """分类错误类型"""
        error_str = str(exception).lower()

        if isinstance(exception, TimeoutError) or 'timeout' in error_str:
            return FailureType.TIMEOUT
        elif isinstance(exception, ConnectionError) or 'connection' in error_str:
            return FailureType.CONNECTION_ERROR
        elif 'rate limit' in error_str or 'too many requests' in error_str:
            return FailureType.RATE_LIMIT
        elif 'quota' in error_str or 'limit exceeded' in error_str:
            return FailureType.QUOTA_EXCEEDED
        elif 'unauthorized' in error_str or 'authentication' in error_str:
            return FailureType.AUTHENTICATION
        elif 'server error' in error_str or '500' in error_str:
            return FailureType.SERVER_ERROR
        else:
            return FailureType.UNKNOWN

    def _calculate_delay(self, config: RetryConfig, attempt: int) -> float:
        """计算重试延迟"""
        delay = config.base_delay * (config.backoff_factor ** attempt)
        delay = min(delay, config.max_delay)

        # 添加抖动
        if config.jitter:
            import random
            delay *= (0.5 + random.random() * 0.5)

        return delay

class ServiceDegradationManager:
    """服务降级管理器"""

    def __init__(self):
        self.degradation_strategies: Dict[str, Callable] = {}
        self.fallback_responses: Dict[str, Any] = {}

    def register_degradation_strategy(self, service_name: str, strategy: Callable):
        """注册降级策略"""
        self.degradation_strategies[service_name] = strategy
        logger.info(f"已注册服务 {service_name} 的降级策略")

    def register_fallback_response(self, service_name: str, response: Any):
        """注册备用响应"""
        self.fallback_responses[service_name] = response
        logger.info(f"已注册服务 {service_name} 的备用响应")

    async def execute_with_degradation(self, service_name: str, func: Callable, *args, **kwargs) -> Any:
        """带降级的执行函数"""
        status = service_monitor.get_service_status(service_name)

        # 如果服务健康，正常执行
        if status == ServiceStatus.HEALTHY:
            return await self._execute_normal(service_name, func, *args, **kwargs)

        # 如果服务降级，尝试降级策略
        elif status == ServiceStatus.DEGRADED:
            return await self._execute_degraded(service_name, func, *args, **kwargs)

        # 如果服务不可用，使用备用响应
        else:
            return await self._execute_fallback(service_name, func, *args, **kwargs)

    async def _execute_normal(self, service_name: str, func: Callable, *args, **kwargs) -> Any:
        """正常执行"""
        if asyncio.iscoroutinefunction(func):
            return await func(*args, **kwargs)
        else:
            return func(*args, **kwargs)

    async def _execute_degraded(self, service_name: str, func: Callable, *args, **kwargs) -> Any:
        """降级执行"""
        # 首先尝试降级策略
        if service_name in self.degradation_strategies:
            try:
                strategy = self.degradation_strategies[service_name]
                logger.info(f"使用服务 {service_name} 的降级策略")

                if asyncio.iscoroutinefunction(strategy):
                    return await strategy(*args, **kwargs)
                else:
                    return strategy(*args, **kwargs)
            except Exception as e:
                logger.warning(f"降级策略执行失败: {e}")

        # 降级策略失败，尝试正常执行
        try:
            return await self._execute_normal(service_name, func, *args, **kwargs)
        except Exception as e:
            logger.warning(f"降级状态下正常执行失败: {e}")
            return await self._execute_fallback(service_name, func, *args, **kwargs)

    async def _execute_fallback(self, service_name: str, func: Callable, *args, **kwargs) -> Any:
        """备用执行"""
        if service_name in self.fallback_responses:
            logger.info(f"使用服务 {service_name} 的备用响应")
            return self.fallback_responses[service_name]

        # 如果没有备用响应，抛出异常
        raise Exception(f"服务 {service_name} 不可用且没有备用响应")

class ServiceOrchestrator:
    """服务编排器 - 统一的服务调用入口"""

    def __init__(self):
        self.retry_manager = RetryManager()
        self.degradation_manager = ServiceDegradationManager()
        self.load_balancer = ServiceLoadBalancer()

    async def call_service(self, service_name: str, func: Callable, *args, **kwargs) -> Any:
        """统一的服务调用接口"""
        try:
            # 获取推荐的提供商
            provider = service_monitor.get_recommended_provider(service_name)
            if provider:
                kwargs['provider'] = provider

            # 带重试和降级的执行
            return await self.retry_manager.execute_with_retry(
                service_name,
                lambda: self.degradation_manager.execute_with_degradation(
                    service_name, func, *args, **kwargs
                )
            )

        except Exception as e:
            logger.error(f"服务调用失败: {service_name} - {e}")
            raise

class ServiceLoadBalancer:
    """服务负载均衡器"""

    def __init__(self):
        self.provider_weights: Dict[str, Dict[str, float]] = {}
        self.provider_metrics: Dict[str, Dict[str, ServiceMetrics]] = {}

    def update_provider_weight(self, service_name: str, provider: str, weight: float):
        """更新提供商权重"""
        if service_name not in self.provider_weights:
            self.provider_weights[service_name] = {}
        self.provider_weights[service_name][provider] = weight

    def get_best_provider(self, service_name: str, available_providers: List[str]) -> str:
        """获取最佳提供商"""
        if not available_providers:
            return None

        if len(available_providers) == 1:
            return available_providers[0]

        # 基于权重和性能指标选择
        weights = self.provider_weights.get(service_name, {})

        best_provider = available_providers[0]
        best_score = 0.0

        for provider in available_providers:
            # 计算综合得分
            weight = weights.get(provider, 1.0)

            # 这里可以添加更复杂的评分逻辑
            # 例如基于响应时间、成功率等
            score = weight

            if score > best_score:
                best_score = score
                best_provider = provider

        return best_provider

# 全局实例
service_monitor = ServiceHealthMonitor()
retry_manager = RetryManager()
degradation_manager = ServiceDegradationManager()
service_orchestrator = ServiceOrchestrator()
