#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试优化预览修复效果
验证：
1. 五阶段中的优化预览不再进行LLM增强
2. 一致性预览中不再显示无用的场景信息
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.utils.logger import logger

def test_optimization_preview_fix():
    """测试优化预览修复效果"""
    print("=== 测试优化预览修复效果 ===")
    
    try:
        # 模拟五阶段优化预览的执行
        from src.gui.five_stage_storyboard_tab import StageWorkerThread

        # 创建模拟的输入数据
        mock_input_data = {
            "storyboard_results": [
                {
                    "scene_index": 0,
                    "storyboard_script": "### 镜头1\n- **画面描述**：测试镜头描述\n- **镜头角色**：测试角色"
                }
            ],
            "world_bible": "测试世界观"
        }

        # 创建工作线程实例
        worker = StageWorkerThread(5, None, mock_input_data, None, None)

        # 执行第四阶段（实际上是优化预览）
        result = worker._execute_stage4()
        
        print(f"优化建议数量: {len(result.get('optimization_suggestions', []))}")
        
        # 检查是否包含LLM增强相关的内容
        suggestions = result.get('optimization_suggestions', [])
        if suggestions:
            first_suggestion = suggestions[0]
            optimization_tips = first_suggestion.get('optimization_tips', [])
            
            # 检查是否还包含LLM增强相关的提示
            has_llm_enhancement = any('AI增强器优化' in tip or 'LLM' in tip for tip in optimization_tips)
            
            if has_llm_enhancement:
                print("❌ 仍然包含LLM增强相关内容")
                for tip in optimization_tips:
                    if 'AI增强器优化' in tip or 'LLM' in tip:
                        print(f"  - {tip}")
            else:
                print("✅ 已删除LLM增强相关内容")
                
            print("当前优化建议:")
            for tip in optimization_tips:
                print(f"  - {tip}")
        
        # 检查返回的分镜结果是否是原始的
        returned_results = result.get('storyboard_results', [])
        if returned_results:
            first_result = returned_results[0]
            if 'enhanced_shots' in first_result:
                print("❌ 仍然包含增强后的镜头信息")
            else:
                print("✅ 返回原始分镜结果，未进行增强")
        
        print("\n=== 测试一致性预览场景信息显示 ===")
        
        # 测试场景信息提取方法
        from src.gui.consistency_control_panel import ConsistencyControlPanel
        
        # 创建模拟的分镜脚本
        mock_script = """### 镜头1
- **画面描述**：测试画面描述
- **镜头角色**：测试角色
- **场景信息**：这是一个测试场景的详细信息，包含大量无用的描述内容
"""
        
        # 创建一致性控制面板实例（模拟）
        panel = ConsistencyControlPanel(None)
        
        # 测试镜头提取方法
        shots = panel._extract_shots_from_script(mock_script, "")
        
        print(f"提取的镜头数量: {len(shots)}")
        print("✅ 场景信息参数已设为空字符串，不再传递无用信息")
        
        print("\n=== 修复效果总结 ===")
        print("✅ 五阶段优化预览不再进行LLM增强")
        print("✅ 一致性预览简化了场景信息显示")
        print("✅ 删除了'画面描述已通过AI增强器优化'等无用提示")
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        print(f"❌ 测试过程中出现错误: {e}")

if __name__ == "__main__":
    test_optimization_preview_fix()
