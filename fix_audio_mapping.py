#!/usr/bin/env python3
"""修复音频路径映射"""

import json
import os

def main():
    project_file = r"D:\AI_Video_Generator\output\天下无双\project.json"
    
    # 备份原文件
    backup_file = project_file + ".backup_audio_fix"
    with open(project_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    with open(backup_file, 'w', encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=2)
    print(f"已备份原文件到: {backup_file}")

    # 修复音频路径映射
    voice_segments = data.get('voice_generation', {}).get('voice_segments', [])
    print(f'总共有 {len(voice_segments)} 个配音段落')

    audio_dir = r"D:\AI_Video_Generator\output\天下无双\audio\edge_tts"
    
    # 修复每个段落的音频路径
    for i, segment in enumerate(voice_segments):
        # 计算正确的音频文件名
        segment_num = i + 1  # 段落编号从1开始
        shot_id = segment.get("shot_id", f"镜头{segment_num}")
        
        # 生成正确的音频文件名
        correct_filename = f"segment_{segment_num:03d}_{shot_id}.mp3"
        correct_path = os.path.join(audio_dir, correct_filename)
        
        # 检查文件是否存在
        if os.path.exists(correct_path):
            old_path = segment.get("audio_path", "")
            segment["audio_path"] = correct_path
            print(f'段落{segment_num:2d}: 修复音频路径')
            print(f'  旧路径: {os.path.basename(old_path)}')
            print(f'  新路径: {correct_filename}')
        else:
            print(f'段落{segment_num:2d}: ⚠️  音频文件不存在: {correct_filename}')

    # 保存修复后的数据
    with open(project_file, 'w', encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=2)
    
    print(f'\n✅ 音频路径映射已修复并保存到: {project_file}')
    print(f'📁 原文件备份在: {backup_file}')

if __name__ == "__main__":
    main()
