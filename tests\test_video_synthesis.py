#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
视频合成功能测试脚本
"""

import os
import sys
import asyncio
import tempfile
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.processors.video_synthesis_processor import (
    VideoSynthesisProcessor, SynthesisProject, SynthesisConfig,
    VideoSegment, AudioSegment, ImageSegment, TransitionEffect
)
from src.utils.logger import logger

def create_test_image(file_path: str, width: int = 1920, height: int = 1080, color: tuple = (255, 0, 0)):
    """创建测试图像"""
    try:
        from PIL import Image
        
        # 创建纯色图像
        image = Image.new('RGB', (width, height), color)
        image.save(file_path)
        return True
    except ImportError:
        logger.warning("PIL不可用，无法创建测试图像")
        return False

def create_test_audio(file_path: str, duration: float = 5.0, frequency: int = 440):
    """创建测试音频"""
    try:
        import numpy as np
        import wave
        
        # 生成正弦波音频
        sample_rate = 44100
        samples = int(sample_rate * duration)
        t = np.linspace(0, duration, samples, False)
        audio_data = np.sin(2 * np.pi * frequency * t)
        
        # 转换为16位整数
        audio_data = (audio_data * 32767).astype(np.int16)
        
        # 保存为WAV文件
        with wave.open(file_path, 'w') as wav_file:
            wav_file.setnchannels(1)  # 单声道
            wav_file.setsampwidth(2)  # 16位
            wav_file.setframerate(sample_rate)
            wav_file.writeframes(audio_data.tobytes())
        
        return True
    except ImportError:
        logger.warning("NumPy不可用，无法创建测试音频")
        return False

async def test_basic_synthesis():
    """测试基础视频合成功能"""
    print("🧪 开始测试基础视频合成功能...")
    
    # 创建临时目录
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # 创建测试文件
        image1_path = temp_path / "test_image1.png"
        image2_path = temp_path / "test_image2.png"
        audio_path = temp_path / "test_audio.wav"
        
        print("📁 创建测试文件...")
        
        # 创建测试图像
        if not create_test_image(str(image1_path), color=(255, 0, 0)):  # 红色
            print("❌ 无法创建测试图像，跳过测试")
            return False
        
        if not create_test_image(str(image2_path), color=(0, 255, 0)):  # 绿色
            print("❌ 无法创建测试图像，跳过测试")
            return False
        
        # 创建测试音频
        if not create_test_audio(str(audio_path)):
            print("⚠️ 无法创建测试音频，继续测试（无音频）")
        
        try:
            # 创建视频合成处理器
            processor = VideoSynthesisProcessor(output_dir=str(temp_path))
            
            # 创建项目
            config = SynthesisConfig(
                output_width=1280,
                output_height=720,
                fps=24,
                quality_preset="fast"  # 使用快速预设以加快测试
            )
            
            project = processor.create_project("test_project", config)
            print(f"✅ 项目已创建: {project.name}")
            
            # 添加图像片段
            processor.add_image_segment(project, str(image1_path), 0.0, 3.0)
            processor.add_image_segment(project, str(image2_path), 3.0, 3.0)
            print("✅ 图像片段已添加")
            
            # 添加音频片段（如果可用）
            if audio_path.exists():
                processor.add_audio_segment(project, str(audio_path), 0.0, 5.0)
                print("✅ 音频片段已添加")
            
            # 添加转场效果
            transition = TransitionEffect("fade", 0.5)
            processor.add_transition(project, transition)
            print("✅ 转场效果已添加")
            
            # 保存项目
            project_file = processor.save_project(project)
            print(f"✅ 项目已保存: {project_file}")
            
            # 加载项目测试
            loaded_project = processor.load_project(project_file)
            print(f"✅ 项目已加载: {loaded_project.name}")
            
            # 获取项目信息
            info = processor.get_project_info(loaded_project)
            print(f"📊 项目信息: {info}")
            
            # 检查MoviePy是否可用
            try:
                import moviepy
                print("✅ MoviePy可用，可以进行视频合成")
                
                # 进度回调函数
                def progress_callback(progress: float, message: str):
                    print(f"📈 合成进度: {progress*100:.1f}% - {message}")
                
                # 执行视频合成
                print("🎬 开始视频合成...")
                output_path = await processor.synthesize_video(
                    loaded_project, 
                    "test_output.mp4",
                    progress_callback
                )
                
                print(f"✅ 视频合成完成: {output_path}")
                
                # 检查输出文件
                if os.path.exists(output_path):
                    file_size = os.path.getsize(output_path)
                    print(f"📁 输出文件大小: {file_size / 1024 / 1024:.2f} MB")
                    return True
                else:
                    print("❌ 输出文件不存在")
                    return False
                    
            except ImportError:
                print("⚠️ MoviePy不可用，跳过视频合成测试")
                print("💡 安装MoviePy: pip install moviepy")
                return True  # 其他功能测试通过
                
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            logger.error(f"视频合成测试失败: {e}")
            return False

async def test_error_handling():
    """测试错误处理"""
    print("\n🧪 开始测试错误处理...")
    
    processor = VideoSynthesisProcessor()
    
    try:
        # 测试添加不存在的文件
        project = processor.create_project("error_test")
        
        try:
            processor.add_video_segment(project, "nonexistent.mp4", 0.0)
            print("❌ 应该抛出FileNotFoundError")
            return False
        except FileNotFoundError:
            print("✅ 正确处理了不存在的文件")
        
        try:
            processor.add_audio_segment(project, "nonexistent.mp3", 0.0)
            print("❌ 应该抛出FileNotFoundError")
            return False
        except FileNotFoundError:
            print("✅ 正确处理了不存在的音频文件")
        
        try:
            processor.add_image_segment(project, "nonexistent.jpg", 0.0, 5.0)
            print("❌ 应该抛出FileNotFoundError")
            return False
        except FileNotFoundError:
            print("✅ 正确处理了不存在的图像文件")
        
        print("✅ 错误处理测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 错误处理测试失败: {e}")
        return False

def test_project_serialization():
    """测试项目序列化"""
    print("\n🧪 开始测试项目序列化...")
    
    try:
        with tempfile.TemporaryDirectory() as temp_dir:
            processor = VideoSynthesisProcessor(output_dir=temp_dir)
            
            # 创建复杂项目
            config = SynthesisConfig(
                output_width=1920,
                output_height=1080,
                fps=30,
                enable_transitions=True
            )
            
            project = processor.create_project("serialization_test", config)
            
            # 保存和加载项目
            project_file = processor.save_project(project)
            loaded_project = processor.load_project(project_file)
            
            # 验证数据一致性
            assert project.name == loaded_project.name
            assert project.config.output_width == loaded_project.config.output_width
            assert project.config.output_height == loaded_project.config.output_height
            assert project.config.fps == loaded_project.config.fps
            assert project.config.enable_transitions == loaded_project.config.enable_transitions
            
            print("✅ 项目序列化测试通过")
            return True
            
    except Exception as e:
        print(f"❌ 项目序列化测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("🎬 AI视频生成器 - 视频合成功能测试")
    print("=" * 50)
    
    test_results = []
    
    # 运行测试
    test_results.append(await test_basic_synthesis())
    test_results.append(await test_error_handling())
    test_results.append(test_project_serialization())
    
    # 汇总结果
    print("\n" + "=" * 50)
    print("📊 测试结果汇总:")
    
    passed = sum(test_results)
    total = len(test_results)
    
    print(f"✅ 通过: {passed}/{total}")
    print(f"❌ 失败: {total - passed}/{total}")
    
    if passed == total:
        print("🎉 所有测试通过！视频合成功能正常工作。")
        return True
    else:
        print("⚠️ 部分测试失败，请检查相关功能。")
        return False

if __name__ == "__main__":
    # 运行测试
    result = asyncio.run(main())
    sys.exit(0 if result else 1)
