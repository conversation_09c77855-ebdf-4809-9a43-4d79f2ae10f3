"""
程序运行状态检测脚本
检测AI视频生成器各个模块的运行状况
"""

import sys
import os
import logging
import traceback
from pathlib import Path

# 添加项目根目录到路径
sys.path.append('.')

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_imports():
    """测试核心模块导入"""
    print("🔍 测试核心模块导入...")
    
    import_tests = [
        ("PyQt5核心", "from PyQt5.QtWidgets import QApplication, QMainWindow"),
        ("项目管理器", "from src.core.project_manager import ProjectManager"),
        ("应用控制器", "from src.core.app_controller import AppController"),
        ("配音生成", "from src.gui.voice_generation_tab import VoiceGenerationTab"),
        ("分镜图像生成", "from src.gui.storyboard_image_generation_tab import StoryboardImageGenerationTab"),
        ("五阶段分镜", "from src.gui.five_stage_storyboard_tab import FiveStageStoryboardTab"),
        ("一致性控制", "from src.gui.consistency_control_panel import ConsistencyControlPanel"),
        ("配音驱动分镜", "from src.core.voice_driven_storyboard import VoiceDrivenStoryboardSystem"),
        ("工作流程指导", "from src.gui.workflow_guide_widget import WorkflowGuideWidget"),
        ("主界面", "from src.gui.new_main_window import NewMainWindow")
    ]
    
    results = []
    for name, import_code in import_tests:
        try:
            exec(import_code)
            print(f"   ✅ {name}: 导入成功")
            results.append((name, True, None))
        except Exception as e:
            print(f"   ❌ {name}: 导入失败 - {str(e)}")
            results.append((name, False, str(e)))
    
    return results

def test_project_structure():
    """测试项目结构"""
    print("\n📁 测试项目结构...")
    
    required_paths = [
        "src/core/project_manager.py",
        "src/core/app_controller.py",
        "src/core/voice_driven_storyboard.py",
        "src/gui/new_main_window.py",
        "src/gui/voice_generation_tab.py",
        "src/gui/storyboard_image_generation_tab.py",
        "src/gui/five_stage_storyboard_tab.py",
        "src/gui/consistency_control_panel.py",
        "src/gui/workflow_guide_widget.py",
        "src/utils/project_data_optimizer.py",
        "output",
        "config"
    ]
    
    results = []
    for path in required_paths:
        if os.path.exists(path):
            print(f"   ✅ {path}: 存在")
            results.append((path, True))
        else:
            print(f"   ❌ {path}: 不存在")
            results.append((path, False))
    
    return results

def test_configuration():
    """测试配置文件"""
    print("\n⚙️ 测试配置文件...")
    
    config_files = [
        "config/config.json",
        "config/llm_config.json",
        "config/image_generation_config.json"
    ]
    
    results = []
    for config_file in config_files:
        try:
            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    import json
                    json.load(f)
                print(f"   ✅ {config_file}: 存在且格式正确")
                results.append((config_file, True, None))
            else:
                print(f"   ⚠️ {config_file}: 不存在")
                results.append((config_file, False, "文件不存在"))
        except Exception as e:
            print(f"   ❌ {config_file}: 格式错误 - {str(e)}")
            results.append((config_file, False, str(e)))
    
    return results

def test_voice_driven_workflow():
    """测试配音驱动工作流程"""
    print("\n🎭 测试配音驱动工作流程...")
    
    try:
        from src.core.voice_driven_storyboard import VoiceDrivenStoryboardSystem
        
        # 创建测试实例
        voice_system = VoiceDrivenStoryboardSystem()
        print("   ✅ 配音驱动分镜系统: 创建成功")
        
        # 测试配音数据加载
        test_voice_data = [
            {
                'index': 0,
                'dialogue_text': '测试配音内容',
                'audio_path': 'test.mp3',
                'duration': 3.0,
                'content_type': '旁白',
                'sound_effect': '',
                'status': '已生成'
            }
        ]
        
        if voice_system.load_voice_data(test_voice_data):
            print("   ✅ 配音数据加载: 成功")
        else:
            print("   ❌ 配音数据加载: 失败")
            return False
        
        # 测试场景分析
        if voice_system.analyze_voice_driven_scenes():
            print("   ✅ 配音场景分析: 成功")
        else:
            print("   ❌ 配音场景分析: 失败")
            return False
        
        # 测试分镜生成
        if voice_system.generate_voice_driven_storyboard():
            print("   ✅ 配音驱动分镜生成: 成功")
        else:
            print("   ❌ 配音驱动分镜生成: 失败")
            return False
        
        print("   🎉 配音驱动工作流程: 完全正常")
        return True
        
    except Exception as e:
        print(f"   💥 配音驱动工作流程测试失败: {e}")
        traceback.print_exc()
        return False

def test_existing_projects():
    """测试现有项目"""
    print("\n📋 测试现有项目...")
    
    output_dir = Path("output")
    if not output_dir.exists():
        print("   ⚠️ output目录不存在")
        return []
    
    projects = []
    for project_dir in output_dir.iterdir():
        if project_dir.is_dir():
            project_json = project_dir / "project.json"
            if project_json.exists():
                try:
                    with open(project_json, 'r', encoding='utf-8') as f:
                        import json
                        project_data = json.load(f)
                    
                    # 检查项目数据结构
                    has_voice_data = 'voice_generation' in project_data
                    has_storyboard_data = 'five_stage_storyboard' in project_data
                    has_workflow_settings = 'workflow_settings' in project_data
                    
                    print(f"   📁 {project_dir.name}:")
                    print(f"      配音数据: {'✅' if has_voice_data else '❌'}")
                    print(f"      分镜数据: {'✅' if has_storyboard_data else '❌'}")
                    print(f"      工作流程设置: {'✅' if has_workflow_settings else '❌'}")
                    
                    projects.append({
                        'name': project_dir.name,
                        'has_voice_data': has_voice_data,
                        'has_storyboard_data': has_storyboard_data,
                        'has_workflow_settings': has_workflow_settings
                    })
                    
                except Exception as e:
                    print(f"   ❌ {project_dir.name}: 项目文件损坏 - {str(e)}")
                    projects.append({
                        'name': project_dir.name,
                        'error': str(e)
                    })
            else:
                print(f"   ⚠️ {project_dir.name}: 缺少project.json文件")
    
    return projects

def test_ui_components():
    """测试UI组件"""
    print("\n🖥️ 测试UI组件...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        import sys
        
        # 创建QApplication实例（如果不存在）
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        print("   ✅ QApplication: 创建成功")
        
        # 测试工作流程指导组件
        from src.gui.workflow_guide_widget import WorkflowGuideWidget
        workflow_guide = WorkflowGuideWidget()
        print("   ✅ 工作流程指导组件: 创建成功")
        
        # 测试主界面组件（不显示）
        from src.core.app_controller import AppController
        from src.core.project_manager import ProjectManager
        
        app_controller = AppController()
        project_manager = ProjectManager()
        
        print("   ✅ 应用控制器: 创建成功")
        print("   ✅ 项目管理器: 创建成功")
        
        return True
        
    except Exception as e:
        print(f"   💥 UI组件测试失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🔍 AI视频生成器 - 程序运行状态检测")
    print("=" * 60)
    
    # 运行所有测试
    import_results = test_imports()
    structure_results = test_project_structure()
    config_results = test_configuration()
    voice_workflow_ok = test_voice_driven_workflow()
    project_results = test_existing_projects()
    ui_components_ok = test_ui_components()
    
    # 统计结果
    print("\n" + "=" * 60)
    print("📊 检测结果总结")
    print("=" * 60)
    
    # 导入测试结果
    import_success = sum(1 for _, success, _ in import_results if success)
    import_total = len(import_results)
    print(f"📦 模块导入: {import_success}/{import_total} 成功")
    
    # 项目结构结果
    structure_success = sum(1 for _, success in structure_results if success)
    structure_total = len(structure_results)
    print(f"📁 项目结构: {structure_success}/{structure_total} 完整")
    
    # 配置文件结果
    config_success = sum(1 for _, success, _ in config_results if success)
    config_total = len(config_results)
    print(f"⚙️ 配置文件: {config_success}/{config_total} 正常")
    
    # 配音驱动工作流程
    print(f"🎭 配音驱动工作流程: {'✅ 正常' if voice_workflow_ok else '❌ 异常'}")
    
    # UI组件
    print(f"🖥️ UI组件: {'✅ 正常' if ui_components_ok else '❌ 异常'}")
    
    # 现有项目
    if project_results:
        valid_projects = sum(1 for p in project_results if 'error' not in p)
        print(f"📋 现有项目: {valid_projects}/{len(project_results)} 正常")
    else:
        print("📋 现有项目: 无项目")
    
    # 总体评估
    print("\n🎯 总体状态评估:")
    
    critical_issues = []
    if import_success < import_total:
        critical_issues.append("模块导入问题")
    if not voice_workflow_ok:
        critical_issues.append("配音驱动工作流程问题")
    if not ui_components_ok:
        critical_issues.append("UI组件问题")
    
    if not critical_issues:
        print("🎉 程序运行状态良好，所有核心功能正常！")
        print("\n✅ 可以正常使用的功能:")
        print("   • 配音驱动工作流程")
        print("   • 五阶段分镜生成")
        print("   • 图像生成")
        print("   • 配音生成")
        print("   • 视频合成")
        print("   • 工作流程指导")
    else:
        print("⚠️ 发现以下关键问题:")
        for issue in critical_issues:
            print(f"   • {issue}")
        print("\n🔧 建议:")
        print("   1. 检查Python环境和依赖包")
        print("   2. 确保所有必需文件存在")
        print("   3. 查看详细错误信息进行修复")
    
    print("\n" + "=" * 60)
    print("检测完成！")
    print("=" * 60)

if __name__ == "__main__":
    main()
