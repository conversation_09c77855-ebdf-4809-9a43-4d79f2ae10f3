# 分镜内容丢失问题修复方案

## 📊 问题分析结果

通过详细分析，发现了分镜系统的核心问题：

### 🔍 测试数据分析
- **原文总长度**: 1396字符，45个句子
- **分镜覆盖长度**: 1029字符，34个镜头
- **覆盖率**: 仅73.71%
- **遗漏句子**: 24个重要句子
- **场景4**: 完全没有内容 (0%覆盖率)

### 🎯 根本原因

**这不是大模型能力问题，而是系统设计问题**：

1. **提示词不够强制性** - 缺乏严格的覆盖要求
2. **缺乏验证机制** - 没有检查生成结果的完整性
3. **场景分割问题** - 某些场景的原文没有正确传递
4. **数据流程缺陷** - project.json和prompt.json数据不同步

## 🔧 已实施的修复

### 1. 强化提示词设计

**修复前**：
```
请为这个场景创建详细的分镜头脚本。每个镜头都应该有明确的视觉目标和技术参数。
```

**修复后**：
```
你是一位专业的分镜师。你的任务是将以下原文内容100%完整地转换为分镜脚本。

🚨 绝对要求 - 违反将被拒绝：
1. 必须逐句覆盖所有原文，不能遗漏任何内容
2. 严格按照原文顺序进行分镜
3. 不能添加原文中没有的任何内容
4. 每个镜头必须包含具体的原文句子

原文句子分解（共X句）：
1. 句子1
2. 句子2
...

验证检查：
生成完成后，请确认：
- 是否所有X个句子都被包含？
- 是否没有添加原文以外的内容？
- 是否每个镜头都有实质内容？
```

### 2. 智能句子分割算法

```python
def _split_text_into_sentences(self, text):
    """将文本按句子分割"""
    import re
    
    # 按中文句号、问号、感叹号分割
    sentences = re.split(r'[。！？]', text)
    
    # 清理空句子和过短的句子
    cleaned_sentences = []
    for sentence in sentences:
        sentence = sentence.strip()
        if len(sentence) > 5:  # 过滤掉过短的片段
            cleaned_sentences.append(sentence + '。')  # 重新添加句号
    
    return cleaned_sentences
```

### 3. 智能镜头数量计算

```python
# 根据句子数量智能计算镜头数量
if total_sentences <= 3:
    suggested_shots = total_sentences
elif total_sentences <= 6:
    suggested_shots = max(3, total_sentences // 2 + 1)
elif total_sentences <= 10:
    suggested_shots = max(4, total_sentences // 2)
else:
    suggested_shots = max(6, min(10, total_sentences // 2))
```

### 4. 内容覆盖验证机制

```python
def _validate_content_coverage(self, storyboard_response, original_text, original_sentences):
    """验证分镜脚本是否完整覆盖了原文内容"""
    
    # 提取分镜中的所有"镜头原文"内容
    shot_texts = re.findall(r'- \*\*镜头原文\*\*：([^\n]+)', storyboard_response)
    
    # 合并所有镜头原文
    covered_text = "".join([text.strip() for text in shot_texts])
    
    # 计算覆盖率
    coverage_ratio = len(covered_text) / len(original_text)
    
    # 检查遗漏的句子
    missing_sentences = []
    for sentence in original_sentences:
        if sentence.strip() and sentence.strip() not in covered_text:
            missing_sentences.append(sentence.strip())
    
    # 判断是否完整（覆盖率>=90%且遗漏句子<=2个）
    is_complete = coverage_ratio >= 0.9 and len(missing_sentences) <= 2
    
    return {
        'is_complete': is_complete,
        'coverage_ratio': coverage_ratio,
        'missing_count': len(missing_sentences),
        'missing_sentences': missing_sentences
    }
```

### 5. 修复数据源调用问题

**问题**: 系统仍在调用不存在的prompt.json文件

**修复**: 
- 添加了从project.json加载分镜数据的备选方案
- 统一了数据源，优先使用project.json
- 修复了"prompt.json文件不存在，无法加载分镜数据"的警告

## 🎯 建议的使用流程

### 重新生成分镜的步骤：

1. **清理旧数据**：
   - 删除 `output/成语/storyboard/` 目录下的所有文件
   - 清理 `project.json` 中的分镜数据

2. **重新进行场景分割**：
   - 进入五阶段分镜界面
   - 重新执行第3阶段"场景分割"
   - 确保每个场景都包含足够的原文内容

3. **重新生成分镜**：
   - 进入第4阶段"分镜生成"
   - 选择所有场景
   - 点击"生成分镜"
   - 系统将使用新的强化算法

4. **验证结果**：
   - 检查生成的分镜文件
   - 确认覆盖率是否达到90%以上
   - 检查是否有遗漏的重要内容

## 💡 进一步优化建议

### 1. 提示词优化
如果覆盖率仍然不理想，可以考虑：
- 使用更强制性的语言
- 添加示例格式
- 增加惩罚机制描述

### 2. 分批处理
对于超长文本：
- 可以将场景进一步细分
- 每个场景控制在200-300字以内
- 确保每个场景都有完整的情节

### 3. 人工审核
- 在自动生成后添加人工审核步骤
- 标记遗漏的内容
- 手动补充缺失的镜头

### 4. 迭代优化
- 如果第一次生成不完整，可以针对遗漏部分重新生成
- 逐步完善直到达到100%覆盖

## 🔄 测试验证

使用提供的测试脚本可以验证修复效果：

```python
# 运行内容覆盖测试
python test_content_coverage.py
```

**期望结果**：
- 覆盖率 > 90%
- 遗漏句子 < 3个
- 所有场景都有内容

## 📈 预期改进效果

修复后的系统应该能够：

1. **完整覆盖原文** - 覆盖率从73.71%提升到90%+
2. **减少内容遗漏** - 遗漏句子从24个减少到3个以内
3. **消除空场景** - 所有场景都有实质内容
4. **提高生成质量** - 分镜更贴近原文，更有逻辑性

## 🚀 总结

这次修复主要解决了：
1. ✅ 提示词设计问题 - 使用更强制性的要求
2. ✅ 验证机制缺失 - 添加了覆盖率检查
3. ✅ 数据源调用错误 - 修复了prompt.json问题
4. ✅ 算法优化 - 智能计算镜头数量和分配

**关键改进**：从"建议性"的提示词改为"强制性"的要求，并添加了验证机制确保质量。

---

**修复完成时间**: 2025-06-23  
**主要修复文件**: `src/gui/five_stage_storyboard_tab.py`, `src/gui/storyboard_image_generation_tab.py`  
**测试覆盖率**: 预期从73.71%提升到90%+
