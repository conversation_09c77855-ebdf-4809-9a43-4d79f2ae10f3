#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复project.json中缺失的technical_details数据
从enhancement_progress.json中提取完整的36个镜头数据并更新到project.json
"""

import json
import os
import shutil
from datetime import datetime

def fix_technical_details():
    """修复project.json中的technical_details数据"""
    
    # 文件路径
    project_dir = "output/国外故事"
    enhancement_file = os.path.join(project_dir, "enhancement_progress.json")
    project_file = os.path.join(project_dir, "project.json")
    backup_file = os.path.join(project_dir, f"project_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
    
    print(f"开始修复technical_details数据...")
    print(f"增强数据文件: {enhancement_file}")
    print(f"项目文件: {project_file}")
    
    # 检查文件是否存在
    if not os.path.exists(enhancement_file):
        print(f"错误: 找不到增强数据文件 {enhancement_file}")
        return False
        
    if not os.path.exists(project_file):
        print(f"错误: 找不到项目文件 {project_file}")
        return False
    
    try:
        # 读取enhancement_progress.json
        with open(enhancement_file, 'r', encoding='utf-8') as f:
            enhancement_data = json.load(f)
        
        # 读取project.json
        with open(project_file, 'r', encoding='utf-8') as f:
            project_data = json.load(f)
        
        # 创建备份
        shutil.copy2(project_file, backup_file)
        print(f"已创建备份文件: {backup_file}")
        
        # 提取technical_details数据
        technical_details_map = {}
        
        if 'enhanced_results' in enhancement_data:
            for result in enhancement_data['enhanced_results']:
                if 'enhanced_result' in result and 'enhanced_details' in result['enhanced_result']:
                    for detail in result['enhanced_result']['enhanced_details']:
                        if 'technical_details' in detail and 'shot_info' in detail:
                            shot_info = detail['shot_info']
                            shot_number = shot_info.get('镜头编号', '')
                            if shot_number:
                                technical_details_map[shot_number] = detail['technical_details']
        
        print(f"从enhancement_progress.json中提取到 {len(technical_details_map)} 个镜头的technical_details")
        
        # 更新project.json中的technical_details
        updated_count = 0
        created_count = 0

        # 确保enhanced_descriptions存在
        if 'enhanced_descriptions' not in project_data:
            project_data['enhanced_descriptions'] = {}

        # 更新enhanced_descriptions中的technical_details
        for shot_number, technical_details in technical_details_map.items():
            if shot_number in project_data['enhanced_descriptions']:
                # 更新现有条目
                project_data['enhanced_descriptions'][shot_number]['technical_details'] = technical_details
                updated_count += 1
                print(f"更新 {shot_number} 的technical_details")
            else:
                # 创建新条目（为缺失的镜头12-36）
                project_data['enhanced_descriptions'][shot_number] = {
                    "shot_number": shot_number,
                    "scene": "",
                    "original_prompt": "",
                    "enhanced_prompt": "",
                    "technical_details": technical_details,
                    "consistency_info": "",
                    "characters": [],
                    "fusion_quality_score": 0.0
                }
                created_count += 1
                print(f"创建 {shot_number} 的enhanced_descriptions条目")

        # 同时更新shot_image_mappings中的technical_details（如果存在）
        if 'shot_image_mappings' in project_data:
            for shot_key, shot_data in project_data['shot_image_mappings'].items():
                if isinstance(shot_data, dict) and 'shot_name' in shot_data:
                    shot_number = shot_data['shot_name']  # 使用shot_name而不是shot_number
                    if shot_number in technical_details_map:
                        shot_data['technical_details'] = technical_details_map[shot_number]
                        print(f"同时更新shot_image_mappings中 {shot_key} ({shot_number}) 的technical_details")
        
        print(f"成功更新了 {updated_count} 个镜头的technical_details")
        print(f"成功创建了 {created_count} 个新的enhanced_descriptions条目")
        
        # 保存更新后的project.json
        with open(project_file, 'w', encoding='utf-8') as f:
            json.dump(project_data, f, ensure_ascii=False, indent=2)
        
        print(f"已保存更新后的project.json")
        print(f"修复完成！")
        
        return True
        
    except Exception as e:
        print(f"修复过程中出现错误: {str(e)}")
        return False

if __name__ == "__main__":
    success = fix_technical_details()
    if success:
        print("technical_details数据修复成功！")
    else:
        print("technical_details数据修复失败！")
