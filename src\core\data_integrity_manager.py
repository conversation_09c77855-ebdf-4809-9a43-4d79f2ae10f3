#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据一致性管理器
提供数据完整性检查、自动备份、事务性操作、数据恢复等功能
"""

import os
import json
import time
import hashlib
import threading
import shutil
from typing import Dict, List, Optional, Any, Callable, Tuple
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime, timedelta
from pathlib import Path
from contextlib import contextmanager

from PyQt5.QtCore import QObject, pyqtSignal, QTimer

from src.utils.logger import logger
from src.utils.enhanced_error_handler import enhanced_error_handler, ErrorDomain

class DataIntegrityLevel(Enum):
    """数据完整性级别"""
    BASIC = "basic"           # 基础检查
    STANDARD = "standard"     # 标准检查
    STRICT = "strict"         # 严格检查
    PARANOID = "paranoid"     # 偏执检查

class BackupType(Enum):
    """备份类型"""
    MANUAL = "manual"         # 手动备份
    AUTO = "auto"            # 自动备份
    TRANSACTION = "transaction"  # 事务备份
    CHECKPOINT = "checkpoint"    # 检查点备份

@dataclass
class DataChecksum:
    """数据校验和"""
    file_path: str
    checksum: str
    size: int
    modified_time: float
    created_time: datetime = field(default_factory=datetime.now)

@dataclass
class BackupRecord:
    """备份记录"""
    backup_id: str
    backup_type: BackupType
    source_path: str
    backup_path: str
    checksum: str
    size: int
    created_time: datetime
    description: str = ""
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class IntegrityCheckResult:
    """完整性检查结果"""
    is_valid: bool
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    corrupted_files: List[str] = field(default_factory=list)
    missing_files: List[str] = field(default_factory=list)
    checksum_mismatches: List[str] = field(default_factory=list)

class DataIntegrityManager(QObject):
    """数据完整性管理器"""
    
    # 信号
    integrity_check_completed = pyqtSignal(bool)  # is_valid
    backup_created = pyqtSignal(str)  # backup_id
    data_corruption_detected = pyqtSignal(str)  # file_path
    auto_recovery_completed = pyqtSignal(bool)  # success
    
    def __init__(self, project_root: str = None):
        super().__init__()
        
        self.project_root = Path(project_root) if project_root else Path.cwd()
        self.integrity_dir = self.project_root / ".integrity"
        self.integrity_dir.mkdir(exist_ok=True)
        
        # 配置文件
        self.config_file = self.integrity_dir / "integrity_config.json"
        self.checksums_file = self.integrity_dir / "checksums.json"
        self.backups_file = self.integrity_dir / "backups.json"
        
        # 数据存储
        self.checksums: Dict[str, DataChecksum] = {}
        self.backup_records: Dict[str, BackupRecord] = {}
        self.config = self._load_config()
        
        # 锁机制
        self._lock = threading.RLock()
        self._transaction_lock = threading.Lock()
        
        # 自动备份定时器
        self.auto_backup_timer = QTimer()
        self.auto_backup_timer.timeout.connect(self._perform_auto_backup)
        
        # 完整性检查定时器
        self.integrity_check_timer = QTimer()
        self.integrity_check_timer.timeout.connect(self._perform_integrity_check)
        
        # 加载现有数据
        self._load_checksums()
        self._load_backup_records()
        
        # 启动定时器
        self._start_timers()
        
        logger.info(f"数据完整性管理器初始化完成: {self.project_root}")
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置"""
        default_config = {
            "integrity_level": DataIntegrityLevel.STANDARD.value,
            "auto_backup_enabled": True,
            "auto_backup_interval": 300,  # 5分钟
            "integrity_check_interval": 600,  # 10分钟
            "max_backups": 10,
            "backup_compression": True,
            "watched_files": [
                "project.json",
                "config/*.json",
                "src/**/*.py"
            ],
            "critical_files": [
                "project.json"
            ]
        }
        
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                # 合并默认配置
                for key, value in default_config.items():
                    if key not in config:
                        config[key] = value
                return config
            else:
                self._save_config(default_config)
                return default_config
        except Exception as e:
            logger.error(f"加载完整性配置失败: {e}")
            return default_config
    
    def _save_config(self, config: Dict[str, Any]):
        """保存配置"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"保存完整性配置失败: {e}")
    
    def _load_checksums(self):
        """加载校验和数据"""
        try:
            if self.checksums_file.exists():
                with open(self.checksums_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                for file_path, checksum_data in data.items():
                    self.checksums[file_path] = DataChecksum(
                        file_path=checksum_data['file_path'],
                        checksum=checksum_data['checksum'],
                        size=checksum_data['size'],
                        modified_time=checksum_data['modified_time'],
                        created_time=datetime.fromisoformat(checksum_data['created_time'])
                    )
        except Exception as e:
            logger.error(f"加载校验和数据失败: {e}")
    
    def _save_checksums(self):
        """保存校验和数据"""
        try:
            data = {}
            for file_path, checksum in self.checksums.items():
                data[file_path] = {
                    'file_path': checksum.file_path,
                    'checksum': checksum.checksum,
                    'size': checksum.size,
                    'modified_time': checksum.modified_time,
                    'created_time': checksum.created_time.isoformat()
                }
            
            with open(self.checksums_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"保存校验和数据失败: {e}")
    
    def _load_backup_records(self):
        """加载备份记录"""
        try:
            if self.backups_file.exists():
                with open(self.backups_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                for backup_id, record_data in data.items():
                    self.backup_records[backup_id] = BackupRecord(
                        backup_id=record_data['backup_id'],
                        backup_type=BackupType(record_data['backup_type']),
                        source_path=record_data['source_path'],
                        backup_path=record_data['backup_path'],
                        checksum=record_data['checksum'],
                        size=record_data['size'],
                        created_time=datetime.fromisoformat(record_data['created_time']),
                        description=record_data.get('description', ''),
                        metadata=record_data.get('metadata', {})
                    )
        except Exception as e:
            logger.error(f"加载备份记录失败: {e}")
    
    def _save_backup_records(self):
        """保存备份记录"""
        try:
            data = {}
            for backup_id, record in self.backup_records.items():
                data[backup_id] = {
                    'backup_id': record.backup_id,
                    'backup_type': record.backup_type.value,
                    'source_path': record.source_path,
                    'backup_path': record.backup_path,
                    'checksum': record.checksum,
                    'size': record.size,
                    'created_time': record.created_time.isoformat(),
                    'description': record.description,
                    'metadata': record.metadata
                }
            
            with open(self.backups_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"保存备份记录失败: {e}")
    
    def _start_timers(self):
        """启动定时器"""
        if self.config.get('auto_backup_enabled', True):
            interval = self.config.get('auto_backup_interval', 300) * 1000  # 转换为毫秒
            self.auto_backup_timer.start(interval)
            logger.info(f"自动备份定时器已启动，间隔: {interval/1000}秒")
        
        integrity_interval = self.config.get('integrity_check_interval', 600) * 1000
        self.integrity_check_timer.start(integrity_interval)
        logger.info(f"完整性检查定时器已启动，间隔: {integrity_interval/1000}秒")
    
    def calculate_file_checksum(self, file_path: str) -> str:
        """计算文件校验和"""
        try:
            hash_md5 = hashlib.md5()
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception as e:
            logger.error(f"计算文件校验和失败 {file_path}: {e}")
            return ""
    
    def update_file_checksum(self, file_path: str) -> bool:
        """更新文件校验和"""
        try:
            with self._lock:
                abs_path = str(Path(file_path).resolve())
                
                if not os.path.exists(abs_path):
                    logger.warning(f"文件不存在，无法更新校验和: {abs_path}")
                    return False
                
                checksum = self.calculate_file_checksum(abs_path)
                if not checksum:
                    return False
                
                stat = os.stat(abs_path)
                
                self.checksums[abs_path] = DataChecksum(
                    file_path=abs_path,
                    checksum=checksum,
                    size=stat.st_size,
                    modified_time=stat.st_mtime
                )
                
                self._save_checksums()
                return True
                
        except Exception as e:
            logger.error(f"更新文件校验和失败 {file_path}: {e}")
            return False
    
    def verify_file_integrity(self, file_path: str) -> bool:
        """验证文件完整性"""
        try:
            abs_path = str(Path(file_path).resolve())
            
            if abs_path not in self.checksums:
                logger.warning(f"文件没有校验和记录: {abs_path}")
                return True  # 新文件认为是有效的
            
            if not os.path.exists(abs_path):
                logger.error(f"文件缺失: {abs_path}")
                return False
            
            stored_checksum = self.checksums[abs_path]
            current_checksum = self.calculate_file_checksum(abs_path)
            
            if current_checksum != stored_checksum.checksum:
                logger.error(f"文件校验和不匹配: {abs_path}")
                self.data_corruption_detected.emit(abs_path)
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"验证文件完整性失败 {file_path}: {e}")
            return False

    def perform_integrity_check(self, level: DataIntegrityLevel = None) -> IntegrityCheckResult:
        """执行完整性检查"""
        if level is None:
            level = DataIntegrityLevel(self.config.get('integrity_level', 'standard'))

        result = IntegrityCheckResult(is_valid=True)

        try:
            logger.info(f"开始执行 {level.value} 级别的完整性检查")

            # 检查关键文件
            critical_files = self.config.get('critical_files', [])
            for file_pattern in critical_files:
                files = self._expand_file_pattern(file_pattern)
                for file_path in files:
                    if not self.verify_file_integrity(file_path):
                        result.corrupted_files.append(file_path)
                        result.is_valid = False

            # 检查监控文件
            if level in [DataIntegrityLevel.STANDARD, DataIntegrityLevel.STRICT, DataIntegrityLevel.PARANOID]:
                watched_files = self.config.get('watched_files', [])
                for file_pattern in watched_files:
                    files = self._expand_file_pattern(file_pattern)
                    for file_path in files:
                        if not os.path.exists(file_path):
                            result.missing_files.append(file_path)
                            result.warnings.append(f"监控文件缺失: {file_path}")
                        elif not self.verify_file_integrity(file_path):
                            result.corrupted_files.append(file_path)
                            result.is_valid = False

            # 严格模式下的额外检查
            if level in [DataIntegrityLevel.STRICT, DataIntegrityLevel.PARANOID]:
                # 检查备份完整性
                self._check_backup_integrity(result)

                # 检查数据结构一致性
                self._check_data_structure_consistency(result)

            # 偏执模式下的全面检查
            if level == DataIntegrityLevel.PARANOID:
                # 检查所有已知文件
                for file_path in self.checksums.keys():
                    if not self.verify_file_integrity(file_path):
                        result.corrupted_files.append(file_path)
                        result.is_valid = False

            logger.info(f"完整性检查完成，结果: {'通过' if result.is_valid else '失败'}")
            self.integrity_check_completed.emit(result.is_valid)

            return result

        except Exception as e:
            logger.error(f"完整性检查失败: {e}")
            result.is_valid = False
            result.errors.append(f"检查过程出错: {e}")
            return result

    def create_backup(self, source_path: str, backup_type: BackupType = BackupType.MANUAL,
                     description: str = "") -> Optional[str]:
        """创建备份"""
        try:
            with self._lock:
                # 生成备份ID
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_id = f"{backup_type.value}_{timestamp}_{int(time.time())}"

                # 确定备份路径
                backup_dir = self.integrity_dir / "backups" / backup_id
                backup_dir.mkdir(parents=True, exist_ok=True)

                source_path_obj = Path(source_path)
                if source_path_obj.is_file():
                    backup_file_path = backup_dir / source_path_obj.name
                    shutil.copy2(source_path, backup_file_path)
                    backup_path = str(backup_file_path)
                else:
                    backup_path = str(backup_dir / source_path_obj.name)
                    shutil.copytree(source_path, backup_path, dirs_exist_ok=True)

                # 计算校验和
                if os.path.isfile(backup_path):
                    checksum = self.calculate_file_checksum(backup_path)
                    size = os.path.getsize(backup_path)
                else:
                    # 目录的话计算所有文件的组合校验和
                    checksum = self._calculate_directory_checksum(backup_path)
                    size = self._calculate_directory_size(backup_path)

                # 创建备份记录
                backup_record = BackupRecord(
                    backup_id=backup_id,
                    backup_type=backup_type,
                    source_path=str(source_path),
                    backup_path=backup_path,
                    checksum=checksum,
                    size=size,
                    created_time=datetime.now(),
                    description=description
                )

                self.backup_records[backup_id] = backup_record
                self._save_backup_records()

                # 清理旧备份
                self._cleanup_old_backups()

                logger.info(f"备份创建成功: {backup_id}")
                self.backup_created.emit(backup_id)

                return backup_id

        except Exception as e:
            logger.error(f"创建备份失败 {source_path}: {e}")
            enhanced_error_handler.handle_exception(e, show_to_user=False)
            return None

    def restore_backup(self, backup_id: str, target_path: str = None, create_recovery_backup: bool = True) -> bool:
        """恢复备份"""
        try:
            if backup_id not in self.backup_records:
                logger.error(f"备份记录不存在: {backup_id}")
                return False

            backup_record = self.backup_records[backup_id]

            if not os.path.exists(backup_record.backup_path):
                logger.error(f"备份文件不存在: {backup_record.backup_path}")
                return False

            # 验证备份完整性
            if os.path.isfile(backup_record.backup_path):
                current_checksum = self.calculate_file_checksum(backup_record.backup_path)
            else:
                current_checksum = self._calculate_directory_checksum(backup_record.backup_path)

            if current_checksum != backup_record.checksum:
                logger.error(f"备份文件已损坏: {backup_id}")
                return False

            # 确定恢复目标
            if target_path is None:
                target_path = backup_record.source_path

            # 创建恢复前的备份（仅在非事务回滚时）
            if create_recovery_backup and os.path.exists(target_path):
                recovery_backup_id = self.create_backup(
                    target_path,
                    BackupType.TRANSACTION,
                    f"恢复前备份 - {backup_id}"
                )
                logger.info(f"恢复前备份已创建: {recovery_backup_id}")

            # 执行恢复
            if os.path.isfile(backup_record.backup_path):
                shutil.copy2(backup_record.backup_path, target_path)
            else:
                if os.path.exists(target_path):
                    shutil.rmtree(target_path)
                shutil.copytree(backup_record.backup_path, target_path)

            # 更新校验和
            self.update_file_checksum(target_path)

            logger.info(f"备份恢复成功: {backup_id} -> {target_path}")
            return True

        except Exception as e:
            logger.error(f"恢复备份失败 {backup_id}: {e}")
            enhanced_error_handler.handle_exception(e, show_to_user=True)
            return False

    @contextmanager
    def transaction(self, description: str = "", files_to_protect: List[str] = None):
        """事务性操作上下文管理器"""
        transaction_id = None
        backup_paths = []
        backup_file_mapping = {}  # 备份ID到原文件路径的映射

        try:
            with self._transaction_lock:
                # 确定要保护的文件
                if files_to_protect:
                    files_to_backup = files_to_protect
                else:
                    # 使用配置中的关键文件
                    critical_files = self.config.get('critical_files', [])
                    files_to_backup = []
                    for file_pattern in critical_files:
                        files_to_backup.extend(self._expand_file_pattern(file_pattern))

                # 创建事务前备份
                for file_path in files_to_backup:
                    if os.path.exists(file_path):
                        backup_id = self.create_backup(
                            file_path,
                            BackupType.TRANSACTION,
                            f"事务备份: {description}"
                        )
                        if backup_id:
                            backup_paths.append(backup_id)
                            backup_file_mapping[backup_id] = file_path

                transaction_id = f"tx_{int(time.time())}"
                logger.info(f"事务开始: {transaction_id}, 保护文件数: {len(backup_paths)}")

                yield transaction_id

                # 事务成功，更新校验和
                for file_path in files_to_backup:
                    if os.path.exists(file_path):
                        self.update_file_checksum(file_path)

                logger.info(f"事务提交成功: {transaction_id}")

        except Exception as e:
            # 事务失败，恢复备份
            logger.error(f"事务失败，开始回滚: {transaction_id} - {e}")

            if backup_paths:
                for backup_id in backup_paths:
                    try:
                        original_file = backup_file_mapping.get(backup_id)
                        if original_file:
                            # 事务回滚时不创建恢复前备份
                            self.restore_backup(backup_id, original_file, create_recovery_backup=False)
                            logger.info(f"已回滚备份: {backup_id} -> {original_file}")
                        else:
                            self.restore_backup(backup_id, create_recovery_backup=False)
                            logger.info(f"已回滚备份: {backup_id}")
                    except Exception as restore_error:
                        logger.error(f"回滚备份失败: {backup_id} - {restore_error}")

            raise e

    def _expand_file_pattern(self, pattern: str) -> List[str]:
        """展开文件模式"""
        try:
            import glob

            # 处理相对路径
            if not os.path.isabs(pattern):
                pattern = str(self.project_root / pattern)

            # 使用glob展开模式
            files = glob.glob(pattern, recursive=True)

            # 过滤出存在的文件
            return [f for f in files if os.path.isfile(f)]

        except Exception as e:
            logger.error(f"展开文件模式失败 {pattern}: {e}")
            return []

    def _calculate_directory_checksum(self, directory: str) -> str:
        """计算目录校验和"""
        try:
            hash_md5 = hashlib.md5()

            for root, dirs, files in os.walk(directory):
                # 排序确保一致性
                dirs.sort()
                files.sort()

                for file in files:
                    file_path = os.path.join(root, file)
                    try:
                        with open(file_path, 'rb') as f:
                            for chunk in iter(lambda: f.read(4096), b""):
                                hash_md5.update(chunk)
                        # 添加文件路径到哈希中
                        hash_md5.update(file_path.encode('utf-8'))
                    except Exception as e:
                        logger.warning(f"计算文件校验和时跳过: {file_path} - {e}")

            return hash_md5.hexdigest()

        except Exception as e:
            logger.error(f"计算目录校验和失败 {directory}: {e}")
            return ""

    def _calculate_directory_size(self, directory: str) -> int:
        """计算目录大小"""
        try:
            total_size = 0
            for root, dirs, files in os.walk(directory):
                for file in files:
                    file_path = os.path.join(root, file)
                    try:
                        total_size += os.path.getsize(file_path)
                    except Exception as e:
                        logger.warning(f"计算文件大小时跳过: {file_path} - {e}")
            return total_size
        except Exception as e:
            logger.error(f"计算目录大小失败 {directory}: {e}")
            return 0

    def _check_backup_integrity(self, result: IntegrityCheckResult):
        """检查备份完整性"""
        try:
            for backup_id, backup_record in self.backup_records.items():
                if not os.path.exists(backup_record.backup_path):
                    result.warnings.append(f"备份文件缺失: {backup_id}")
                    continue

                # 验证备份校验和
                if os.path.isfile(backup_record.backup_path):
                    current_checksum = self.calculate_file_checksum(backup_record.backup_path)
                else:
                    current_checksum = self._calculate_directory_checksum(backup_record.backup_path)

                if current_checksum != backup_record.checksum:
                    result.warnings.append(f"备份文件校验和不匹配: {backup_id}")

        except Exception as e:
            result.errors.append(f"检查备份完整性失败: {e}")

    def _check_data_structure_consistency(self, result: IntegrityCheckResult):
        """检查数据结构一致性"""
        try:
            # 检查项目文件结构
            project_file = self.project_root / "project.json"
            if project_file.exists():
                try:
                    with open(project_file, 'r', encoding='utf-8') as f:
                        project_data = json.load(f)

                    # 验证必要字段
                    required_fields = ['name', 'version', 'created_time']
                    for field in required_fields:
                        if field not in project_data:
                            result.warnings.append(f"项目文件缺少必要字段: {field}")

                except json.JSONDecodeError as e:
                    result.errors.append(f"项目文件JSON格式错误: {e}")
                except Exception as e:
                    result.errors.append(f"读取项目文件失败: {e}")
            else:
                result.warnings.append("项目文件不存在")

        except Exception as e:
            result.errors.append(f"检查数据结构一致性失败: {e}")

    def _cleanup_old_backups(self):
        """清理旧备份"""
        try:
            max_backups = self.config.get('max_backups', 10)

            # 按创建时间排序
            sorted_backups = sorted(
                self.backup_records.items(),
                key=lambda x: x[1].created_time,
                reverse=True
            )

            # 删除超出限制的备份
            if len(sorted_backups) > max_backups:
                backups_to_delete = sorted_backups[max_backups:]

                for backup_id, backup_record in backups_to_delete:
                    try:
                        # 删除备份文件
                        if os.path.exists(backup_record.backup_path):
                            if os.path.isfile(backup_record.backup_path):
                                os.remove(backup_record.backup_path)
                            else:
                                shutil.rmtree(backup_record.backup_path)

                        # 删除记录
                        del self.backup_records[backup_id]
                        logger.info(f"已清理旧备份: {backup_id}")

                    except Exception as e:
                        logger.error(f"清理备份失败 {backup_id}: {e}")

                # 保存更新后的备份记录
                self._save_backup_records()

        except Exception as e:
            logger.error(f"清理旧备份失败: {e}")

    def _perform_auto_backup(self):
        """执行自动备份"""
        try:
            logger.info("开始执行自动备份")

            critical_files = self.config.get('critical_files', [])
            backup_count = 0

            for file_pattern in critical_files:
                files = self._expand_file_pattern(file_pattern)
                for file_path in files:
                    if os.path.exists(file_path):
                        backup_id = self.create_backup(
                            file_path,
                            BackupType.AUTO,
                            f"自动备份 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
                        )
                        if backup_id:
                            backup_count += 1

            logger.info(f"自动备份完成，创建了 {backup_count} 个备份")

        except Exception as e:
            logger.error(f"自动备份失败: {e}")

    def _perform_integrity_check(self):
        """执行定期完整性检查"""
        try:
            result = self.perform_integrity_check()

            if not result.is_valid:
                logger.warning("定期完整性检查发现问题")

                # 尝试自动恢复
                if self.config.get('auto_recovery_enabled', True):
                    self._attempt_auto_recovery(result)
            else:
                logger.info("定期完整性检查通过")

        except Exception as e:
            logger.error(f"定期完整性检查失败: {e}")

    def _attempt_auto_recovery(self, check_result: IntegrityCheckResult):
        """尝试自动恢复"""
        try:
            logger.info("开始尝试自动恢复")
            recovery_success = True

            # 恢复损坏的文件
            for corrupted_file in check_result.corrupted_files:
                # 查找最新的备份
                latest_backup = self._find_latest_backup_for_file(corrupted_file)

                if latest_backup:
                    if self.restore_backup(latest_backup, corrupted_file):
                        logger.info(f"已自动恢复文件: {corrupted_file}")
                    else:
                        logger.error(f"自动恢复文件失败: {corrupted_file}")
                        recovery_success = False
                else:
                    logger.warning(f"找不到文件的备份: {corrupted_file}")
                    recovery_success = False

            self.auto_recovery_completed.emit(recovery_success)

            if recovery_success:
                logger.info("自动恢复完成")
            else:
                logger.warning("自动恢复部分失败")

        except Exception as e:
            logger.error(f"自动恢复失败: {e}")
            self.auto_recovery_completed.emit(False)

    def _find_latest_backup_for_file(self, file_path: str) -> Optional[str]:
        """查找文件的最新备份"""
        try:
            latest_backup = None
            latest_time = None

            for backup_id, backup_record in self.backup_records.items():
                if backup_record.source_path == file_path:
                    if latest_time is None or backup_record.created_time > latest_time:
                        latest_time = backup_record.created_time
                        latest_backup = backup_id

            return latest_backup

        except Exception as e:
            logger.error(f"查找文件备份失败 {file_path}: {e}")
            return None

    def get_integrity_summary(self) -> Dict[str, Any]:
        """获取完整性摘要"""
        try:
            total_files = len(self.checksums)
            total_backups = len(self.backup_records)

            # 计算备份总大小
            total_backup_size = sum(record.size for record in self.backup_records.values())

            # 最近的完整性检查
            recent_check = self.perform_integrity_check(DataIntegrityLevel.BASIC)

            return {
                'total_monitored_files': total_files,
                'total_backups': total_backups,
                'total_backup_size_mb': total_backup_size / (1024 * 1024),
                'integrity_status': 'healthy' if recent_check.is_valid else 'issues_detected',
                'corrupted_files_count': len(recent_check.corrupted_files),
                'missing_files_count': len(recent_check.missing_files),
                'last_auto_backup': max(
                    (record.created_time for record in self.backup_records.values()
                     if record.backup_type == BackupType.AUTO),
                    default=None
                ),
                'config': {
                    'auto_backup_enabled': self.config.get('auto_backup_enabled', True),
                    'integrity_level': self.config.get('integrity_level', 'standard'),
                    'max_backups': self.config.get('max_backups', 10)
                }
            }

        except Exception as e:
            logger.error(f"获取完整性摘要失败: {e}")
            return {'error': str(e)}

# 全局数据完整性管理器实例
data_integrity_manager = DataIntegrityManager()
