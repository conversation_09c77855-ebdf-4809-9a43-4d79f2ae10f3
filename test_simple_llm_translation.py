#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的LLM翻译测试
直接创建LLM API实例进行测试
"""

import sys
import os

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_direct_llm_translation():
    print("🧪 直接LLM翻译测试")
    print("=" * 50)
    
    try:
        # 直接导入和创建LLM API
        from src.models.llm_api import LLMApi

        # 使用通义千问配置
        llm_api = LLMApi(
            api_type='tongyi',
            api_key='sk-ab30df729a9b4df287db20a8f47ba12c',
            api_url='https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions'
        )
        
        print("✅ LLM API创建成功")
        
        # 测试翻译
        test_text = "林肯在书房内，烛光摇曳，他在阅读妻子玛丽的信"
        
        prompt = f"""请将以下中文图像描述翻译成英文。要求：
1. 使用专业的摄影和电影术语
2. 保持描述的视觉准确性和艺术性
3. 翻译要简洁明了，适合AI图像生成
4. 只输出英文翻译结果，不要包含任何解释

中文描述：{test_text}

英文翻译："""
        
        print(f"原文: {test_text}")
        print("正在翻译...")
        
        result = llm_api.rewrite_text(prompt)
        
        if result:
            # 清理结果
            cleaned_result = result.strip()
            
            # 移除常见前缀
            prefixes = ["英文翻译：", "Translation:", "English:"]
            for prefix in prefixes:
                if cleaned_result.startswith(prefix):
                    cleaned_result = cleaned_result[len(prefix):].strip()
            
            # 移除引号
            if cleaned_result.startswith('"') and cleaned_result.endswith('"'):
                cleaned_result = cleaned_result[1:-1]
            
            print(f"译文: {cleaned_result}")
            print("✅ LLM翻译成功！")
            return True
        else:
            print("❌ LLM翻译失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_enhanced_translator_with_llm():
    print("\n🔧 增强翻译器测试")
    print("=" * 50)
    
    try:
        from src.utils.enhanced_translator import EnhancedTranslator
        from src.models.llm_api import LLMApi

        # 创建LLM API
        llm_api = LLMApi(
            api_type='tongyi',
            api_key='sk-ab30df729a9b4df287db20a8f47ba12c',
            api_url='https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions'
        )
        
        # 创建翻译器并传入LLM API
        translator = EnhancedTranslator(llm_api=llm_api)
        
        print("✅ 增强翻译器创建成功")
        
        # 测试翻译
        test_texts = [
            "林肯在书房内，烛光摇曳，他在阅读妻子玛丽的信",
            "镜头特写：主角的眼神充满了坚定和决心"
        ]
        
        success_count = 0
        for i, text in enumerate(test_texts, 1):
            print(f"\n测试 {i}:")
            print(f"原文: {text}")
            
            try:
                result = translator.translate_text(text, 'zh', 'en')
                if result and result != text:
                    print(f"译文: {result}")
                    print("✅ 翻译成功")
                    success_count += 1
                else:
                    print("❌ 翻译失败")
            except Exception as e:
                print(f"❌ 翻译异常: {e}")
        
        print(f"\n成功率: {success_count}/{len(test_texts)}")
        return success_count > 0
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    print("🚀 LLM翻译功能简单测试")
    print("=" * 60)
    
    # 测试1：直接LLM翻译
    direct_ok = test_direct_llm_translation()
    
    # 测试2：增强翻译器
    enhanced_ok = test_enhanced_translator_with_llm()
    
    print("\n" + "=" * 60)
    print("📋 测试总结")
    print("=" * 60)
    print(f"直接LLM翻译: {'✅ 成功' if direct_ok else '❌ 失败'}")
    print(f"增强翻译器: {'✅ 成功' if enhanced_ok else '❌ 失败'}")
    
    if direct_ok and enhanced_ok:
        print("\n🎉 LLM翻译功能正常！")
        print("\n💡 下一步:")
        print("- 重启主程序测试翻译功能")
        print("- LLM翻译将自动使用当前配置的通义千问模型")
    else:
        print("\n⚠️ 存在问题，需要进一步调试")

if __name__ == "__main__":
    main()
