#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统备份脚本
在进行重要修改前自动备份关键文件
"""

import os
import shutil
import json
import time
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any

class SystemBackup:
    """系统备份管理器"""
    
    def __init__(self, project_root: str = None):
        self.project_root = Path(project_root) if project_root else Path(__file__).parent.parent
        self.backup_dir = self.project_root / "backups"
        self.backup_dir.mkdir(exist_ok=True)
        
        # 关键文件和目录列表
        self.critical_files = [
            "main.py",
            "start.py", 
            "requirements.txt",
            "src/core/",
            "src/gui/new_main_window.py",
            "src/processors/",
            "src/services/",
            "src/utils/",
            "config/",
            "docs/PROJECT_OVERVIEW.md",
            "docs/PROJECT_STATUS.md"
        ]
    
    def create_backup(self, backup_name: str = None) -> str:
        """创建系统备份"""
        if backup_name is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_name = f"system_backup_{timestamp}"
        
        backup_path = self.backup_dir / backup_name
        backup_path.mkdir(exist_ok=True)
        
        print(f"🔄 开始创建系统备份: {backup_name}")
        
        backup_info = {
            "backup_name": backup_name,
            "created_time": datetime.now().isoformat(),
            "files_backed_up": [],
            "total_size": 0
        }
        
        for file_path in self.critical_files:
            source_path = self.project_root / file_path
            
            if source_path.exists():
                try:
                    if source_path.is_file():
                        # 备份单个文件
                        dest_path = backup_path / file_path
                        dest_path.parent.mkdir(parents=True, exist_ok=True)
                        shutil.copy2(source_path, dest_path)
                        backup_info["files_backed_up"].append(str(file_path))
                        backup_info["total_size"] += source_path.stat().st_size
                        
                    elif source_path.is_dir():
                        # 备份整个目录
                        dest_path = backup_path / file_path
                        shutil.copytree(source_path, dest_path, dirs_exist_ok=True)
                        backup_info["files_backed_up"].append(str(file_path))
                        
                        # 计算目录大小
                        for root, dirs, files in os.walk(source_path):
                            for file in files:
                                file_path_obj = Path(root) / file
                                backup_info["total_size"] += file_path_obj.stat().st_size
                                
                    print(f"  ✅ 已备份: {file_path}")
                    
                except Exception as e:
                    print(f"  ❌ 备份失败: {file_path} - {e}")
            else:
                print(f"  ⚠️ 文件不存在: {file_path}")
        
        # 保存备份信息
        backup_info_path = backup_path / "backup_info.json"
        with open(backup_info_path, 'w', encoding='utf-8') as f:
            json.dump(backup_info, f, ensure_ascii=False, indent=2)
        
        # 转换大小为可读格式
        size_mb = backup_info["total_size"] / (1024 * 1024)
        print(f"✅ 备份完成! 备份大小: {size_mb:.2f} MB")
        print(f"📁 备份位置: {backup_path}")
        
        return str(backup_path)
    
    def list_backups(self) -> List[Dict[str, Any]]:
        """列出所有备份"""
        backups = []
        
        for backup_dir in self.backup_dir.iterdir():
            if backup_dir.is_dir():
                info_file = backup_dir / "backup_info.json"
                if info_file.exists():
                    try:
                        with open(info_file, 'r', encoding='utf-8') as f:
                            backup_info = json.load(f)
                        backup_info["backup_path"] = str(backup_dir)
                        backups.append(backup_info)
                    except Exception as e:
                        print(f"读取备份信息失败: {backup_dir} - {e}")
        
        # 按创建时间排序
        backups.sort(key=lambda x: x.get("created_time", ""), reverse=True)
        return backups
    
    def restore_backup(self, backup_name: str) -> bool:
        """恢复备份"""
        backup_path = self.backup_dir / backup_name
        
        if not backup_path.exists():
            print(f"❌ 备份不存在: {backup_name}")
            return False
        
        print(f"🔄 开始恢复备份: {backup_name}")
        print("⚠️ 警告: 这将覆盖当前文件!")
        
        # 在实际项目中，这里应该有用户确认
        confirm = input("确认恢复备份? (y/N): ")
        if confirm.lower() != 'y':
            print("❌ 恢复操作已取消")
            return False
        
        try:
            # 读取备份信息
            info_file = backup_path / "backup_info.json"
            if info_file.exists():
                with open(info_file, 'r', encoding='utf-8') as f:
                    backup_info = json.load(f)
                
                for file_path in backup_info.get("files_backed_up", []):
                    source_path = backup_path / file_path
                    dest_path = self.project_root / file_path
                    
                    if source_path.exists():
                        if source_path.is_file():
                            dest_path.parent.mkdir(parents=True, exist_ok=True)
                            shutil.copy2(source_path, dest_path)
                        elif source_path.is_dir():
                            if dest_path.exists():
                                shutil.rmtree(dest_path)
                            shutil.copytree(source_path, dest_path)
                        
                        print(f"  ✅ 已恢复: {file_path}")
            
            print("✅ 备份恢复完成!")
            return True
            
        except Exception as e:
            print(f"❌ 恢复备份失败: {e}")
            return False
    
    def cleanup_old_backups(self, keep_count: int = 5):
        """清理旧备份，保留最新的几个"""
        backups = self.list_backups()
        
        if len(backups) <= keep_count:
            print(f"当前备份数量: {len(backups)}，无需清理")
            return
        
        backups_to_delete = backups[keep_count:]
        
        for backup in backups_to_delete:
            backup_path = Path(backup["backup_path"])
            try:
                shutil.rmtree(backup_path)
                print(f"🗑️ 已删除旧备份: {backup['backup_name']}")
            except Exception as e:
                print(f"❌ 删除备份失败: {backup['backup_name']} - {e}")

def main():
    """主函数"""
    backup_system = SystemBackup()
    
    print("🛡️ AI视频生成器系统备份工具")
    print("=" * 50)
    
    while True:
        print("\n选择操作:")
        print("1. 创建备份")
        print("2. 列出备份")
        print("3. 恢复备份")
        print("4. 清理旧备份")
        print("5. 退出")
        
        choice = input("\n请输入选择 (1-5): ").strip()
        
        if choice == "1":
            backup_name = input("输入备份名称 (留空使用时间戳): ").strip()
            backup_name = backup_name if backup_name else None
            backup_system.create_backup(backup_name)
            
        elif choice == "2":
            backups = backup_system.list_backups()
            if backups:
                print(f"\n📋 共找到 {len(backups)} 个备份:")
                for i, backup in enumerate(backups, 1):
                    size_mb = backup.get("total_size", 0) / (1024 * 1024)
                    print(f"{i}. {backup['backup_name']} - {backup['created_time']} ({size_mb:.2f} MB)")
            else:
                print("📭 没有找到备份")
                
        elif choice == "3":
            backups = backup_system.list_backups()
            if backups:
                print("\n选择要恢复的备份:")
                for i, backup in enumerate(backups, 1):
                    print(f"{i}. {backup['backup_name']} - {backup['created_time']}")
                
                try:
                    index = int(input("输入备份编号: ")) - 1
                    if 0 <= index < len(backups):
                        backup_system.restore_backup(backups[index]['backup_name'])
                    else:
                        print("❌ 无效的备份编号")
                except ValueError:
                    print("❌ 请输入有效的数字")
            else:
                print("📭 没有找到备份")
                
        elif choice == "4":
            try:
                keep_count = int(input("保留最新备份数量 (默认5): ") or "5")
                backup_system.cleanup_old_backups(keep_count)
            except ValueError:
                print("❌ 请输入有效的数字")
                
        elif choice == "5":
            print("👋 再见!")
            break
            
        else:
            print("❌ 无效选择，请重试")

if __name__ == "__main__":
    main()
