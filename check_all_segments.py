#!/usr/bin/env python3
"""检查所有段落的音频路径映射"""

import json
import os

def main():
    project_file = r"D:\AI_Video_Generator\output\天下无双\project.json"
    
    with open(project_file, 'r', encoding='utf-8') as f:
        data = json.load(f)

    # 查找所有配音段落
    voice_segments = data.get('voice_generation', {}).get('voice_segments', [])
    print(f'总共有 {len(voice_segments)} 个配音段落')

    # 检查每个段落的音频路径
    print('\n所有段落的音频路径映射:')
    for i, segment in enumerate(voice_segments):
        audio_path = segment.get("audio_path", "")
        shot_id = segment.get("shot_id", "")
        scene_id = segment.get("scene_id", "")
        
        # 从路径中提取文件名
        filename = os.path.basename(audio_path) if audio_path else "无"
        
        print(f'  段落{i+1:2d}: {scene_id}-{shot_id} -> {filename}')
        
        # 检查是否有重复的音频文件
        if i > 0:
            for j in range(i):
                prev_segment = voice_segments[j]
                prev_audio_path = prev_segment.get("audio_path", "")
                if audio_path == prev_audio_path and audio_path:
                    print(f'    ⚠️  与段落{j+1}使用相同音频文件')

if __name__ == "__main__":
    main()
