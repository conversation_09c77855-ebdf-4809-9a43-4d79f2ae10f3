# -*- coding: utf-8 -*-
"""
CogVideoX-Flash 视频生成引擎实现
智谱AI的免费视频生成模型，支持图生视频和文生视频
"""

import asyncio
import aiohttp
import os
import time
import json
import base64
from typing import List, Dict, Optional, Callable
from ..video_engine_base import (
    VideoGenerationEngine, VideoEngineType, VideoEngineStatus, 
    VideoGenerationConfig, VideoGenerationResult, VideoEngineInfo, ConfigConverter
)
from src.utils.logger import logger


class CogVideoXEngine(VideoGenerationEngine):
    """CogVideoX-Flash 引擎实现"""
    
    def __init__(self, config: Optional[Dict] = None):
        super().__init__(VideoEngineType.COGVIDEOX_FLASH)
        self.config = config or {}
        
        # API配置
        self.api_key = self.config.get('api_key', '')
        self.base_url = self.config.get('base_url', 'https://open.bigmodel.cn/api/paas/v4')
        self.model = self.config.get('model', 'cogvideox-flash')
        
        # 请求配置
        self.timeout = self.config.get('timeout', 300)  # 5分钟超时
        self.max_retries = self.config.get('max_retries', 3)
        
        # 输出配置
        self.output_dir = self.config.get('output_dir', 'output/videos')
        
        # HTTP会话
        self.session: Optional[aiohttp.ClientSession] = None
        
        # 项目相关信息
        self.project_manager = None
        self.current_project_name = None
        
        if not self.api_key:
            logger.warning("CogVideoX-Flash引擎未配置API密钥")
    
    async def initialize(self) -> bool:
        """初始化引擎"""
        try:
            if not self.api_key:
                logger.error("CogVideoX-Flash引擎缺少API密钥")
                self.status = VideoEngineStatus.ERROR
                return False
            
            # 创建HTTP会话
            connector = aiohttp.TCPConnector(limit=10, limit_per_host=5)
            timeout = aiohttp.ClientTimeout(total=self.timeout)
            self.session = aiohttp.ClientSession(
                connector=connector,
                timeout=timeout,
                headers={
                    'Authorization': f'Bearer {self.api_key}',
                    'Content-Type': 'application/json'
                }
            )
            
            # 测试连接
            if await self.test_connection():
                self.status = VideoEngineStatus.IDLE
                logger.info("CogVideoX-Flash引擎初始化成功")
                return True
            else:
                self.status = VideoEngineStatus.ERROR
                logger.error("CogVideoX-Flash引擎连接测试失败")
                return False
                
        except Exception as e:
            logger.error(f"CogVideoX-Flash引擎初始化失败: {e}")
            self.status = VideoEngineStatus.ERROR
            return False
    
    async def test_connection(self) -> bool:
        """测试连接"""
        try:
            if not self.session:
                return False

            # 测试视频生成端点是否可访问
            # 使用一个简单的请求来验证API密钥和端点
            test_url = f"{self.base_url}/videos/generations"
            test_data = {
                "model": self.model,
                "prompt": "test"
            }

            async with self.session.post(test_url, json=test_data) as response:
                # 如果返回401，说明API密钥问题
                # 如果返回400，可能是参数问题，但端点是对的
                # 如果返回200或202，说明连接正常
                if response.status in [200, 202]:
                    return True
                elif response.status == 401:
                    logger.error("API密钥无效或已过期")
                    return False
                elif response.status == 400:
                    # 参数错误但API可访问，认为连接正常
                    logger.info("API端点可访问（参数测试返回400）")
                    return True
                else:
                    logger.warning(f"API测试返回状态码: {response.status}")
                    return False

        except Exception as e:
            logger.error(f"CogVideoX-Flash连接测试失败: {e}")
            return False
    
    def get_available_models(self) -> List[str]:
        """获取可用模型"""
        return ['cogvideox-flash']
    
    def get_engine_info(self) -> VideoEngineInfo:
        """获取引擎信息"""
        return VideoEngineInfo(
            name="CogVideoX-Flash",
            version="1.0",
            description="智谱AI免费视频生成模型，支持图生视频和文生视频",
            is_free=True,
            supports_image_to_video=True,
            supports_text_to_video=True,
            max_duration=10.0,  # 最大10秒
            supported_resolutions=[
                (720, 480), (1024, 1024), (1280, 960), 
                (960, 1280), (1920, 1080), (1080, 1920),
                (2048, 1080), (3840, 2160)  # 4K
            ],
            supported_fps=[24, 30, 60],
            cost_per_second=0.0,  # 免费
            rate_limit=60  # 每分钟60次请求（估计值）
        )
    
    def _get_output_dir(self) -> str:
        """获取输出目录"""
        try:
            # 如果有项目管理器，使用项目目录
            if self.project_manager and self.current_project_name:
                try:
                    # 🔧 修复项目管理器方法调用
                    if hasattr(self.project_manager, 'current_project') and self.project_manager.current_project:
                        project_data = self.project_manager.current_project
                        if project_data and 'project_dir' in project_data:
                            project_dir = project_data['project_dir']
                            output_dir = os.path.join(project_dir, 'videos', 'cogvideox')
                            os.makedirs(output_dir, exist_ok=True)
                            logger.info(f"使用项目输出目录: {output_dir}")
                            return output_dir
                except Exception as e:
                    logger.warning(f"获取项目路径失败: {e}，使用默认目录")

        except Exception as e:
            logger.warning(f"无法获取项目目录: {e}")

        # 无项目时使用temp/video_cache
        output_dir = os.path.join(os.getcwd(), 'temp', 'video_cache', 'cogvideox')
        os.makedirs(output_dir, exist_ok=True)
        logger.info(f"使用默认输出目录: {output_dir}")
        return output_dir
    
    async def generate_video(self, config: VideoGenerationConfig, 
                           progress_callback: Optional[Callable] = None,
                           project_manager=None, current_project_name=None) -> VideoGenerationResult:
        """生成视频"""
        # 设置项目信息
        if project_manager and current_project_name:
            self.project_manager = project_manager
            self.current_project_name = current_project_name
            # 更新输出目录
            self.output_dir = self._get_output_dir()
        
        start_time = time.time()
        self.status = VideoEngineStatus.BUSY
        self.request_count += 1
        
        try:
            if progress_callback:
                progress_callback("开始CogVideoX-Flash视频生成...")
            
            # 准备请求数据
            request_data = self._prepare_request_data(config)
            
            if progress_callback:
                progress_callback("发送视频生成请求...")
            
            # 发送异步生成请求
            task_id = await self._submit_generation_task(request_data)
            
            if progress_callback:
                progress_callback("等待视频生成完成...")
            
            # 轮询任务状态
            video_url = await self._poll_task_status(task_id, progress_callback)
            
            if progress_callback:
                progress_callback("下载生成的视频...")
            
            # 下载视频文件
            video_path = await self._download_video(video_url, config)
            
            # 获取视频信息
            video_info = await self._get_video_info(video_path)
            
            generation_time = time.time() - start_time
            self.success_count += 1
            self.status = VideoEngineStatus.IDLE
            
            if progress_callback:
                progress_callback("视频生成完成!")
            
            return VideoGenerationResult(
                success=True,
                video_path=video_path,
                generation_time=generation_time,
                engine_type=self.engine_type,
                duration=video_info.get('duration', config.duration),
                fps=video_info.get('fps', config.fps),
                resolution=video_info.get('resolution', (config.width, config.height)),
                file_size=video_info.get('file_size', 0),
                metadata={
                    'model': self.model,
                    'prompt': config.input_prompt,
                    'input_image': config.input_image_path,
                    'motion_intensity': config.motion_intensity
                }
            )
            
        except Exception as e:
            self.error_count += 1
            self.last_error = str(e)
            self.status = VideoEngineStatus.IDLE
            logger.error(f"CogVideoX-Flash视频生成失败: {e}")
            
            return VideoGenerationResult(
                success=False,
                error_message=f"CogVideoX-Flash生成失败: {e}",
                generation_time=time.time() - start_time,
                engine_type=self.engine_type
            )

    def _prepare_request_data(self, config: VideoGenerationConfig) -> Dict:
        """准备请求数据"""
        request_data = {
            "model": self.model,
            "prompt": config.input_prompt
        }

        # 检查是否是图生视频模式
        is_image_to_video = config.input_image_path and os.path.exists(config.input_image_path)

        if is_image_to_video:
            # 图生视频模式 - 将本地图像转换为base64格式
            try:
                image_base64 = self._convert_image_to_base64(config.input_image_path)
                request_data["image_url"] = image_base64
                logger.info(f"图像已转换为base64格式，长度: {len(image_base64)} 字符")
            except Exception as e:
                logger.error(f"图像转换失败: {e}")
                raise Exception(f"无法处理输入图像: {e}")

        # 无论是图生视频还是文生视频，都添加完整参数
        if config.duration > 0:
            request_data["duration"] = min(config.duration, 10.0)  # 最大10秒

        # 🔧 图生视频和文生视频支持的FPS不同
        is_image_to_video = config.input_image_path and os.path.exists(config.input_image_path)
        if is_image_to_video:
            # 图生视频模式支持的FPS值
            supported_fps = [30, 60]  # 根据测试结果，图生视频只支持30和60
            default_fps = 30
        else:
            # 文生视频模式支持的FPS值
            supported_fps = [8, 16, 24, 25, 30, 60]
            default_fps = 24

        logger.info(f"配置FPS: {config.fps}, {'图生视频' if is_image_to_video else '文生视频'}模式支持的FPS: {supported_fps}")

        if config.fps in supported_fps:
            request_data["fps"] = config.fps
            logger.info(f"设置请求FPS: {config.fps}")
        else:
            # 如果FPS不支持，使用默认值
            request_data["fps"] = default_fps
            logger.warning(f"FPS {config.fps} 不支持，使用默认值{default_fps}")

        # 🔧 处理duration限制
        if hasattr(config, 'duration') and config.duration:
            if is_image_to_video:
                # 图生视频模式的duration限制
                if 5.0 <= config.duration <= 5.9:
                    # 5.0-5.9秒范围内支持
                    request_data["duration"] = config.duration
                    logger.info(f"设置请求duration: {config.duration}")
                elif config.duration == 10.0:
                    # 10.0秒也支持
                    request_data["duration"] = config.duration
                    logger.info(f"设置请求duration: {config.duration}")
                else:
                    # 不支持的duration，调整到最接近的支持值
                    if config.duration < 5.0:
                        adjusted_duration = 5.0
                    elif config.duration > 10.0:
                        adjusted_duration = 10.0
                    elif 5.9 < config.duration < 10.0:
                        # 在5.9-10.0之间，选择更接近的值
                        if config.duration - 5.9 < 10.0 - config.duration:
                            adjusted_duration = 5.9
                        else:
                            adjusted_duration = 10.0
                    else:
                        adjusted_duration = 5.0

                    request_data["duration"] = adjusted_duration
                    logger.warning(f"图生视频duration {config.duration} 不支持，调整为 {adjusted_duration}")
            else:
                # 文生视频模式，直接使用配置的duration
                request_data["duration"] = config.duration
                logger.info(f"设置请求duration: {config.duration}")
        else:
            # 没有duration配置，让API使用默认值
            logger.info("未设置duration，使用API默认值")

        if config.width and config.height:
            request_data["size"] = f"{config.width}x{config.height}"

        # 运动强度
        if config.motion_intensity is not None:
            request_data["motion_intensity"] = config.motion_intensity

        # 随机种子
        if config.seed is not None:
            request_data["seed"] = config.seed

        # 🔧 添加完整请求数据日志（隐藏base64内容）
        log_data = request_data.copy()
        if "image_url" in log_data and log_data["image_url"].startswith("data:"):
            log_data["image_url"] = f"data:image/...;base64,<{len(log_data['image_url'])} chars>"
        logger.info(f"CogVideoX-Flash请求数据: {log_data}")

        return request_data

    def _convert_image_to_base64(self, image_path: str) -> str:
        """将图像文件转换为base64格式的data URL"""
        try:
            with open(image_path, 'rb') as f:
                image_data = f.read()

            # 获取文件扩展名确定MIME类型
            ext = os.path.splitext(image_path)[1].lower()
            if ext == '.png':
                mime_type = 'image/png'
            elif ext in ['.jpg', '.jpeg']:
                mime_type = 'image/jpeg'
            elif ext == '.webp':
                mime_type = 'image/webp'
            else:
                mime_type = 'image/png'  # 默认

            # 编码为base64
            base64_str = base64.b64encode(image_data).decode('utf-8')

            # 返回data URL格式
            return f"data:{mime_type};base64,{base64_str}"

        except Exception as e:
            logger.error(f"图像转换为base64失败: {e}")
            raise Exception(f"无法读取或转换图像文件 {image_path}: {e}")

    async def _submit_generation_task(self, request_data: Dict) -> str:
        """提交生成任务"""
        if not self.session:
            raise Exception("HTTP会话未初始化")

        url = f"{self.base_url}/videos/generations"

        async with self.session.post(url, json=request_data) as response:
            if response.status != 200:
                error_text = await response.text()
                raise Exception(f"API请求失败 (状态码: {response.status}): {error_text}")

            result = await response.json()

            if 'id' not in result:
                raise Exception(f"API响应格式错误: {result}")

            return result['id']

    async def _poll_task_status(self, task_id: str, progress_callback: Optional[Callable] = None) -> str:
        """轮询任务状态"""
        if not self.session:
            raise Exception("HTTP会话未初始化")

        url = f"{self.base_url}/async-result/{task_id}"
        max_wait_time = 300  # 最大等待5分钟
        poll_interval = 5  # 每5秒查询一次
        start_time = time.time()

        while time.time() - start_time < max_wait_time:
            try:
                async with self.session.get(url) as response:
                    if response.status != 200:
                        error_text = await response.text()
                        raise Exception(f"查询任务状态失败 (状态码: {response.status}): {error_text}")

                    result = await response.json()
                    status = result.get('task_status', 'PROCESSING')

                    if status == 'SUCCESS':
                        video_result = result.get('video_result', [])

                        # video_result是一个列表，取第一个元素
                        if isinstance(video_result, list) and len(video_result) > 0:
                            video_info = video_result[0]
                            video_url = video_info.get('url')
                        else:
                            video_url = None

                        if not video_url:
                            raise Exception("API响应中没有视频URL")
                        return video_url

                    elif status == 'FAIL':
                        error_msg = result.get('error', {}).get('message', '未知错误')
                        raise Exception(f"视频生成失败: {error_msg}")

                    elif status in ['PROCESSING', 'SUBMITTED']:
                        if progress_callback:
                            elapsed = int(time.time() - start_time)
                            progress_callback(f"视频生成中... ({elapsed}s)")
                        await asyncio.sleep(poll_interval)

                    else:
                        logger.warning(f"未知任务状态: {status}")
                        await asyncio.sleep(poll_interval)

            except Exception as e:
                if "查询任务状态失败" in str(e):
                    raise e
                logger.warning(f"查询任务状态时出错: {e}")
                await asyncio.sleep(poll_interval)

        raise Exception(f"视频生成超时 (超过 {max_wait_time} 秒)")

    async def _download_video(self, video_url: str, config: VideoGenerationConfig) -> str:
        """下载视频文件"""
        if not self.session:
            raise Exception("HTTP会话未初始化")

        # 生成输出文件名
        timestamp = int(time.time())
        filename = f"cogvideox_{timestamp}.{config.output_format}"
        output_path = os.path.join(self.output_dir, filename)

        # 下载视频
        async with self.session.get(video_url) as response:
            if response.status != 200:
                raise Exception(f"下载视频失败 (状态码: {response.status})")

            with open(output_path, 'wb') as f:
                async for chunk in response.content.iter_chunked(8192):
                    f.write(chunk)

        logger.info(f"视频已保存到: {output_path}")
        return output_path

    async def _get_video_info(self, video_path: str) -> Dict:
        """获取视频信息"""
        try:
            import cv2

            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened():
                return {}

            fps = cap.get(cv2.CAP_PROP_FPS)
            frame_count = cap.get(cv2.CAP_PROP_FRAME_COUNT)
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            duration = frame_count / fps if fps > 0 else 0

            cap.release()

            file_size = os.path.getsize(video_path)

            return {
                'duration': duration,
                'fps': int(fps),
                'resolution': (width, height),
                'file_size': file_size
            }

        except ImportError:
            logger.warning("OpenCV未安装，无法获取视频详细信息")
            return {'file_size': os.path.getsize(video_path)}
        except Exception as e:
            logger.warning(f"获取视频信息失败: {e}")
            return {'file_size': os.path.getsize(video_path)}

    async def shutdown(self):
        """关闭引擎"""
        if self.session:
            await self.session.close()
            self.session = None
        self.status = VideoEngineStatus.OFFLINE
        logger.info("CogVideoX-Flash引擎已关闭")
