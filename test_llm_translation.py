#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LLM翻译功能测试脚本
用于验证LLM翻译是否正常工作
"""

import sys
import os

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_llm_translation():
    print("🧪 LLM翻译功能测试")
    print("=" * 50)
    
    try:
        from src.utils.enhanced_translator import get_translator
        
        # 获取翻译器实例
        translator = get_translator()
        
        # 测试文本
        test_texts = [
            "林肯在书房内，烛光摇曳，他在阅读妻子玛丽的信",
            "一个美丽的花园，阳光明媚，鸟儿在歌唱",
            "镜头特写：主角的眼神充满了坚定和决心",
            "全景镜头：广阔的草原上，一匹白马在奔跑"
        ]
        
        print("📝 测试文本翻译...")
        print("-" * 30)
        
        success_count = 0
        total_count = len(test_texts)
        
        for i, text in enumerate(test_texts, 1):
            print(f"\n测试 {i}/{total_count}:")
            print(f"原文: {text}")
            
            try:
                result = translator.translate_text(text, 'zh', 'en')
                if result and result != text:  # 确保有翻译结果且不是原文
                    print(f"译文: {result}")
                    print("✅ 翻译成功")
                    success_count += 1
                else:
                    print("❌ 翻译失败或返回原文")
            except Exception as e:
                print(f"❌ 翻译异常: {e}")
        
        print(f"\n📊 测试结果:")
        print(f"成功: {success_count}/{total_count}")
        print(f"成功率: {success_count/total_count*100:.1f}%")
        
        if success_count > 0:
            print("\n🎉 LLM翻译功能正常工作！")
            return True
        else:
            print("\n⚠️ LLM翻译功能存在问题")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        return False

def test_translation_config():
    print("\n🔧 翻译配置检查")
    print("=" * 50)
    
    try:
        from config.translation_config import ENABLE_TRANSLATION, TRANSLATION_PRIORITY
        
        print(f"翻译功能启用: {ENABLE_TRANSLATION}")
        print(f"翻译优先级: {TRANSLATION_PRIORITY}")
        
        if ENABLE_TRANSLATION and 'llm' in TRANSLATION_PRIORITY:
            if TRANSLATION_PRIORITY[0] == 'llm':
                print("✅ LLM翻译已设置为最高优先级")
            else:
                print(f"⚠️ LLM翻译优先级: {TRANSLATION_PRIORITY.index('llm') + 1}")
            return True
        else:
            print("❌ 翻译功能未启用或LLM翻译未配置")
            return False
            
    except Exception as e:
        print(f"❌ 配置检查失败: {e}")
        return False

def test_llm_api_availability():
    print("\n🤖 LLM API可用性检查")
    print("=" * 50)
    
    try:
        from src.utils.enhanced_translator import get_translator
        
        translator = get_translator()
        
        # 尝试自动配置LLM API
        if not translator.llm_api:
            translator._auto_configure_llm_api()
        
        if translator.llm_api:
            print("✅ LLM API已配置")
            
            # 测试LLM API是否可用
            try:
                test_result = translator.llm_api.rewrite_text("测试")
                if test_result:
                    print("✅ LLM API响应正常")
                    return True
                else:
                    print("⚠️ LLM API无响应")
                    return False
            except Exception as e:
                print(f"⚠️ LLM API测试失败: {e}")
                return False
        else:
            print("❌ LLM API未配置")
            return False
            
    except Exception as e:
        print(f"❌ LLM API检查失败: {e}")
        return False

def main():
    print("🚀 LLM翻译功能完整测试")
    print("=" * 60)
    
    # 测试配置
    config_ok = test_translation_config()
    
    # 测试LLM API
    api_ok = test_llm_api_availability()
    
    # 测试翻译功能
    translation_ok = False
    if config_ok and api_ok:
        translation_ok = test_llm_translation()
    
    # 总结
    print("\n" + "=" * 60)
    print("📋 测试总结")
    print("=" * 60)
    print(f"配置检查: {'✅ 通过' if config_ok else '❌ 失败'}")
    print(f"LLM API: {'✅ 可用' if api_ok else '❌ 不可用'}")
    print(f"翻译功能: {'✅ 正常' if translation_ok else '❌ 异常'}")
    
    if config_ok and api_ok and translation_ok:
        print("\n🎉 LLM翻译功能配置成功，可以正常使用！")
        print("\n💡 建议:")
        print("- 现在可以重启主程序，翻译功能将使用LLM进行翻译")
        print("- LLM翻译质量较好，适合图像描述等专业内容")
        print("- 如果需要调整翻译优先级，可以修改 config/translation_config.py")
    else:
        print("\n⚠️ LLM翻译功能配置存在问题")
        print("\n🔧 建议:")
        if not config_ok:
            print("- 检查 config/translation_config.py 配置文件")
        if not api_ok:
            print("- 确保主程序中的LLM API已正确配置")
            print("- 检查LLM API密钥和连接")
        if not translation_ok:
            print("- 检查翻译器实现和LLM API调用")

if __name__ == "__main__":
    main()
