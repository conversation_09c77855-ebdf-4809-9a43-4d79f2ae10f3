#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的CogVideoX-Flash API测试
直接测试智谱AI的视频生成API
"""

import asyncio
import aiohttp
import json
import time

# 智谱AI API配置
API_KEY = "ed7ffe9976bb4484ab16647564c0cb27.rI1BXVjDDDURMtJY"
BASE_URL = "https://open.bigmodel.cn/api/paas/v4"

async def test_api_direct():
    """直接测试API"""
    print("🔍 直接测试智谱AI CogVideoX API...")
    
    headers = {
        'Authorization': f'Bearer {API_KEY}',
        'Content-Type': 'application/json'
    }
    
    # 测试数据
    test_data = {
        "model": "cogvideox-flash",
        "prompt": "一朵花在微风中摇摆"
    }
    
    async with aiohttp.ClientSession() as session:
        try:
            # 1. 测试视频生成端点
            print("📡 测试视频生成端点...")
            url = f"{BASE_URL}/videos/generations"
            
            async with session.post(url, headers=headers, json=test_data) as response:
                print(f"   状态码: {response.status}")
                response_text = await response.text()
                print(f"   响应: {response_text[:200]}...")
                
                if response.status == 200:
                    result = await response.json()
                    print("✅ API调用成功!")
                    print(f"   任务ID: {result.get('id', 'N/A')}")
                    return True
                elif response.status == 401:
                    print("❌ API密钥无效")
                    return False
                elif response.status == 400:
                    print("⚠️ 请求参数有问题，但API端点可访问")
                    return True
                else:
                    print(f"❌ API调用失败，状态码: {response.status}")
                    return False
                    
        except Exception as e:
            print(f"❌ API测试异常: {e}")
            return False

async def test_models_endpoint():
    """测试模型列表端点"""
    print("\n📋 测试模型列表端点...")
    
    headers = {
        'Authorization': f'Bearer {API_KEY}',
        'Content-Type': 'application/json'
    }
    
    async with aiohttp.ClientSession() as session:
        try:
            url = f"{BASE_URL}/models"
            
            async with session.get(url, headers=headers) as response:
                print(f"   状态码: {response.status}")
                response_text = await response.text()
                print(f"   响应: {response_text[:200]}...")
                
                if response.status == 200:
                    result = await response.json()
                    print("✅ 模型列表获取成功!")
                    models = result.get('data', [])
                    print(f"   可用模型数量: {len(models)}")
                    
                    # 查找CogVideoX模型
                    video_models = [m for m in models if 'video' in m.get('id', '').lower()]
                    if video_models:
                        print("   视频模型:")
                        for model in video_models[:3]:
                            print(f"     - {model.get('id', 'N/A')}")
                    
                    return True
                else:
                    print(f"❌ 获取模型列表失败，状态码: {response.status}")
                    return False
                    
        except Exception as e:
            print(f"❌ 模型列表测试异常: {e}")
            return False

async def test_simple_video_generation():
    """测试简单的视频生成"""
    print("\n🎬 测试简单视频生成...")
    
    headers = {
        'Authorization': f'Bearer {API_KEY}',
        'Content-Type': 'application/json'
    }
    
    # 简单的测试数据
    test_data = {
        "model": "cogvideox-flash",
        "prompt": "一朵美丽的花在微风中轻轻摇摆，阳光洒在花瓣上，画面温馨美好"
    }
    
    async with aiohttp.ClientSession() as session:
        try:
            # 提交生成任务
            print("📤 提交视频生成任务...")
            url = f"{BASE_URL}/videos/generations"
            
            async with session.post(url, headers=headers, json=test_data) as response:
                print(f"   状态码: {response.status}")
                
                if response.status != 200:
                    response_text = await response.text()
                    print(f"   错误响应: {response_text}")
                    return False
                
                result = await response.json()
                task_id = result.get('id')
                
                if not task_id:
                    print(f"❌ 未获取到任务ID: {result}")
                    return False
                
                print(f"✅ 任务提交成功，ID: {task_id}")
                
                # 轮询任务状态
                print("⏳ 等待视频生成...")
                max_wait = 300  # 5分钟
                poll_interval = 10  # 10秒查询一次
                start_time = time.time()
                
                while time.time() - start_time < max_wait:
                    # 尝试不同的状态查询端点
                    status_urls = [
                        f"{BASE_URL}/async-result/{task_id}",
                        f"{BASE_URL}/videos/generations/{task_id}",
                        f"{BASE_URL}/videos/{task_id}",
                        f"{BASE_URL}/tasks/{task_id}"
                    ]

                    status_found = False
                    for status_url in status_urls:
                        try:
                            async with session.get(status_url, headers=headers) as status_response:
                                if status_response.status == 200:
                                    status_result = await status_response.json()

                                    # 处理不同的响应格式
                                    if isinstance(status_result, list) and len(status_result) > 0:
                                        status_result = status_result[0]  # 取第一个元素

                                    # 确保status_result是字典
                                    if not isinstance(status_result, dict):
                                        print(f"⚠️ 意外的响应格式: {type(status_result)}")
                                        continue

                                    task_status = status_result.get('task_status', 'UNKNOWN')

                                    elapsed = int(time.time() - start_time)
                                    print(f"   状态: {task_status} (已等待 {elapsed}s) [端点: {status_url.split('/')[-2]}]")

                                    if task_status == 'SUCCESS':
                                        video_result = status_result.get('video_result', [])

                                        # video_result是一个列表，取第一个元素
                                        if isinstance(video_result, list) and len(video_result) > 0:
                                            video_info = video_result[0]
                                            video_url = video_info.get('url')
                                        else:
                                            video_url = None

                                        if video_url:
                                            print(f"✅ 视频生成成功!")
                                            print(f"   视频URL: {video_url}")
                                            print(f"   生成时间: {elapsed}秒")
                                            return True
                                        else:
                                            print(f"❌ 成功但未获取到视频URL: {status_result}")
                                            return False

                                    elif task_status == 'FAIL':
                                        error_info = status_result.get('error', {})
                                        print(f"❌ 视频生成失败: {error_info}")
                                        return False

                                    elif task_status in ['PROCESSING', 'SUBMITTED']:
                                        status_found = True
                                        break  # 找到有效端点，跳出循环

                                    else:
                                        print(f"⚠️ 未知状态: {task_status}")
                                        status_found = True
                                        break

                        except Exception as e:
                            print(f"   尝试端点 {status_url} 失败: {e}")
                            continue

                    if not status_found:
                        print("❌ 所有状态查询端点都失败")
                        break

                    await asyncio.sleep(poll_interval)
                
                print(f"❌ 视频生成超时 (超过 {max_wait} 秒)")
                return False
                
        except Exception as e:
            print(f"❌ 视频生成测试异常: {e}")
            return False

async def main():
    """主测试函数"""
    print("=" * 60)
    print("🧪 智谱AI CogVideoX-Flash API 直接测试")
    print("=" * 60)
    print(f"API密钥: {API_KEY[:8]}...")
    print(f"API端点: {BASE_URL}")
    print()
    
    tests = [
        ("模型列表测试", test_models_endpoint),
        ("API直接测试", test_api_direct),
        ("简单视频生成", test_simple_video_generation),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"{'='*20} {test_name} {'='*20}")
        try:
            result = await test_func()
            results.append((test_name, result))
            
            if result:
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
                
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
            results.append((test_name, False))
        
        print()
    
    # 输出结果汇总
    print("=" * 60)
    print("📋 测试结果汇总:")
    print("=" * 60)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:20} : {status}")
    
    success_count = sum(1 for _, result in results if result)
    total_count = len(results)
    
    print(f"\n📊 总计: {success_count}/{total_count} 测试通过")
    
    if success_count > 0:
        print("🎉 基本API功能可用!")
    else:
        print("❌ API测试全部失败，请检查配置")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n👋 测试已取消")
    except Exception as e:
        print(f"\n❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
