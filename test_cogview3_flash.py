#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CogView-3-Flash 集成测试脚本
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.models.engines.cogview3_flash_engine import CogView3FlashEngine
from src.models.image_engine_base import GenerationConfig, EngineType
from src.utils.logger import logger
from config.image_generation_config import get_config


async def test_cogview3_flash_engine():
    """测试CogView-3-Flash引擎"""
    print("🧪 开始测试CogView-3-Flash引擎集成")
    print("=" * 50)
    
    # 获取配置
    config = get_config('development')
    cogview_config = config['engines']['cogview_3_flash']
    
    print(f"📋 配置信息:")
    print(f"  - API密钥: {cogview_config['api_key'][:10]}...")
    print(f"  - 基础URL: {cogview_config['base_url']}")
    print(f"  - 模型: {cogview_config['model']}")
    print(f"  - 超时时间: {cogview_config['timeout']}秒")
    print()
    
    # 创建引擎实例
    print("🔧 创建CogView-3-Flash引擎实例...")
    engine = CogView3FlashEngine(cogview_config)
    
    try:
        # 测试初始化
        print("🚀 初始化引擎...")
        if await engine.initialize():
            print("✅ 引擎初始化成功")
        else:
            print("❌ 引擎初始化失败")
            return False
        
        # 测试连接
        print("🔗 测试API连接...")
        if await engine.test_connection():
            print("✅ API连接测试成功")
        else:
            print("❌ API连接测试失败")
            return False
        
        # 获取引擎信息
        print("ℹ️ 获取引擎信息...")
        engine_info = engine.get_engine_info()
        print(f"  - 引擎名称: {engine_info.name}")
        print(f"  - 版本: {engine_info.version}")
        print(f"  - 描述: {engine_info.description}")
        print(f"  - 是否免费: {engine_info.is_free}")
        print(f"  - 支持批量: {engine_info.supports_batch}")
        print(f"  - 最大批量: {engine_info.max_batch_size}")
        print(f"  - 支持尺寸: {len(engine_info.supported_sizes)}种")
        print()
        
        # 测试图像生成
        print("🎨 测试图像生成...")
        generation_config = GenerationConfig(
            prompt="一只可爱的小猫坐在花园里，阳光明媚，高质量，详细",
            width=1024,
            height=1024,
            batch_size=1
        )
        
        def progress_callback(message):
            print(f"  📊 {message}")
        
        result = await engine.generate(
            generation_config, 
            progress_callback=progress_callback
        )
        
        if result.success:
            print("✅ 图像生成成功!")
            print(f"  - 生成时间: {result.generation_time:.2f}秒")
            print(f"  - 生成图片数量: {len(result.image_paths)}")
            print(f"  - 成本: ${result.cost:.4f}")
            print(f"  - 引擎类型: {result.engine_type.value}")
            
            for i, path in enumerate(result.image_paths):
                print(f"  - 图片{i+1}: {path}")
                if os.path.exists(path):
                    file_size = os.path.getsize(path) / 1024  # KB
                    print(f"    文件大小: {file_size:.1f} KB")
                else:
                    print(f"    ⚠️ 文件不存在")
        else:
            print("❌ 图像生成失败")
            print(f"  错误信息: {result.error_message}")
            return False
        
        print()
        print("🎉 所有测试通过!")
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中发生异常: {e}")
        logger.error(f"CogView-3-Flash测试异常: {e}")
        return False
        
    finally:
        # 清理资源
        print("🧹 清理资源...")
        await engine.cleanup()


async def test_engine_type_integration():
    """测试引擎类型集成"""
    print("🔧 测试引擎类型集成...")
    
    # 测试EngineType枚举
    try:
        cogview_type = EngineType.COGVIEW_3_FLASH
        print(f"✅ EngineType.COGVIEW_3_FLASH = {cogview_type.value}")
    except AttributeError:
        print("❌ EngineType.COGVIEW_3_FLASH 未定义")
        return False
    
    # 测试引擎工厂
    try:
        from src.models.image_engine_factory import EngineFactory
        factory = EngineFactory()
        
        config = get_config('development')
        cogview_config = config['engines']['cogview_3_flash']
        
        engine = await factory.create_engine(EngineType.COGVIEW_3_FLASH, cogview_config)
        if engine:
            print("✅ 引擎工厂创建CogView-3-Flash引擎成功")
            await engine.cleanup()
        else:
            print("❌ 引擎工厂创建CogView-3-Flash引擎失败")
            return False
            
    except Exception as e:
        print(f"❌ 引擎工厂测试失败: {e}")
        return False
    
    return True


async def main():
    """主测试函数"""
    print("🚀 CogView-3-Flash 集成测试开始")
    print("=" * 60)
    
    # 测试引擎类型集成
    if not await test_engine_type_integration():
        print("❌ 引擎类型集成测试失败")
        return
    
    print()
    
    # 测试引擎功能
    if await test_cogview3_flash_engine():
        print("🎉 CogView-3-Flash 集成测试全部通过!")
    else:
        print("❌ CogView-3-Flash 集成测试失败")


if __name__ == "__main__":
    asyncio.run(main())
