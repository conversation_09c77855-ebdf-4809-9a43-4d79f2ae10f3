# 统一错误处理机制使用指南

## 📋 概述

AI视频生成器项目已经实现了完善的统一错误处理机制，包括智能错误分类、用户友好的错误提示、自动恢复机制等功能。本指南将帮助开发者正确使用这些功能。

## 🏗️ 错误处理架构

### 核心组件

1. **ErrorHandler** - 主要的错误处理器
2. **ErrorClassifier** - 错误分类器
3. **SolutionProvider** - 解决方案提供器
4. **UserMessageGenerator** - 用户友好消息生成器
5. **RetryMechanism** - 重试机制
6. **NetworkChecker** - 网络状态检查器

### 错误分类

系统自动将错误分为以下类别：
- `NETWORK` - 网络连接问题
- `FILE_IO` - 文件读写问题
- `API` - API调用问题
- `MEMORY` - 内存不足问题
- `PERMISSION` - 权限问题
- `VALIDATION` - 数据验证问题
- `UI` - 界面相关问题
- `UNKNOWN` - 未知错误

## 🚀 使用方法

### 1. 基本错误处理

```python
from src.utils.error_handler import handle_error

try:
    # 可能出错的代码
    result = some_risky_operation()
except Exception as e:
    # 使用统一错误处理
    error_info = handle_error(e, {
        'source': 'my_function',
        'operation': 'some_risky_operation'
    })
```

### 2. 使用装饰器

```python
from src.utils.error_handler import handle_exception_decorator

@handle_exception_decorator(show_to_user=True, context={'module': 'image_generation'})
def generate_image(prompt):
    # 函数实现
    pass
```

### 3. 安全执行函数

```python
from src.utils.error_handler import safe_execute

# 安全执行，返回默认值而不是抛出异常
result = safe_execute(
    risky_function,
    arg1, arg2,
    default_return=None,
    show_error=True
)
```

### 4. 网络状态检查

```python
from src.utils.error_handler import check_network

if check_network():
    # 网络正常，执行网络操作
    pass
else:
    # 网络异常，显示提示或使用离线模式
    pass
```

## 📝 最佳实践

### 1. 在关键函数中使用错误处理

```python
def critical_function():
    try:
        # 关键业务逻辑
        pass
    except Exception as e:
        handle_error(e, {
            'function': 'critical_function',
            'severity': 'high',
            'user_action': 'Please try again or contact support'
        })
```

### 2. 为AI服务调用添加错误处理

```python
def call_ai_service(prompt):
    try:
        response = ai_api.generate(prompt)
        return response
    except Exception as e:
        handle_error(e, {
            'service': 'ai_api',
            'prompt_length': len(prompt),
            'retry_suggested': True
        })
        return None
```

### 3. 文件操作错误处理

```python
def save_project_data(data, file_path):
    try:
        with open(file_path, 'w') as f:
            json.dump(data, f)
    except Exception as e:
        handle_error(e, {
            'operation': 'save_project',
            'file_path': file_path,
            'data_size': len(str(data))
        })
```

## 🔧 配置选项

### 错误抑制阈值

```python
# 在error_handler.py中配置
error_handler.suppress_threshold = 5  # 同一错误出现5次后开始抑制
```

### 错误历史记录

```python
# 获取错误历史
error_history = error_handler.error_history

# 获取错误统计
error_counts = error_handler.error_counts
```

## 🎯 改进效果

### 已实现的改进

1. **主程序错误处理** ✅
   - 添加了全局异常处理器
   - 集成了统一错误处理机制
   - 改进了错误消息的用户友好性

2. **错误分类和解决方案** ✅
   - 自动错误分类
   - 智能解决方案建议
   - 错误抑制机制

3. **网络状态监控** ✅
   - 实时网络状态检查
   - 网络恢复自动通知
   - 网络相关错误的特殊处理

### 待改进项目

1. **更多模块集成** 🔄
   - 五阶段分镜系统的错误处理优化
   - 图像生成模块的错误处理统一
   - 语音生成模块的错误处理改进

2. **错误恢复机制** 🔄
   - 自动重试失败的操作
   - 智能降级策略
   - 数据恢复机制

## 📊 使用统计

可以通过以下方式获取错误处理统计信息：

```python
from src.utils.error_handler import error_handler

# 获取错误统计
stats = {
    'total_errors': len(error_handler.error_history),
    'error_counts': error_handler.error_counts,
    'suppressed_errors': len(error_handler.suppressed_errors)
}
```

## 🚨 注意事项

1. **不要过度使用错误抑制** - 确保重要错误仍然能够被用户看到
2. **提供有意义的上下文** - 在调用错误处理时提供足够的上下文信息
3. **测试错误场景** - 确保错误处理在各种异常情况下都能正常工作
4. **定期检查错误日志** - 通过日志分析改进系统稳定性

## 🔗 相关文档

- [项目概览](PROJECT_OVERVIEW.md)
- [开发规范](CONTRIBUTING.md)
- [日志系统说明](../src/utils/logger.py)
