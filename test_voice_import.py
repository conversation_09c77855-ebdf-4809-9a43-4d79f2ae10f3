#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试AI配音界面的文本导入功能
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QTextEdit, QLabel
from PyQt5.QtCore import Qt

# 添加项目根目录到路径
sys.path.append('.')

def test_voice_import():
    """测试配音界面的文本导入功能"""
    try:
        print("🚀 测试AI配音界面文本导入功能")
        
        # 导入必要的模块
        from PyQt5.QtWidgets import QApplication
        from src.gui.voice_generation_tab import VoiceGenerationTab
        from src.core.app_controller import AppController
        from src.core.project_manager import ProjectManager
        
        # 创建QApplication
        app = QApplication(sys.argv)
        app.setApplicationName("AI配音界面测试")
        
        print("✅ QApplication创建成功")
        
        # 创建核心组件
        app_controller = AppController()
        project_manager = app_controller.project_manager
        
        print("✅ 核心组件创建成功")
        
        # 创建测试主窗口
        main_window = QMainWindow()
        main_window.setWindowTitle("AI配音界面测试")
        main_window.resize(1200, 800)
        
        # 创建中央部件
        central_widget = QWidget()
        main_window.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 添加说明标签
        info_label = QLabel("🎵 AI配音界面测试 - 新增文本导入功能")
        info_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #2196F3; padding: 10px;")
        layout.addWidget(info_label)
        
        # 模拟文本创作内容
        text_creation_widget = QWidget()
        text_layout = QVBoxLayout(text_creation_widget)
        
        text_label = QLabel("📝 模拟文本创作内容（用于测试导入功能）：")
        text_layout.addWidget(text_label)
        
        text_edit = QTextEdit()
        text_edit.setPlainText("""在一个遥远的星球上，住着一群善良的外星人。他们拥有着超凡的智慧和神奇的能力。

每当夜幕降临，这些外星人就会聚集在一起，分享彼此的故事和经历。他们用心灵感应的方式交流，不需要语言就能理解对方的想法。

有一天，一艘来自地球的飞船意外降落在这个星球上。飞船里的宇航员们惊讶地发现，这里的居民竟然如此友善和智慧。

外星人们热情地接待了这些来自地球的客人。他们展示了自己的科技成果，包括能够治愈任何疾病的神奇药物，以及可以瞬间传送到任何地方的传送门。

宇航员们被这些奇迹深深震撼了。他们意识到，宇宙中还有如此多的未知等待着人类去探索和发现。

最终，在外星人的帮助下，宇航员们成功修复了飞船，准备返回地球。临别时，外星人们赠送给他们一些珍贵的礼物，希望能够促进两个星球之间的友谊与合作。""")
        text_edit.setMaximumHeight(200)
        text_layout.addWidget(text_edit)
        
        layout.addWidget(text_creation_widget)
        
        # 创建配音界面
        voice_tab = VoiceGenerationTab(app_controller, project_manager, main_window)
        
        # 模拟主窗口的text_creation_tab属性
        class MockTextCreationTab:
            def __init__(self, text_widget):
                self.result_text = text_widget
            
            def get_created_text(self):
                return self.result_text.toPlainText()
        
        main_window.text_creation_tab = MockTextCreationTab(text_edit)
        
        layout.addWidget(voice_tab)
        
        print("✅ AI配音界面创建成功")
        
        # 显示窗口
        main_window.show()
        
        print("🎭 AI配音界面测试启动成功！")
        print("\n📋 测试说明：")
        print("1. 在上方文本框中编辑测试文本")
        print("2. 点击配音界面中的'📝 从文本创作导入'按钮")
        print("3. 查看文本是否正确导入并分段")
        print("4. 测试'✏️ 手动输入文本'功能")
        print("5. 验证配音生成功能")
        
        # 启动事件循环
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"💥 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_voice_import()
