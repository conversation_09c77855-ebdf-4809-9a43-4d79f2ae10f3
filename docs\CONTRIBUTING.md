# 贡献指南

感谢您对AI视频生成器项目的关注！我们欢迎各种形式的贡献，包括但不限于：

- 🐛 报告Bug
- 💡 提出新功能建议
- 📝 改进文档
- 🔧 提交代码修复
- ✨ 开发新功能

## 开始之前

### 行为准则
请确保您的行为符合我们的社区标准：
- 尊重他人，保持友善和专业
- 欢迎不同观点和经验水平的贡献者
- 专注于对项目最有利的事情
- 对新手保持耐心和帮助态度

### 开发环境设置
1. Fork本项目到您的GitHub账户
2. 克隆您的Fork到本地
3. 安装开发依赖
4. 创建新的分支进行开发

```bash
git clone https://github.com/yourusername/AI_Video_Generator.git
cd AI_Video_Generator
pip install -r requirements.txt
git checkout -b feature/your-feature-name
```

## 贡献类型

### 🐛 报告Bug

**在提交Bug报告之前，请：**
1. 检查是否已有相同的Issue
2. 确认这确实是一个Bug而不是使用问题
3. 尝试在最新版本中重现问题

**Bug报告应包含：**
- 清晰的标题和描述
- 重现步骤
- 预期行为和实际行为
- 系统环境信息
- 相关的日志文件
- 截图或录屏（如适用）

**Bug报告模板：**
```markdown
## Bug描述
简要描述遇到的问题

## 重现步骤
1. 打开应用
2. 点击...
3. 输入...
4. 看到错误

## 预期行为
描述您期望发生的情况

## 实际行为
描述实际发生的情况

## 环境信息
- 操作系统: [例如 Windows 11]
- Python版本: [例如 3.9.0]
- 应用版本: [例如 4.0.0]

## 附加信息
添加任何其他有助于解决问题的信息
```

### 💡 功能建议

**功能建议应包含：**
- 功能的详细描述
- 使用场景和用户价值
- 可能的实现方案
- 相关的设计图或原型

### 📝 文档改进

文档贡献包括：
- 修复错别字和语法错误
- 改进现有文档的清晰度
- 添加缺失的文档
- 翻译文档到其他语言

### 🔧 代码贡献

#### 代码规范
- 使用Python 3.8+语法
- 遵循PEP 8代码风格
- 添加适当的注释和文档字符串
- 编写单元测试（如适用）

#### 提交规范
使用约定式提交格式：
```
<类型>[可选的作用域]: <描述>

[可选的正文]

[可选的脚注]
```

**类型：**
- `feat`: 新功能
- `fix`: Bug修复
- `docs`: 文档更新
- `style`: 代码格式修改
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

**示例：**
```
feat(image-gen): 添加图像预览自动刷新功能

当图像生成完成后，自动刷新预览区域显示新生成的图像，
提升用户体验。

Closes #123
```

## 开发流程

### 1. 创建Issue
- 对于重大更改，请先创建Issue讨论
- 描述您想要解决的问题或添加的功能
- 等待维护者的反馈和批准

### 2. 开发
- 从main分支创建新的功能分支
- 保持提交历史清晰，每个提交只做一件事
- 定期从main分支合并最新更改

### 3. 测试
- 确保所有现有测试通过
- 为新功能添加测试
- 手动测试您的更改

### 4. 提交Pull Request
- 填写PR模板
- 链接相关的Issue
- 描述您的更改和测试情况
- 请求代码审查

### Pull Request模板
```markdown
## 更改描述
简要描述此PR的更改内容

## 相关Issue
Closes #(issue编号)

## 更改类型
- [ ] Bug修复
- [ ] 新功能
- [ ] 文档更新
- [ ] 代码重构
- [ ] 其他

## 测试
- [ ] 现有测试通过
- [ ] 添加了新测试
- [ ] 手动测试完成

## 检查清单
- [ ] 代码遵循项目规范
- [ ] 自我审查了代码
- [ ] 添加了必要的注释
- [ ] 更新了相关文档
- [ ] 没有引入新的警告
```

## 代码审查

### 审查标准
- 代码质量和可读性
- 功能正确性
- 性能影响
- 安全考虑
- 文档完整性

### 审查流程
1. 维护者会在48小时内响应PR
2. 可能需要多轮审查和修改
3. 所有反馈都应得到解决
4. 至少需要一个维护者批准

## 发布流程

### 版本管理
- 使用语义化版本控制
- 主要版本：不兼容的API更改
- 次要版本：向后兼容的新功能
- 补丁版本：向后兼容的Bug修复

### 发布步骤
1. 更新版本号
2. 更新CHANGELOG.md
3. 创建发布标签
4. 发布到GitHub Releases

## 获得帮助

如果您需要帮助或有疑问：

1. **查看文档**: 首先查看项目文档和FAQ
2. **搜索Issue**: 查看是否已有相关讨论
3. **创建Issue**: 描述您的问题或疑问
4. **联系维护者**: 通过GitHub或其他方式联系

## 认可贡献者

我们会在以下地方认可贡献者：
- README.md中的贡献者列表
- 发布说明中的感谢
- GitHub Contributors页面

## 许可证

通过贡献代码，您同意您的贡献将在MIT许可证下发布。

---

再次感谢您的贡献！每一个贡献都让这个项目变得更好。
