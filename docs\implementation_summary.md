# 五阶段分镜系统失败检测机制实现总结

## 实现概述

已成功在五阶段分镜系统中实现了完整的失败检测机制，用于检测分镜生成和增强描述的失败情况，并提供用户友好的重试功能。

## 核心功能

### 1. 失败检测
- **分镜生成失败检测**：自动检测每个场景的分镜生成是否成功
- **增强描述失败检测**：自动检测每个场景的增强描述是否成功
- **智能判断标准**：基于内容长度、错误关键词、必要元素等多维度判断

### 2. 用户界面
- **失败检测对话框**：当检测到失败时自动弹出
- **分类显示**：分镜失败和增强描述失败分别显示在不同标签页
- **详细信息**：显示具体的错误原因和失败场景信息
- **选择功能**：支持全选/取消全选，或手动选择特定项目

### 3. 重试机制
- **选择性重试**：用户可以选择需要重试的特定场景
- **批量重试**：支持一键重试所有失败项目
- **进度显示**：重试过程中显示详细进度信息
- **结果反馈**：重试完成后提供详细的成功/失败统计

## 技术实现

### 1. 新增文件
- `src/gui/failure_detection_dialog.py` - 失败检测对话框
- `docs/failure_detection_system.md` - 系统使用说明
- `test_failure_detection.py` - 测试脚本

### 2. 修改文件
- `src/gui/five_stage_storyboard_tab.py` - 主要实现文件

### 3. 核心类和方法

#### 失败检测结果类
```python
class FailureDetectionResult:
    def __init__(self):
        self.failed_storyboards = []  # 失败的分镜列表
        self.failed_enhancements = []  # 失败的增强描述列表
        self.has_failures = False
        self.error_details = {}
```

#### 增强线程类
```python
class EnhancementThread(QThread):
    finished = pyqtSignal(bool, str)
    error = pyqtSignal(str)
    enhancement_failed = pyqtSignal(list)  # 新增失败信号
```

#### 工作线程类
```python
class StageWorkerThread(QThread):
    storyboard_failed = pyqtSignal(list)  # 新增失败信号
```

### 4. 关键检测方法

#### 分镜生成失败检测
```python
def _is_storyboard_generation_failed(self, response):
    # 检查空值和类型
    if not response or not isinstance(response, str):
        return True
    
    # 检查错误关键词
    error_patterns = ['api错误', '请求超时', '网络错误', ...]
    if any(pattern in response.lower() for pattern in error_patterns):
        return True
    
    # 检查内容长度
    if len(response.strip()) < 50:
        return True
    
    # 检查必要元素
    required_elements = ['镜头', '画面描述']
    has_required_elements = any(element in response for element in required_elements)
    if len(response.strip()) >= 50 and not has_required_elements:
        return True
    
    return False
```

#### 增强描述成功检测
```python
def _is_enhancement_successful(self, enhanced_result):
    # 检查结果格式
    if not enhanced_result or not isinstance(enhanced_result, dict):
        return False
    
    # 检查内容
    enhanced_content = enhanced_result.get('enhanced_description', '')
    if not enhanced_content or len(enhanced_content.strip()) < 20:
        return False
    
    # 检查错误关键词
    error_patterns = ['api错误', '请求超时', '网络错误', ...]
    if any(pattern in enhanced_content.lower() for pattern in error_patterns):
        return False
    
    return True
```

### 5. 重试方法

#### 单个分镜重试
```python
def _retry_single_storyboard(self, scene_index, scene_info, world_bible, scenes_analysis):
    # 重新构建提示词
    # 调用LLM API
    # 检测生成结果
    # 更新分镜数据
    return success
```

#### 单个增强描述重试
```python
def _retry_single_enhancement(self, scene_index, scene_info):
    # 获取对应分镜脚本
    # 创建场景描述增强器
    # 调用增强功能
    # 检测增强结果
    return success
```

## 信号连接

### 分镜失败检测
```python
self.worker_thread.storyboard_failed.connect(self.on_storyboard_failed)
```

### 增强描述失败检测
```python
self.enhancement_thread.enhancement_failed.connect(self.on_enhancement_failed)
```

## 用户体验

### 1. 自动检测
- 用户无需手动检查，系统自动检测失败情况
- 检测到失败时立即弹出对话框提醒用户

### 2. 友好界面
- 清晰的错误分类和描述
- 直观的选择和操作界面
- 详细的进度反馈

### 3. 灵活重试
- 支持选择性重试特定场景
- 支持批量重试所有失败项目
- 重试过程可以随时取消

## 测试验证

### 测试用例
1. 空字符串 → 检测为失败 ✓
2. 包含错误信息 → 检测为失败 ✓
3. 内容过短 → 检测为失败 ✓
4. 正常内容 → 检测为成功 ✓
5. 非字符串类型 → 检测为失败 ✓

### 测试结果
所有测试用例均通过，检测逻辑准确可靠。

## 部署说明

### 1. 文件部署
- 确保所有新增文件已正确放置
- 检查导入路径是否正确

### 2. 依赖检查
- PyQt5 相关组件
- 现有的LLM API和场景增强器

### 3. 配置验证
- 确保LLM API配置正确
- 验证项目管理器功能正常

## 使用指南

### 1. 正常使用
用户正常使用五阶段分镜系统，当出现失败时会自动弹出检测对话框。

### 2. 重试操作
1. 在失败检测对话框中选择需要重试的项目
2. 点击"重试选中项目"或"重试全部"
3. 等待重试完成，查看结果反馈

### 3. 故障排除
如果重试仍然失败，建议：
- 检查网络连接
- 验证API配置
- 查看系统日志
- 联系技术支持

## 总结

该失败检测机制显著提升了用户体验，解决了与大模型通讯超时导致的失败问题。通过智能检测、友好界面和灵活重试，用户可以更高效地完成分镜生成和增强描述工作。
