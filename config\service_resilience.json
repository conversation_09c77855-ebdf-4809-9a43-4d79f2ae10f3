{"services": [{"service_name": "llm_service", "providers": ["deepseek", "tongyi", "<PERSON><PERSON><PERSON>", "google", "openai", "siliconflow"], "primary_provider": "deepseek", "fallback_providers": ["tongyi", "<PERSON><PERSON><PERSON>", "google"], "circuit_breaker": {"failure_threshold": 5, "timeout_threshold": 30.0, "recovery_timeout": 60.0, "half_open_max_calls": 3}, "retry_config": {"max_retries": 3, "base_delay": 1.0, "max_delay": 30.0, "backoff_factor": 2.0, "jitter": true}, "health_check_interval": 30.0, "degradation_threshold": 0.7}, {"service_name": "image_service", "providers": ["pollinations", "comfyui_local", "comfyui_cloud", "<PERSON><PERSON>_dalle", "stability_ai"], "primary_provider": "pollinations", "fallback_providers": ["comfyui_local", "<PERSON><PERSON>_dalle"], "circuit_breaker": {"failure_threshold": 3, "timeout_threshold": 60.0, "recovery_timeout": 120.0, "half_open_max_calls": 2}, "retry_config": {"max_retries": 2, "base_delay": 2.0, "max_delay": 60.0, "backoff_factor": 2.0, "jitter": true}, "health_check_interval": 45.0, "degradation_threshold": 0.6}, {"service_name": "voice_service", "providers": ["edge_tts", "azure", "elevenlabs", "openai"], "primary_provider": "edge_tts", "fallback_providers": ["azure", "openai"], "circuit_breaker": {"failure_threshold": 4, "timeout_threshold": 20.0, "recovery_timeout": 45.0, "half_open_max_calls": 3}, "retry_config": {"max_retries": 2, "base_delay": 1.5, "max_delay": 20.0, "backoff_factor": 1.5, "jitter": true}, "health_check_interval": 30.0, "degradation_threshold": 0.8}, {"service_name": "video_service", "providers": ["cogvideox_flash", "local_video", "cloud_video"], "primary_provider": "cogvideox_flash", "fallback_providers": ["local_video"], "circuit_breaker": {"failure_threshold": 2, "timeout_threshold": 120.0, "recovery_timeout": 300.0, "half_open_max_calls": 1}, "retry_config": {"max_retries": 1, "base_delay": 5.0, "max_delay": 120.0, "backoff_factor": 2.0, "jitter": false}, "health_check_interval": 60.0, "degradation_threshold": 0.5}], "global_settings": {"enable_circuit_breaker": true, "enable_retry": true, "enable_degradation": true, "enable_health_check": true, "metrics_retention_days": 7, "alert_thresholds": {"error_rate": 0.1, "response_time": 10.0, "consecutive_failures": 3}}, "degradation_strategies": {"llm_service": {"strategy": "use_cache", "description": "使用缓存的响应或简化的模型"}, "image_service": {"strategy": "use_placeholder", "description": "使用占位图像或降低图像质量"}, "voice_service": {"strategy": "use_local_tts", "description": "使用本地TTS引擎"}, "video_service": {"strategy": "skip_video", "description": "跳过视频生成，仅使用图像"}}, "fallback_responses": {"llm_service": {"type": "cached_response", "message": "AI服务暂时不可用，请稍后重试"}, "image_service": {"type": "placeholder_image", "path": "assets/placeholder.png"}, "voice_service": {"type": "text_only", "message": "语音服务暂时不可用，仅显示文本"}, "video_service": {"type": "image_slideshow", "message": "视频服务暂时不可用，将生成图像幻灯片"}}}