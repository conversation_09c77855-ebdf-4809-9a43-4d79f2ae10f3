"""
测试新的配音驱动UI界面
"""

import sys
import os
import logging
from pathlib import Path

# 添加项目根目录到路径
sys.path.append('.')

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_new_ui():
    """测试新的UI界面"""
    try:
        print("🚀 启动AI视频生成器 - 配音驱动版本")
        
        # 导入必要的模块
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        from src.gui.new_main_window import NewMainWindow

        # 创建QApplication
        app = QApplication(sys.argv)
        app.setApplicationName("AI视频生成器 - 配音驱动版")
        app.setApplicationVersion("2.0")

        print("✅ QApplication创建成功")

        # 创建主界面（NewMainWindow内部会自动创建核心组件）
        main_window = NewMainWindow()

        print("✅ 主界面和核心组件创建成功")
        
        print("✅ 主界面创建成功")
        
        # 设置窗口属性
        main_window.setWindowTitle("AI视频生成器 - 配音驱动工作流程")
        main_window.resize(1400, 900)
        
        # 显示主界面
        main_window.show()
        
        print("🎭 配音驱动工作流程界面已启动！")
        print("\n📋 新功能介绍：")
        print("1. 🎭 工作流程指南 - 引导您使用配音驱动工作流程")
        print("2. 📝 文本创作 - AI创作或文本改写")
        print("3. 🎵 AI配音生成 - 配音驱动的核心步骤")
        print("4. 🎭 配音驱动分镜 - 基于配音内容的五阶段分镜")
        print("5. 🎨 一致性控制 - 角色场景管理")
        print("6. 🖼️ 图像生成 - 基于配音内容的图像生成")
        print("7. 🎬 视频合成 - 最终视频制作")
        print("8. ⚙️ 设置 - 系统配置")
        
        print("\n💡 使用提示：")
        print("• 首次使用请查看'工作流程指南'标签页")
        print("• 按照标签页顺序操作，享受配音驱动的完美体验")
        print("• 配音驱动工作流程确保内容100%一致")
        
        # 运行应用
        return app.exec_()
        
    except Exception as e:
        print(f"💥 启动失败: {e}")
        import traceback
        traceback.print_exc()
        return 1

def main():
    """主函数"""
    print("=" * 60)
    print("🎭 AI视频生成器 - 配音驱动工作流程测试")
    print("=" * 60)
    
    # 检查环境
    if not os.path.exists("src"):
        print("❌ 错误：请在项目根目录运行此脚本")
        return 1
    
    # 启动UI测试
    exit_code = test_new_ui()
    
    print("\n" + "=" * 60)
    if exit_code == 0:
        print("✅ UI测试完成")
    else:
        print("❌ UI测试失败")
    print("=" * 60)
    
    return exit_code

if __name__ == "__main__":
    sys.exit(main())
