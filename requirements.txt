# AI视频生成器 - 核心依赖
# 基于实际代码分析的最小依赖集合

# 核心GUI框架
PyQt5>=5.15.0
PyQtWebEngine>=5.15.0

# 网络请求
requests>=2.28.0
aiohttp>=3.8.0
aiofiles>=0.8.0

# 图像处理
Pillow>=9.0.0
numpy>=1.21.0

# 视频处理 (可选，用于视频合成功能)
moviepy>=1.0.3

# 音频处理 (可选，用于语音合成功能)
pydub>=0.25.1

# TTS引擎依赖
edge-tts>=6.1.0

# 数据处理
pandas>=1.4.0

# 工具库
tqdm>=4.64.0
colorama>=0.4.5

# 开发和调试工具 (可选)
# pytest>=7.1.0
# black>=22.6.0
# flake8>=5.0.0

# 注意事项：
# 1. 本项目主要依赖PyQt5进行GUI开发
# 2. 网络功能用于调用各种AI服务API
# 3. 图像处理用于预览和基本操作
# 4. 视频和音频处理功能为可选模块
# 5. 大部分AI功能通过API调用实现，无需本地AI库
