# -*- coding: utf-8 -*-
"""
增强翻译服务模块
支持多种翻译服务的级联调用：百度翻译 → Google翻译 → LLM翻译
"""

import logging
import requests
import time
from typing import Optional, Dict, Any
from urllib.parse import quote

logger = logging.getLogger(__name__)

class EnhancedTranslator:
    """增强翻译器，支持多种翻译服务的级联调用"""
    
    def __init__(self, llm_api=None, enable_translation=None):
        """
        初始化增强翻译器

        Args:
            llm_api: LLM API实例，用于LLM翻译
            enable_translation: 是否启用翻译功能，None时从配置文件读取
        """
        self.llm_api = llm_api

        # 从配置文件读取翻译开关设置
        if enable_translation is None:
            try:
                from config.translation_config import ENABLE_TRANSLATION
                self.enable_translation = ENABLE_TRANSLATION
            except ImportError:
                logger.warning("无法导入翻译配置，使用默认设置")
                self.enable_translation = True
        else:
            self.enable_translation = enable_translation

        self._init_baidu_translator()
        
    def _init_baidu_translator(self):
        """初始化百度翻译"""
        try:
            from src.utils.baidu_translator import translate_text, is_configured as is_baidu_configured
            self.baidu_translate = translate_text
            self.is_baidu_configured = is_baidu_configured
            logger.info("百度翻译服务初始化成功")
        except ImportError as e:
            logger.warning(f"百度翻译模块导入失败: {e}")
            self.baidu_translate = None
            self.is_baidu_configured = lambda: False
    
    def translate_text(self, text: str, from_lang: str = 'zh', to_lang: str = 'en') -> Optional[str]:
        """
        翻译文本，使用级联翻译策略：百度 → Google → LLM

        Args:
            text: 待翻译的文本
            from_lang: 源语言，默认为中文(zh)
            to_lang: 目标语言，默认为英文(en)

        Returns:
            翻译结果，失败时返回原文
        """
        if not text or not text.strip():
            logger.warning("翻译文本为空")
            return None

        text = text.strip()

        # 检查翻译开关
        if not self.enable_translation:
            logger.info("翻译功能已禁用，返回原文")
            return text

        logger.info(f"开始翻译文本: {text[:50]}...")

        # 根据配置的优先级顺序尝试翻译服务
        try:
            from config.translation_config import TRANSLATION_PRIORITY
            priority_order = TRANSLATION_PRIORITY
        except ImportError:
            # 如果配置文件不存在，使用默认顺序（LLM优先）
            priority_order = ['llm', 'baidu', 'google']

        for service in priority_order:
            if service == 'llm':
                result = self._try_llm_translate(text, from_lang, to_lang)
                if result:
                    logger.info("LLM翻译成功")
                    return result
            elif service == 'baidu':
                result = self._try_baidu_translate(text, from_lang, to_lang)
                if result:
                    logger.info("百度翻译成功")
                    return result
            elif service == 'google':
                result = self._try_google_translate(text, from_lang, to_lang)
                if result:
                    logger.info("Google翻译成功")
                    return result

        # 4. 如果所有翻译都失败，返回原文并记录警告
        logger.warning("所有翻译方法都失败了，返回原文")
        return text  # 返回原文而不是None，确保程序能继续运行
    
    def _try_baidu_translate(self, text: str, from_lang: str, to_lang: str) -> Optional[str]:
        """尝试百度翻译"""
        if not self.baidu_translate or not self.is_baidu_configured():
            logger.debug("百度翻译未配置，跳过")
            return None
        
        try:
            logger.debug("尝试百度翻译")
            result = self.baidu_translate(text, from_lang, to_lang)
            if result and result.strip():
                logger.debug(f"百度翻译结果: {result[:50]}...")
                return result.strip()
            else:
                logger.warning("百度翻译返回空结果")
                return None
        except Exception as e:
            logger.warning(f"百度翻译失败: {e}")
            return None
    
    def _try_google_translate(self, text: str, from_lang: str, to_lang: str) -> Optional[str]:
        """尝试Google翻译（使用免费API）"""
        import time
        import random

        # 添加重试机制
        max_retries = 3
        base_delay = 2.0

        for attempt in range(max_retries):
            try:
                logger.debug(f"尝试Google翻译 (第{attempt + 1}次)")

                # 添加随机延迟，避免频率限制
                if attempt > 0:
                    delay = base_delay * (2 ** attempt) + random.uniform(0.5, 1.5)
                    logger.debug(f"等待 {delay:.1f} 秒后重试...")
                    time.sleep(delay)

                # 使用Google翻译的免费API
                url = "https://translate.googleapis.com/translate_a/single"
                params = {
                    'client': 'gtx',
                    'sl': from_lang,
                    'tl': to_lang,
                    'dt': 't',
                    'q': text
                }

                headers = {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                }

                response = requests.get(url, params=params, headers=headers, timeout=15)

                # 检查HTTP状态码
                if response.status_code == 429:
                    logger.warning(f"Google翻译频率限制 (429)，第{attempt + 1}次尝试")
                    if attempt < max_retries - 1:
                        continue  # 重试
                    else:
                        logger.error("Google翻译频率限制，已达到最大重试次数")
                        return None

                response.raise_for_status()

                result = response.json()

                # 解析Google翻译结果
                if result and len(result) > 0 and result[0]:
                    translated_text = ""
                    for item in result[0]:
                        if item and len(item) > 0:
                            translated_text += item[0]

                    if translated_text.strip():
                        logger.debug(f"Google翻译结果: {translated_text[:50]}...")
                        return translated_text.strip()

                logger.warning("Google翻译返回空结果")
                if attempt < max_retries - 1:
                    continue  # 重试
                else:
                    return None

            except requests.exceptions.Timeout:
                logger.warning(f"Google翻译请求超时 (第{attempt + 1}次)")
                if attempt < max_retries - 1:
                    continue  # 重试
                else:
                    return None
            except requests.exceptions.RequestException as e:
                logger.warning(f"Google翻译请求异常 (第{attempt + 1}次): {e}")
                if attempt < max_retries - 1:
                    continue  # 重试
                else:
                    return None
            except Exception as e:
                logger.warning(f"Google翻译异常 (第{attempt + 1}次): {e}")
                if attempt < max_retries - 1:
                    continue  # 重试
                else:
                    return None

        return None
    
    def _try_llm_translate(self, text: str, from_lang: str, to_lang: str) -> Optional[str]:
        """尝试LLM翻译"""
        # 如果没有LLM API，尝试自动获取
        if not self.llm_api:
            self._auto_configure_llm_api()

        if not self.llm_api:
            logger.debug("LLM API未配置，跳过")
            return None
        
        try:
            logger.debug("尝试LLM翻译")
            
            # 构建翻译提示词
            if from_lang == 'zh' and to_lang == 'en':
                # 检测文本类型，优化提示词
                if any(keyword in text for keyword in ['镜头', '画面', '场景', '特写', '全景', '中景', '近景']):
                    # 图像描述类文本
                    prompt = f"""请将以下中文图像描述翻译成英文。要求：
1. 使用专业的摄影和电影术语
2. 保持描述的视觉准确性和艺术性
3. 翻译要简洁明了，适合AI图像生成
4. 只输出英文翻译结果，不要包含任何解释

中文描述：{text}

英文翻译："""
                else:
                    # 普通文本
                    prompt = f"""请将以下中文文本翻译成英文。要求：
1. 翻译要准确、自然、流畅
2. 保持原文的语义和语调
3. 只输出英文翻译结果，不要包含任何中文或其他说明
4. 保持专业性和可读性

原文：{text}

英文翻译："""
            elif from_lang == 'en' and to_lang == 'zh':
                prompt = f"""请将以下英文文本翻译成中文。要求：
1. 翻译要准确、自然、符合中文表达习惯
2. 保持原文的语义和语调
3. 只输出中文翻译结果，不要包含任何英文或其他说明

原文：{text}

中文翻译："""
            else:
                prompt = f"Please translate the following text from {from_lang} to {to_lang}. Only output the translation result without any explanation:\n\n{text}"
            
            result = self.llm_api.rewrite_text(prompt)
            
            if result and result.strip():
                # 清理LLM返回的结果
                cleaned_result = self._clean_llm_result(result.strip())
                if cleaned_result:
                    logger.debug(f"LLM翻译结果: {cleaned_result[:50]}...")
                    return cleaned_result
            
            logger.warning("LLM翻译返回空结果")
            return None
            
        except Exception as e:
            logger.warning(f"LLM翻译失败: {e}")
            return None
    
    def _clean_llm_result(self, result: str) -> str:
        """清理LLM翻译结果"""
        # 移除常见的LLM回复前缀和后缀
        prefixes_to_remove = [
            "英文翻译：", "中文翻译：", "翻译结果：", "翻译：",
            "Translation:", "Result:", "英文：", "中文：",
            "English:", "Chinese:", "英语：", "中文版：",
            "英文版：", "Translated text:", "翻译文本：",
        ]

        # 移除前缀
        for prefix in prefixes_to_remove:
            if result.startswith(prefix):
                result = result[len(prefix):].strip()

        # 移除引号
        if result.startswith('"') and result.endswith('"'):
            result = result[1:-1]
        if result.startswith("'") and result.endswith("'"):
            result = result[1:-1]
        if result.startswith('"') and result.endswith('"'):  # 中文引号
            result = result[1:-1]
        if result.startswith("「") and result.endswith("」"):  # 日文引号
            result = result[1:-1]

        # 移除多余的换行和空格
        result = result.replace('\n', ' ').strip()

        # 移除重复的空格
        import re
        result = re.sub(r'\s+', ' ', result)

        return result.strip()

    def _auto_configure_llm_api(self):
        """自动配置LLM API"""
        try:
            # 方法1：从应用控制器获取
            from src.core.app_controller import AppController
            app_controller = AppController.get_instance()
            if app_controller and hasattr(app_controller, 'llm_api') and app_controller.llm_api:
                self.llm_api = app_controller.llm_api
                logger.info("已从应用控制器自动配置LLM翻译服务")
                return
        except Exception as e:
            logger.debug(f"无法从应用控制器获取LLM API: {e}")

        try:
            # 方法2：从服务管理器获取
            from src.core.service_manager import ServiceManager
            service_manager = ServiceManager.get_instance()
            if service_manager:
                llm_service = service_manager.get_service('llm')
                if llm_service and hasattr(llm_service, 'api') and llm_service.api:
                    self.llm_api = llm_service.api
                    logger.info("已从服务管理器自动配置LLM翻译服务")
                    return
        except Exception as e:
            logger.debug(f"无法从服务管理器获取LLM API: {e}")

        try:
            # 方法3：直接创建LLM API实例
            from src.models.llm_api import LLMApi
            from src.core.config_manager import ConfigManager

            config_manager = ConfigManager.get_instance()
            if config_manager:
                # 获取当前选择的模型配置
                current_model = config_manager.get_current_model()
                if current_model:
                    model_config = config_manager.get_model_config(current_model)
                    if model_config:
                        self.llm_api = LLMApi(
                            api_type=model_config.get('api_type'),
                            api_key=model_config.get('api_key'),
                            api_url=model_config.get('api_url')
                        )
                        logger.info(f"已自动创建LLM翻译服务，使用模型: {current_model}")
                        return
        except Exception as e:
            logger.debug(f"无法自动创建LLM API: {e}")

        try:
            # 方法4：使用默认配置创建LLM API
            from src.models.llm_api import LLMApi

            # 尝试使用通义千问作为默认配置
            default_config = {
                'api_type': 'tongyi',
                'api_key': 'sk-ab30df729a9b4df287db20a8f47ba12c',  # 从日志中看到的密钥
                'api_url': 'https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions',
                'model_name': 'qwen-plus'
            }

            self.llm_api = LLMApi(
                api_type=default_config['api_type'],
                api_key=default_config['api_key'],
                api_url=default_config['api_url']
            )
            logger.info("已使用默认配置创建LLM翻译服务（通义千问）")
            return
        except Exception as e:
            logger.debug(f"无法使用默认配置创建LLM API: {e}")

        logger.warning("无法自动配置LLM翻译服务")

    def is_available(self) -> bool:
        """检查翻译服务是否可用"""
        return (self.is_baidu_configured() or 
                self._test_google_translate() or 
                (self.llm_api is not None))
    
    def _test_google_translate(self) -> bool:
        """测试Google翻译是否可用"""
        try:
            test_result = self._try_google_translate("测试", "zh", "en")
            return test_result is not None
        except:
            return False
    
    def get_available_services(self) -> list:
        """获取可用的翻译服务列表"""
        services = []
        
        if self.is_baidu_configured():
            services.append("百度翻译")
        
        if self._test_google_translate():
            services.append("Google翻译")
        
        if self.llm_api:
            services.append("LLM翻译")
        
        return services


# 全局翻译器实例
_global_translator = None

def get_translator(llm_api=None) -> EnhancedTranslator:
    """获取全局翻译器实例"""
    global _global_translator
    if _global_translator is None:
        _global_translator = EnhancedTranslator(llm_api)
    elif llm_api and not _global_translator.llm_api:
        _global_translator.llm_api = llm_api

    # 如果没有提供LLM API，尝试自动配置
    if not _global_translator.llm_api and llm_api is None:
        _global_translator._auto_configure_llm_api()

    return _global_translator

def translate_text_enhanced(text: str, from_lang: str = 'zh', to_lang: str = 'en', llm_api=None) -> Optional[str]:
    """
    增强翻译函数，支持多种翻译服务
    
    Args:
        text: 待翻译的文本
        from_lang: 源语言，默认为中文(zh)
        to_lang: 目标语言，默认为英文(en)
        llm_api: LLM API实例
        
    Returns:
        翻译结果，失败时返回None
    """
    translator = get_translator(llm_api)
    return translator.translate_text(text, from_lang, to_lang)
