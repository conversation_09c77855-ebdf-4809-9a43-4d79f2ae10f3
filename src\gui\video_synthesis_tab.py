#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
视频合成标签页
提供用户友好的视频合成界面
"""

import os
import asyncio
from typing import Optional, Dict, Any, List
from pathlib import Path

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, 
    QListWidget, QListWidgetItem, QGroupBox, QSpinBox, QDoubleSpinBox,
    QComboBox, QLineEdit, QTextEdit, QProgressBar, QFileDialog,
    QMessageBox, QSplitter, QTabWidget, QTableWidget, QTableWidgetItem,
    QHeaderView, QCheckBox
)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt5.QtGui import QFont, QPixmap, QIcon

from src.utils.logger import logger
from src.processors.video_synthesis_processor import (
    VideoSynthesisProcessor, SynthesisProject, SynthesisConfig,
    VideoSegment, AudioSegment, ImageSegment, TransitionEffect
)
from src.gui.notification_system import show_success, show_error, show_info, show_loading
from src.gui.loading_manager import start_loading, update_loading, finish_loading

class VideoSynthesisWorker(QThread):
    """视频合成工作线程"""
    
    progress_updated = pyqtSignal(float, str)
    synthesis_completed = pyqtSignal(str)
    synthesis_failed = pyqtSignal(str)
    
    def __init__(self, processor: VideoSynthesisProcessor, project: SynthesisProject, output_filename: str):
        super().__init__()
        self.processor = processor
        self.project = project
        self.output_filename = output_filename
    
    def run(self):
        """执行视频合成"""
        try:
            # 创建异步事件循环
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            def progress_callback(progress: float, message: str):
                self.progress_updated.emit(progress, message)
            
            # 执行合成
            output_path = loop.run_until_complete(
                self.processor.synthesize_video(
                    self.project, 
                    self.output_filename,
                    progress_callback
                )
            )
            
            self.synthesis_completed.emit(output_path)
            
        except Exception as e:
            self.synthesis_failed.emit(str(e))
        finally:
            loop.close()

class VideoSynthesisTab(QWidget):
    """视频合成标签页"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.processor = VideoSynthesisProcessor()
        self.current_project: Optional[SynthesisProject] = None
        self.synthesis_worker: Optional[VideoSynthesisWorker] = None
        
        self.init_ui()
        self.setup_connections()
    
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        
        # 标题
        title_label = QLabel("🎬 视频合成")
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        layout.addWidget(title_label)
        
        # 主要内容区域
        main_splitter = QSplitter(Qt.Horizontal)
        layout.addWidget(main_splitter)
        
        # 左侧：项目管理和配置
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)
        
        # 项目管理组
        project_group = QGroupBox("项目管理")
        project_layout = QVBoxLayout(project_group)
        
        # 项目操作按钮
        project_buttons_layout = QHBoxLayout()
        self.new_project_btn = QPushButton("新建项目")
        self.load_project_btn = QPushButton("加载项目")
        self.save_project_btn = QPushButton("保存项目")
        
        project_buttons_layout.addWidget(self.new_project_btn)
        project_buttons_layout.addWidget(self.load_project_btn)
        project_buttons_layout.addWidget(self.save_project_btn)
        project_layout.addLayout(project_buttons_layout)
        
        # 项目信息
        self.project_info_label = QLabel("未加载项目")
        project_layout.addWidget(self.project_info_label)
        
        left_layout.addWidget(project_group)
        
        # 合成配置组
        config_group = QGroupBox("合成配置")
        config_layout = QVBoxLayout(config_group)
        
        # 分辨率设置
        resolution_layout = QHBoxLayout()
        resolution_layout.addWidget(QLabel("分辨率:"))
        self.width_spinbox = QSpinBox()
        self.width_spinbox.setRange(480, 3840)
        self.width_spinbox.setValue(1920)
        self.height_spinbox = QSpinBox()
        self.height_spinbox.setRange(360, 2160)
        self.height_spinbox.setValue(1080)
        resolution_layout.addWidget(self.width_spinbox)
        resolution_layout.addWidget(QLabel("x"))
        resolution_layout.addWidget(self.height_spinbox)
        resolution_layout.addStretch()
        config_layout.addLayout(resolution_layout)
        
        # 帧率设置
        fps_layout = QHBoxLayout()
        fps_layout.addWidget(QLabel("帧率:"))
        self.fps_spinbox = QSpinBox()
        self.fps_spinbox.setRange(15, 60)
        self.fps_spinbox.setValue(24)
        fps_layout.addWidget(self.fps_spinbox)
        fps_layout.addWidget(QLabel("fps"))
        fps_layout.addStretch()
        config_layout.addLayout(fps_layout)
        
        # 质量预设
        quality_layout = QHBoxLayout()
        quality_layout.addWidget(QLabel("质量预设:"))
        self.quality_combo = QComboBox()
        self.quality_combo.addItems(["ultrafast", "fast", "medium", "slow", "veryslow"])
        self.quality_combo.setCurrentText("medium")
        quality_layout.addWidget(self.quality_combo)
        quality_layout.addStretch()
        config_layout.addLayout(quality_layout)
        
        # 转场效果
        self.enable_transitions_checkbox = QCheckBox("启用转场效果")
        self.enable_transitions_checkbox.setChecked(True)
        config_layout.addWidget(self.enable_transitions_checkbox)
        
        left_layout.addWidget(config_group)
        
        # 合成控制组
        synthesis_group = QGroupBox("合成控制")
        synthesis_layout = QVBoxLayout(synthesis_group)
        
        # 输出文件名
        output_layout = QHBoxLayout()
        output_layout.addWidget(QLabel("输出文件名:"))
        self.output_filename_edit = QLineEdit()
        self.output_filename_edit.setPlaceholderText("留空使用自动生成的文件名")
        output_layout.addWidget(self.output_filename_edit)
        synthesis_layout.addLayout(output_layout)
        
        # 合成按钮
        self.synthesis_btn = QPushButton("🎬 开始合成")
        self.synthesis_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 10px;
                font-size: 14px;
                font-weight: bold;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:disabled {
                background-color: #cccccc;
            }
        """)
        synthesis_layout.addWidget(self.synthesis_btn)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        synthesis_layout.addWidget(self.progress_bar)
        
        # 状态标签
        self.status_label = QLabel("就绪")
        synthesis_layout.addWidget(self.status_label)
        
        left_layout.addWidget(synthesis_group)
        left_layout.addStretch()
        
        main_splitter.addWidget(left_widget)
        
        # 右侧：时间轴和片段管理
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)
        
        # 片段管理标签页
        self.segments_tab_widget = QTabWidget()
        
        # 视频片段标签页
        self.video_segments_table = self.create_segments_table(["ID", "文件路径", "开始时间", "时长", "音量"])
        self.segments_tab_widget.addTab(self.video_segments_table, "视频片段")
        
        # 音频片段标签页
        self.audio_segments_table = self.create_segments_table(["ID", "文件路径", "开始时间", "时长", "音量", "类型"])
        self.segments_tab_widget.addTab(self.audio_segments_table, "音频片段")
        
        # 图像片段标签页
        self.image_segments_table = self.create_segments_table(["ID", "文件路径", "开始时间", "时长"])
        self.segments_tab_widget.addTab(self.image_segments_table, "图像片段")
        
        right_layout.addWidget(self.segments_tab_widget)
        
        # 片段操作按钮
        segments_buttons_layout = QHBoxLayout()
        self.add_video_btn = QPushButton("添加视频")
        self.add_audio_btn = QPushButton("添加音频")
        self.add_image_btn = QPushButton("添加图像")
        self.remove_segment_btn = QPushButton("删除片段")
        
        segments_buttons_layout.addWidget(self.add_video_btn)
        segments_buttons_layout.addWidget(self.add_audio_btn)
        segments_buttons_layout.addWidget(self.add_image_btn)
        segments_buttons_layout.addWidget(self.remove_segment_btn)
        segments_buttons_layout.addStretch()
        
        right_layout.addLayout(segments_buttons_layout)
        
        main_splitter.addWidget(right_widget)
        
        # 设置分割器比例
        main_splitter.setSizes([300, 700])
        
        # 帮助信息
        help_label = QLabel("""
        <div style='background-color: #f0f8ff; padding: 10px; border-radius: 5px; border: 1px solid #d0e7ff;'>
        <b>💡 使用说明：</b><br>
        1. 创建新项目或加载现有项目<br>
        2. 添加视频、音频和图像片段<br>
        3. 配置合成参数<br>
        4. 点击"开始合成"生成最终视频
        </div>
        """)
        help_label.setWordWrap(True)
        layout.addWidget(help_label)
    
    def create_segments_table(self, headers: List[str]) -> QTableWidget:
        """创建片段表格"""
        table = QTableWidget()
        table.setColumnCount(len(headers))
        table.setHorizontalHeaderLabels(headers)
        table.horizontalHeader().setStretchLastSection(True)
        table.setSelectionBehavior(QTableWidget.SelectRows)
        table.setAlternatingRowColors(True)
        return table
    
    def setup_connections(self):
        """设置信号连接"""
        self.new_project_btn.clicked.connect(self.create_new_project)
        self.load_project_btn.clicked.connect(self.load_project)
        self.save_project_btn.clicked.connect(self.save_project)
        self.synthesis_btn.clicked.connect(self.start_synthesis)
        
        self.add_video_btn.clicked.connect(self.add_video_segment)
        self.add_audio_btn.clicked.connect(self.add_audio_segment)
        self.add_image_btn.clicked.connect(self.add_image_segment)
        self.remove_segment_btn.clicked.connect(self.remove_segment)
    
    def create_new_project(self):
        """创建新项目"""
        try:
            project_name = f"video_project_{len(os.listdir(self.processor.output_dir)) + 1}"
            
            config = SynthesisConfig(
                output_width=self.width_spinbox.value(),
                output_height=self.height_spinbox.value(),
                fps=self.fps_spinbox.value(),
                quality_preset=self.quality_combo.currentText(),
                enable_transitions=self.enable_transitions_checkbox.isChecked()
            )
            
            self.current_project = self.processor.create_project(project_name, config)
            self.update_project_info()
            self.refresh_segments_tables()
            
            show_success(f"新项目已创建: {project_name}")
            logger.info(f"创建新的视频合成项目: {project_name}")
            
        except Exception as e:
            show_error(f"创建项目失败: {e}")
            logger.error(f"创建新项目失败: {e}")
    
    def update_project_info(self):
        """更新项目信息显示"""
        if self.current_project:
            info = self.processor.get_project_info(self.current_project)
            info_text = f"""
            项目名称: {info['name']}
            总时长: {info['total_duration']:.2f}s
            视频片段: {info['video_segments_count']}
            音频片段: {info['audio_segments_count']}
            图像片段: {info['image_segments_count']}
            输出分辨率: {info['output_resolution']}
            帧率: {info['fps']} fps
            """
            self.project_info_label.setText(info_text.strip())
        else:
            self.project_info_label.setText("未加载项目")
    
    def refresh_segments_tables(self):
        """刷新片段表格"""
        if not self.current_project:
            return
        
        # 刷新视频片段表格
        self.refresh_video_segments_table()
        
        # 刷新音频片段表格
        self.refresh_audio_segments_table()
        
        # 刷新图像片段表格
        self.refresh_image_segments_table()
    
    def refresh_video_segments_table(self):
        """刷新视频片段表格"""
        table = self.video_segments_table
        table.setRowCount(len(self.current_project.video_segments))
        
        for i, segment in enumerate(self.current_project.video_segments):
            table.setItem(i, 0, QTableWidgetItem(segment.id))
            table.setItem(i, 1, QTableWidgetItem(os.path.basename(segment.file_path)))
            table.setItem(i, 2, QTableWidgetItem(f"{segment.start_time:.2f}s"))
            table.setItem(i, 3, QTableWidgetItem(f"{segment.duration:.2f}s"))
            table.setItem(i, 4, QTableWidgetItem(f"{segment.volume:.2f}"))
    
    def refresh_audio_segments_table(self):
        """刷新音频片段表格"""
        table = self.audio_segments_table
        table.setRowCount(len(self.current_project.audio_segments))
        
        for i, segment in enumerate(self.current_project.audio_segments):
            table.setItem(i, 0, QTableWidgetItem(segment.id))
            table.setItem(i, 1, QTableWidgetItem(os.path.basename(segment.file_path)))
            table.setItem(i, 2, QTableWidgetItem(f"{segment.start_time:.2f}s"))
            table.setItem(i, 3, QTableWidgetItem(f"{segment.duration:.2f}s"))
            table.setItem(i, 4, QTableWidgetItem(f"{segment.volume:.2f}"))
            table.setItem(i, 5, QTableWidgetItem(segment.track_type))

    def refresh_image_segments_table(self):
        """刷新图像片段表格"""
        table = self.image_segments_table
        table.setRowCount(len(self.current_project.image_segments))

        for i, segment in enumerate(self.current_project.image_segments):
            table.setItem(i, 0, QTableWidgetItem(segment.id))
            table.setItem(i, 1, QTableWidgetItem(os.path.basename(segment.file_path)))
            table.setItem(i, 2, QTableWidgetItem(f"{segment.start_time:.2f}s"))
            table.setItem(i, 3, QTableWidgetItem(f"{segment.duration:.2f}s"))

    def add_video_segment(self):
        """添加视频片段"""
        if not self.current_project:
            show_error("请先创建或加载项目")
            return

        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择视频文件", "",
            "视频文件 (*.mp4 *.avi *.mov *.mkv *.webm);;所有文件 (*)"
        )

        if file_path:
            try:
                # 简单的时间分配：放在当前项目的末尾
                start_time = self.current_project.total_duration

                segment_id = self.processor.add_video_segment(
                    self.current_project, file_path, start_time
                )

                self.update_project_info()
                self.refresh_segments_tables()
                show_success(f"视频片段已添加: {segment_id}")

            except Exception as e:
                show_error(f"添加视频片段失败: {e}")
                logger.error(f"添加视频片段失败: {e}")

    def add_audio_segment(self):
        """添加音频片段"""
        if not self.current_project:
            show_error("请先创建或加载项目")
            return

        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择音频文件", "",
            "音频文件 (*.mp3 *.wav *.aac *.m4a *.ogg);;所有文件 (*)"
        )

        if file_path:
            try:
                start_time = self.current_project.total_duration

                segment_id = self.processor.add_audio_segment(
                    self.current_project, file_path, start_time
                )

                self.update_project_info()
                self.refresh_segments_tables()
                show_success(f"音频片段已添加: {segment_id}")

            except Exception as e:
                show_error(f"添加音频片段失败: {e}")
                logger.error(f"添加音频片段失败: {e}")

    def add_image_segment(self):
        """添加图像片段"""
        if not self.current_project:
            show_error("请先创建或加载项目")
            return

        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择图像文件", "",
            "图像文件 (*.jpg *.jpeg *.png *.bmp *.tiff);;所有文件 (*)"
        )

        if file_path:
            try:
                start_time = self.current_project.total_duration
                duration = 5.0  # 默认5秒

                segment_id = self.processor.add_image_segment(
                    self.current_project, file_path, start_time, duration
                )

                self.update_project_info()
                self.refresh_segments_tables()
                show_success(f"图像片段已添加: {segment_id}")

            except Exception as e:
                show_error(f"添加图像片段失败: {e}")
                logger.error(f"添加图像片段失败: {e}")

    def remove_segment(self):
        """删除选中的片段"""
        if not self.current_project:
            return

        current_tab = self.segments_tab_widget.currentIndex()

        if current_tab == 0:  # 视频片段
            table = self.video_segments_table
            segments = self.current_project.video_segments
        elif current_tab == 1:  # 音频片段
            table = self.audio_segments_table
            segments = self.current_project.audio_segments
        elif current_tab == 2:  # 图像片段
            table = self.image_segments_table
            segments = self.current_project.image_segments
        else:
            return

        current_row = table.currentRow()
        if current_row >= 0 and current_row < len(segments):
            segment = segments[current_row]

            reply = QMessageBox.question(
                self, "确认删除",
                f"确定要删除片段 '{segment.id}' 吗？",
                QMessageBox.Yes | QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                segments.pop(current_row)
                self.update_project_info()
                self.refresh_segments_tables()
                show_success(f"片段已删除: {segment.id}")

    def load_project(self):
        """加载项目"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "加载项目", str(self.processor.output_dir),
            "项目文件 (*.json);;所有文件 (*)"
        )

        if file_path:
            try:
                self.current_project = self.processor.load_project(file_path)

                # 更新界面配置
                self.width_spinbox.setValue(self.current_project.config.output_width)
                self.height_spinbox.setValue(self.current_project.config.output_height)
                self.fps_spinbox.setValue(self.current_project.config.fps)
                self.quality_combo.setCurrentText(self.current_project.config.quality_preset)
                self.enable_transitions_checkbox.setChecked(self.current_project.config.enable_transitions)

                self.update_project_info()
                self.refresh_segments_tables()
                show_success(f"项目已加载: {self.current_project.name}")

            except Exception as e:
                show_error(f"加载项目失败: {e}")
                logger.error(f"加载项目失败: {e}")

    def save_project(self):
        """保存项目"""
        if not self.current_project:
            show_error("没有项目可保存")
            return

        try:
            # 更新项目配置
            self.current_project.config.output_width = self.width_spinbox.value()
            self.current_project.config.output_height = self.height_spinbox.value()
            self.current_project.config.fps = self.fps_spinbox.value()
            self.current_project.config.quality_preset = self.quality_combo.currentText()
            self.current_project.config.enable_transitions = self.enable_transitions_checkbox.isChecked()

            file_path = self.processor.save_project(self.current_project)
            show_success(f"项目已保存: {file_path}")

        except Exception as e:
            show_error(f"保存项目失败: {e}")
            logger.error(f"保存项目失败: {e}")

    def start_synthesis(self):
        """开始视频合成"""
        if not self.current_project:
            show_error("请先创建或加载项目")
            return

        if not self.current_project.video_segments and not self.current_project.image_segments:
            show_error("项目中没有视频或图像片段")
            return

        try:
            # 更新项目配置
            self.current_project.config.output_width = self.width_spinbox.value()
            self.current_project.config.output_height = self.height_spinbox.value()
            self.current_project.config.fps = self.fps_spinbox.value()
            self.current_project.config.quality_preset = self.quality_combo.currentText()
            self.current_project.config.enable_transitions = self.enable_transitions_checkbox.isChecked()

            # 获取输出文件名
            output_filename = self.output_filename_edit.text().strip()
            if not output_filename:
                output_filename = None

            # 禁用合成按钮
            self.synthesis_btn.setEnabled(False)
            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(0)
            self.status_label.setText("准备合成...")

            # 创建工作线程
            self.synthesis_worker = VideoSynthesisWorker(
                self.processor, self.current_project, output_filename
            )

            # 连接信号
            self.synthesis_worker.progress_updated.connect(self.on_synthesis_progress)
            self.synthesis_worker.synthesis_completed.connect(self.on_synthesis_completed)
            self.synthesis_worker.synthesis_failed.connect(self.on_synthesis_failed)

            # 启动合成
            self.synthesis_worker.start()

            logger.info(f"开始视频合成: {self.current_project.name}")

        except Exception as e:
            self.synthesis_btn.setEnabled(True)
            self.progress_bar.setVisible(False)
            show_error(f"启动合成失败: {e}")
            logger.error(f"启动合成失败: {e}")

    def on_synthesis_progress(self, progress: float, message: str):
        """合成进度更新"""
        self.progress_bar.setValue(int(progress * 100))
        self.status_label.setText(message)

    def on_synthesis_completed(self, output_path: str):
        """合成完成"""
        self.synthesis_btn.setEnabled(True)
        self.progress_bar.setVisible(False)
        self.status_label.setText("合成完成")

        show_success(f"视频合成完成!\n输出文件: {output_path}")
        logger.info(f"视频合成完成: {output_path}")

        # 询问是否打开输出目录
        reply = QMessageBox.question(
            self, "合成完成",
            f"视频合成完成!\n\n输出文件: {output_path}\n\n是否打开输出目录？",
            QMessageBox.Yes | QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            import subprocess
            import platform

            output_dir = os.path.dirname(output_path)
            if platform.system() == "Windows":
                subprocess.run(["explorer", output_dir])
            elif platform.system() == "Darwin":  # macOS
                subprocess.run(["open", output_dir])
            else:  # Linux
                subprocess.run(["xdg-open", output_dir])

    def on_synthesis_failed(self, error_message: str):
        """合成失败"""
        self.synthesis_btn.setEnabled(True)
        self.progress_bar.setVisible(False)
        self.status_label.setText("合成失败")

        show_error(f"视频合成失败: {error_message}")
        logger.error(f"视频合成失败: {error_message}")
