# -*- coding: utf-8 -*-
"""
翻译服务状态检查工具
用于检查各种翻译服务的可用性和配置状态
"""

import logging
import requests
from typing import Dict, List, Optional
from .baidu_translator import is_configured as is_baidu_configured, translate_text as baidu_translate
from .enhanced_translator import get_translator

logger = logging.getLogger(__name__)

class TranslationStatusChecker:
    """翻译服务状态检查器"""
    
    def __init__(self):
        self.test_text = "测试"
        self.test_from_lang = "zh"
        self.test_to_lang = "en"
    
    def check_all_services(self) -> Dict[str, Dict]:
        """检查所有翻译服务的状态"""
        results = {
            'baidu': self.check_baidu_translate(),
            'google': self.check_google_translate(),
            'llm': self.check_llm_translate(),
            'summary': {}
        }
        
        # 生成总结
        available_services = []
        for service, status in results.items():
            if service != 'summary' and status.get('available', False):
                available_services.append(service)
        
        results['summary'] = {
            'available_services': available_services,
            'total_available': len(available_services),
            'has_any_available': len(available_services) > 0,
            'recommended_action': self._get_recommended_action(results)
        }
        
        return results
    
    def check_baidu_translate(self) -> Dict:
        """检查百度翻译状态"""
        result = {
            'service_name': '百度翻译',
            'configured': False,
            'available': False,
            'error': None,
            'test_result': None
        }
        
        try:
            # 检查配置
            if not is_baidu_configured():
                result['error'] = "未配置APP ID或密钥"
                return result
            
            result['configured'] = True
            
            # 测试翻译
            test_result = baidu_translate(self.test_text, self.test_from_lang, self.test_to_lang)
            if test_result and test_result.strip():
                result['available'] = True
                result['test_result'] = test_result
                logger.info("百度翻译服务可用")
            else:
                result['error'] = "翻译测试失败或返回空结果"
                
        except Exception as e:
            result['error'] = f"测试异常: {str(e)}"
            logger.error(f"百度翻译测试失败: {e}")
        
        return result
    
    def check_google_translate(self) -> Dict:
        """检查Google翻译状态"""
        result = {
            'service_name': 'Google翻译',
            'configured': True,  # Google翻译不需要配置
            'available': False,
            'error': None,
            'test_result': None
        }
        
        try:
            # 测试Google翻译
            url = "https://translate.googleapis.com/translate_a/single"
            params = {
                'client': 'gtx',
                'sl': self.test_from_lang,
                'tl': self.test_to_lang,
                'dt': 't',
                'q': self.test_text
            }
            
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
            
            response = requests.get(url, params=params, headers=headers, timeout=10)
            
            if response.status_code == 429:
                result['error'] = "频率限制 (429) - 请稍后重试"
            elif response.status_code == 200:
                response_data = response.json()
                if response_data and len(response_data) > 0 and response_data[0]:
                    translated_text = ""
                    for item in response_data[0]:
                        if item and len(item) > 0:
                            translated_text += item[0]
                    
                    if translated_text.strip():
                        result['available'] = True
                        result['test_result'] = translated_text.strip()
                        logger.info("Google翻译服务可用")
                    else:
                        result['error'] = "翻译结果为空"
                else:
                    result['error'] = "响应格式异常"
            else:
                result['error'] = f"HTTP错误: {response.status_code}"
                
        except requests.exceptions.Timeout:
            result['error'] = "请求超时"
        except requests.exceptions.RequestException as e:
            result['error'] = f"网络请求异常: {str(e)}"
        except Exception as e:
            result['error'] = f"测试异常: {str(e)}"
            logger.error(f"Google翻译测试失败: {e}")
        
        return result
    
    def check_llm_translate(self) -> Dict:
        """检查LLM翻译状态"""
        result = {
            'service_name': 'LLM翻译',
            'configured': False,
            'available': False,
            'error': None,
            'test_result': None
        }
        
        try:
            translator = get_translator()
            if not translator.llm_api:
                result['error'] = "LLM API未配置"
                return result
            
            result['configured'] = True
            
            # 测试LLM翻译
            test_result = translator._try_llm_translate(self.test_text, self.test_from_lang, self.test_to_lang)
            if test_result and test_result.strip():
                result['available'] = True
                result['test_result'] = test_result
                logger.info("LLM翻译服务可用")
            else:
                result['error'] = "LLM翻译测试失败"
                
        except Exception as e:
            result['error'] = f"测试异常: {str(e)}"
            logger.error(f"LLM翻译测试失败: {e}")
        
        return result
    
    def _get_recommended_action(self, results: Dict) -> str:
        """根据检查结果提供建议"""
        baidu_status = results.get('baidu', {})
        google_status = results.get('google', {})
        llm_status = results.get('llm', {})
        
        if baidu_status.get('available'):
            return "百度翻译可用，建议优先使用"
        elif baidu_status.get('configured') and not baidu_status.get('available'):
            if "54004" in str(baidu_status.get('error', '')):
                return "百度翻译账户余额不足，请充值或使用其他翻译服务"
            else:
                return "百度翻译配置有问题，请检查配置或使用其他翻译服务"
        elif google_status.get('available'):
            return "Google翻译可用，建议使用"
        elif google_status.get('error') and "429" in str(google_status.get('error')):
            return "Google翻译频率限制，请稍后重试或使用LLM翻译"
        elif llm_status.get('available'):
            return "仅LLM翻译可用，建议配置百度翻译API以获得更好的翻译质量"
        else:
            return "所有翻译服务都不可用，请检查网络连接和API配置"
    
    def print_status_report(self) -> None:
        """打印翻译服务状态报告"""
        results = self.check_all_services()
        
        print("\n" + "="*60)
        print("翻译服务状态报告")
        print("="*60)
        
        for service_key, service_data in results.items():
            if service_key == 'summary':
                continue
                
            service_name = service_data.get('service_name', service_key)
            configured = service_data.get('configured', False)
            available = service_data.get('available', False)
            error = service_data.get('error')
            test_result = service_data.get('test_result')
            
            print(f"\n【{service_name}】")
            print(f"  配置状态: {'✓ 已配置' if configured else '✗ 未配置'}")
            print(f"  可用状态: {'✓ 可用' if available else '✗ 不可用'}")
            
            if error:
                print(f"  错误信息: {error}")
            
            if test_result:
                print(f"  测试结果: '{self.test_text}' -> '{test_result}'")
        
        # 打印总结
        summary = results['summary']
        print(f"\n【总结】")
        print(f"  可用服务: {', '.join(summary['available_services']) if summary['available_services'] else '无'}")
        print(f"  可用数量: {summary['total_available']}/3")
        print(f"  建议操作: {summary['recommended_action']}")
        print("="*60)

def check_translation_status():
    """快速检查翻译状态的便捷函数"""
    checker = TranslationStatusChecker()
    return checker.check_all_services()

def print_translation_status():
    """打印翻译状态报告的便捷函数"""
    checker = TranslationStatusChecker()
    checker.print_status_report()

if __name__ == "__main__":
    print_translation_status()
