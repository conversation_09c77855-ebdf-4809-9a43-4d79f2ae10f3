# 分镜重复镜头与空镜头修复方案

## 问题描述

通过分析 `output/天下无双/storyboard` 目录下的分镜脚本，发现以下问题：

### 1. 重复镜头问题
- **场景1-镜头9**：与镜头1完全重复相同的原文内容
- 重复内容：`夜色如墨，寒风裹挟着雪花扑打在青石板路上，街巷间零星的灯火在风中摇曳，仿佛随时会熄灭。`

### 2. 空镜头问题
- **场景5-镜头5**：所有字段都是 `[无]`，没有有效内容

### 3. 内容覆盖问题
- 总体覆盖率102.4%（超过100%是因为重复内容）
- 存在原文内容的不完整覆盖

## 问题根源分析

### 1. LLM生成过程中的问题
- **句子分配不当**：同一句子被分配给多个镜头
- **镜头数量计算错误**：生成了超出实际需要的镜头数量
- **内容验证不足**：缺乏有效的重复检测机制

### 2. 验证机制不完善
- 原有的 `_validate_content_coverage` 方法未检测重复内容
- 缺乏空镜头的专门检测逻辑
- 完整性判断标准不够严格

## 解决方案

### 1. 改进内容覆盖验证机制

**修改文件**: `src/gui/five_stage_storyboard_tab.py`

**核心改进**:
```python
def _validate_content_coverage(self, storyboard_response, original_text, original_sentences):
    # 🔧 修复：过滤空镜头和无效内容
    valid_shot_texts = []
    seen_texts = set()  # 用于检测重复内容
    
    for text in shot_texts:
        text = text.strip()
        # 过滤空镜头、无效内容和重复内容
        if (text and 
            not text.startswith('[') and 
            not text.startswith('（') and
            text != '[无]' and
            text != '无' and
            text not in seen_texts):
            valid_shot_texts.append(text)
            seen_texts.add(text)

    # 🔧 修复：检测重复镜头
    duplicate_count = len(shot_texts) - len(valid_shot_texts)
    
    # 🔧 修复：更严格的完整性判断
    is_complete = (coverage_ratio >= 0.85 and 
                  len(missing_sentences) <= 1 and 
                  duplicate_count == 0)
```

### 2. 增强重试机制

**改进重试条件**:
```python
# 🔧 修复：重试机制处理重复镜头和空镜头
need_retry = False
retry_reason = []

if coverage_check['coverage_ratio'] < 0.7:
    need_retry = True
    retry_reason.append(f"覆盖率过低({coverage_check['coverage_ratio']:.1%})")

if coverage_check.get('duplicate_count', 0) > 0:
    need_retry = True
    retry_reason.append(f"存在{coverage_check['duplicate_count']}个重复镜头")
```

### 3. 改进重试提示词

**新增防重复指令**:
```
**🎯 重试要求**：
6. **🚫 严禁重复镜头：每个句子只能在一个镜头中出现**
7. **🚫 严禁空镜头：不能有"[无]"或空白的镜头原文**

**⚠️ 特别注意**：
- 检查每个镜头原文是否唯一，不能重复
- 所有镜头都必须有有效的原文内容
```

## 修复效果验证

### 测试结果
```
=== 测试内容覆盖验证功能 ===
验证结果:
  - 覆盖率: 77.5%
  - 遗漏句子数: 1
  - 重复镜头数: 2
  - 是否完整: False
  - 消息: 覆盖率: 77.5%, 遗漏句子: 1个, 重复镜头: 2个

✅ 成功检测到重复镜头
✅ 成功检测到遗漏内容
✅ 正确判断为不完整（因为有重复镜头和遗漏内容）
```

### 现有分镜脚本分析
```
总体分析结果:
  - 总覆盖率: 102.4%
  - 重复镜头数: 1
  - 空镜头数: 1
  - 重复镜头位置: 场景1-镜头9
  - 空镜头位置: 场景5-镜头5
```

## 使用建议

### 1. 重新生成分镜脚本
- 使用修复后的五阶段分镜生成功能
- 系统会自动检测并重试有问题的场景
- 确保所有镜头都有唯一的原文内容

### 2. 验证生成质量
- 检查是否还有重复镜头
- 确认没有空镜头
- 验证原文内容完整覆盖

### 3. 手动清理（如需要）
- 删除现有的重复分镜文件
- 重新运行分镜生成流程

## 技术细节

### 重复检测算法
1. 提取所有镜头原文
2. 使用集合(set)检测重复内容
3. 统计重复镜头数量
4. 在完整性判断中考虑重复因素

### 空镜头检测
1. 检查镜头原文是否为 `[无]`、空字符串或以 `[` 开头
2. 过滤无效内容
3. 确保所有镜头都有有效原文

### 完整性判断标准
- 覆盖率 ≥ 85%
- 遗漏句子 ≤ 1个
- 重复镜头 = 0个

## 总结

通过本次修复：
- ✅ 实现了重复镜头的自动检测
- ✅ 实现了空镜头的自动检测  
- ✅ 改进了内容覆盖验证机制
- ✅ 增强了重试机制的智能性
- ✅ 提高了分镜脚本的生成质量

现在系统能够自动检测并修复分镜生成过程中的重复镜头和空镜头问题，确保生成的分镜脚本质量更高，原文内容覆盖更完整。
