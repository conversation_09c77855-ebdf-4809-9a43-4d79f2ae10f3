#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试与实际请求完全相同的参数组合
"""

import asyncio
import aiohttp
import json
import base64
import os

# 智谱AI API配置
API_KEY = "ed7ffe9976bb4484ab16647564c0cb27.rI1BXVjDDDURMtJY"
BASE_URL = "https://open.bigmodel.cn/api/paas/v4"

def image_to_base64(image_path):
    """将图像转换为base64格式"""
    try:
        with open(image_path, 'rb') as f:
            image_data = f.read()
        
        # 获取文件扩展名
        ext = os.path.splitext(image_path)[1].lower()
        if ext == '.png':
            mime_type = 'image/png'
        elif ext in ['.jpg', '.jpeg']:
            mime_type = 'image/jpeg'
        else:
            mime_type = 'image/png'  # 默认
        
        base64_str = base64.b64encode(image_data).decode('utf-8')
        return f"data:{mime_type};base64,{base64_str}"
    except Exception as e:
        print(f"图像转换失败: {e}")
        return None

async def test_exact_request():
    """测试与实际请求完全相同的参数"""
    print("🎬 测试与实际请求完全相同的参数...")
    
    # 查找测试图像
    test_image_paths = [
        "D:\\AI_Video_Generator\\output\\视频生成测试\\images\\pollinations\\pollinations_1750834422274_0.png",
        "test_image.png",
        "sample.jpg"
    ]
    
    test_image = None
    for path in test_image_paths:
        if os.path.exists(path):
            test_image = path
            break
    
    if not test_image:
        print("❌ 未找到测试图像")
        return False
    
    print(f"📷 使用测试图像: {test_image}")
    
    # 转换图像为base64
    image_base64 = image_to_base64(test_image)
    if not image_base64:
        return False
    
    headers = {
        'Authorization': f'Bearer {API_KEY}',
        'Content-Type': 'application/json'
    }
    
    # 完全模拟实际请求的参数
    test_data = {
        "model": "cogvideox-flash",
        "prompt": "林肯叹了口气，将信收进抽屉，他的动作缓慢而沉重，反映出他对家的渴望和对战争的无奈。，吉卜力风，柔和色彩，奇幻，梦幻，丰富背景\n\n技术细节补充：中景; 平视; 静止; 自然光; 三分法",
        "image_url": image_base64,
        "duration": 5.736,
        "fps": 24,
        "size": "960x1280",
        "motion_intensity": 0.5
    }
    
    print(f"📋 请求参数:")
    log_data = test_data.copy()
    if "image_url" in log_data and log_data["image_url"].startswith("data:"):
        log_data["image_url"] = f"data:image/...;base64,<{len(log_data['image_url'])} chars>"
    print(json.dumps(log_data, indent=2, ensure_ascii=False))
    
    async with aiohttp.ClientSession() as session:
        try:
            url = f"{BASE_URL}/videos/generations"
            
            async with session.post(url, headers=headers, json=test_data) as response:
                print(f"\n状态码: {response.status}")
                response_text = await response.text()
                
                if response.status == 200:
                    result = json.loads(response_text)
                    print(f"✅ 请求成功")
                    print(f"任务ID: {result.get('id')}")
                    print(f"状态: {result.get('task_status')}")
                else:
                    print(f"❌ 请求失败: {response_text}")
                    
                    # 分析错误
                    if "1214" in response_text:
                        print("💡 错误1214: 不支持当前fps值")
                        
                        # 尝试不同的FPS值
                        print("\n🔧 尝试其他FPS值...")
                        for fps in [8, 16, 30]:
                            test_data_alt = test_data.copy()
                            test_data_alt["fps"] = fps
                            
                            async with session.post(url, headers=headers, json=test_data_alt) as alt_response:
                                if alt_response.status == 200:
                                    print(f"   ✅ FPS {fps} 成功")
                                    return True
                                else:
                                    alt_text = await alt_response.text()
                                    print(f"   ❌ FPS {fps} 失败: {alt_text[:50]}...")
                        
                        # 尝试移除FPS参数
                        print("\n🔧 尝试移除FPS参数...")
                        test_data_no_fps = test_data.copy()
                        del test_data_no_fps["fps"]
                        
                        async with session.post(url, headers=headers, json=test_data_no_fps) as no_fps_response:
                            if no_fps_response.status == 200:
                                print("   ✅ 移除FPS参数成功")
                                return True
                            else:
                                no_fps_text = await no_fps_response.text()
                                print(f"   ❌ 移除FPS参数失败: {no_fps_text[:50]}...")
                    
                    elif "1210" in response_text:
                        print("💡 错误1210: API调用参数有误")
                        
                        # 尝试简化参数
                        print("\n🔧 尝试简化参数...")
                        simple_data = {
                            "model": "cogvideox-flash",
                            "prompt": test_data["prompt"],
                            "image_url": test_data["image_url"]
                        }
                        
                        async with session.post(url, headers=headers, json=simple_data) as simple_response:
                            if simple_response.status == 200:
                                print("   ✅ 简化参数成功")
                                return True
                            else:
                                simple_text = await simple_response.text()
                                print(f"   ❌ 简化参数失败: {simple_text[:50]}...")
                        
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            import traceback
            traceback.print_exc()
    
    return False

async def main():
    """主测试函数"""
    print("=" * 80)
    print("🧪 CogVideoX-Flash 完整请求测试")
    print("=" * 80)
    
    success = await test_exact_request()
    
    print("\n" + "=" * 80)
    if success:
        print("✅ 找到了可用的参数组合")
    else:
        print("❌ 所有参数组合都失败了")
    print("=" * 80)

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n👋 测试已取消")
    except Exception as e:
        print(f"\n❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
