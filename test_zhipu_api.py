#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智谱AI API密钥测试脚本
"""

import requests
import json
import time

def test_zhipu_api_key(api_key):
    """测试智谱AI API密钥是否有效"""
    
    print(f"🔑 测试API密钥: {api_key[:10]}...")
    print("=" * 50)
    
    # 智谱AI API配置
    base_url = "https://open.bigmodel.cn/api/paas/v4"
    headers = {
        'Authorization': f'Bearer {api_key}',
        'Content-Type': 'application/json'
    }
    
    # 测试1: 聊天接口
    print("📝 测试1: 聊天接口 (GLM-4-Flash)")
    chat_url = f"{base_url}/chat/completions"
    chat_data = {
        "model": "glm-4-flash",
        "messages": [
            {
                "role": "user",
                "content": "你好，请回复'API测试成功'"
            }
        ],
        "max_tokens": 50,
        "temperature": 0.1
    }
    
    try:
        response = requests.post(chat_url, json=chat_data, headers=headers, timeout=30)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            if 'choices' in result and len(result['choices']) > 0:
                content = result['choices'][0]['message']['content']
                print(f"✅ 聊天接口测试成功!")
                print(f"回复内容: {content}")
            else:
                print(f"⚠️ 响应格式异常: {result}")
        else:
            print(f"❌ 聊天接口测试失败")
            print(f"错误信息: {response.text}")
            
    except Exception as e:
        print(f"❌ 聊天接口请求异常: {e}")
    
    print("-" * 30)
    
    # 测试2: CogVideoX视频生成接口
    print("🎬 测试2: CogVideoX视频生成接口")
    video_url = f"{base_url}/videos/generations"
    
    # 创建一个简单的测试图片base64 (1x1像素的透明PNG)
    test_image_base64 = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="
    
    video_data = {
        "model": "cogvideox-flash",
        "prompt": "测试视频生成",
        "image_url": f"data:image/png;base64,{test_image_base64}",
        "duration": 5.0,
        "fps": 30,
        "size": "1024x1024"
    }
    
    try:
        response = requests.post(video_url, json=video_data, headers=headers, timeout=30)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            if 'id' in result:
                task_id = result['id']
                print(f"✅ 视频生成接口测试成功!")
                print(f"任务ID: {task_id}")
                
                # 测试任务状态查询
                print("🔍 测试任务状态查询...")
                status_url = f"{base_url}/async-result/{task_id}"
                status_response = requests.get(status_url, headers=headers, timeout=30)
                print(f"状态查询响应码: {status_response.status_code}")
                
                if status_response.status_code == 200:
                    status_result = status_response.json()
                    task_status = status_result.get('task_status', 'UNKNOWN')
                    print(f"✅ 任务状态查询成功! 状态: {task_status}")
                else:
                    print(f"⚠️ 任务状态查询失败: {status_response.text}")
            else:
                print(f"⚠️ 视频生成响应格式异常: {result}")
        else:
            print(f"❌ 视频生成接口测试失败")
            print(f"错误信息: {response.text}")
            
    except Exception as e:
        print(f"❌ 视频生成接口请求异常: {e}")
    
    print("-" * 30)
    
    # 测试3: 账户信息查询
    print("💰 测试3: 账户信息查询")
    try:
        # 尝试查询模型列表
        models_url = f"{base_url}/models"
        models_response = requests.get(models_url, headers=headers, timeout=30)
        print(f"模型列表查询状态码: {models_response.status_code}")
        
        if models_response.status_code == 200:
            models_result = models_response.json()
            if 'data' in models_result:
                model_count = len(models_result['data'])
                print(f"✅ 账户信息查询成功! 可用模型数量: {model_count}")
                
                # 显示前几个模型
                for i, model in enumerate(models_result['data'][:3]):
                    model_id = model.get('id', 'unknown')
                    print(f"  - 模型{i+1}: {model_id}")
            else:
                print(f"⚠️ 模型列表响应格式异常: {models_result}")
        else:
            print(f"❌ 账户信息查询失败")
            print(f"错误信息: {models_response.text}")
            
    except Exception as e:
        print(f"❌ 账户信息查询异常: {e}")
    
    print("=" * 50)
    print("🏁 测试完成!")

if __name__ == "__main__":
    # 要测试的API密钥
    api_key = "ed7ffe9976bb4484ab16647564c0cb27.rI1BXVjDDDURMtJY"
    
    print("🧪 智谱AI API密钥测试工具")
    print(f"⏰ 测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    test_zhipu_api_key(api_key)
