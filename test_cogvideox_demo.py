#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CogVideoX-Flash 演示测试脚本
快速验证智谱AI视频生成功能
"""

import asyncio
import os
import sys
import time
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent))

def print_banner():
    """打印横幅"""
    print("=" * 60)
    print("🎬 CogVideoX-Flash 视频生成演示")
    print("=" * 60)
    print("正在测试智谱AI免费视频生成功能...")
    print()

async def test_connection():
    """测试连接"""
    print("🔍 步骤1: 测试API连接...")
    
    try:
        from src.models.video_engines.video_generation_service import VideoGenerationService
        from config.video_generation_config import get_config
        
        # 获取配置
        config = get_config('development')
        
        # 检查API密钥
        api_key = config['engines']['cogvideox_flash'].get('api_key', '')
        if not api_key:
            print("❌ 未找到API密钥配置")
            return False
        
        print(f"✅ 找到API密钥: {api_key[:8]}...")
        
        # 创建服务
        service = VideoGenerationService(config)
        
        # 测试连接
        print("   正在测试连接...")
        result = await service.test_engine('cogvideox_flash')
        
        if result:
            print("✅ API连接测试成功!")
            
            # 获取引擎信息
            info = service.get_engine_info('cogvideox_flash')
            if info:
                print(f"📊 引擎信息:")
                print(f"   名称: {info['name']}")
                print(f"   免费: {'是' if info['is_free'] else '否'}")
                print(f"   最大时长: {info['max_duration']}秒")
                print(f"   支持分辨率: {len(info['supported_resolutions'])}种")
        else:
            print("❌ API连接测试失败")
            print("   可能的原因: 网络问题或API密钥无效")
        
        await service.shutdown()
        return result
        
    except Exception as e:
        print(f"❌ 连接测试异常: {e}")
        return False

async def test_text_to_video():
    """测试文生视频"""
    print("\n🎬 步骤2: 测试文生视频...")
    
    try:
        from src.models.video_engines.video_generation_service import generate_video_simple
        
        # 测试提示词
        prompt = "一朵美丽的花在微风中轻轻摇摆，阳光洒在花瓣上"
        print(f"📝 提示词: {prompt}")
        print("⏳ 正在生成视频，请稍候...")
        
        start_time = time.time()
        
        # 生成视频
        result = await generate_video_simple(
            prompt=prompt,
            duration=3.0,  # 3秒视频
            width=1024,
            height=1024,
            fps=24,
            output_dir="output/videos",
            api_key="ed7ffe9976bb4484ab16647564c0cb27.rI1BXVjDDDURMtJY"
        )
        
        generation_time = time.time() - start_time
        
        if result.success:
            print(f"✅ 文生视频成功!")
            print(f"   视频路径: {result.video_path}")
            print(f"   视频时长: {result.duration:.1f}秒")
            print(f"   生成时间: {generation_time:.1f}秒")
            print(f"   分辨率: {result.resolution}")
            if result.file_size > 0:
                print(f"   文件大小: {result.file_size / 1024 / 1024:.2f}MB")
            
            # 检查文件是否存在
            if os.path.exists(result.video_path):
                print(f"✅ 视频文件已保存到: {result.video_path}")
            else:
                print(f"⚠️ 视频文件路径不存在: {result.video_path}")
            
            return True
        else:
            print(f"❌ 文生视频失败: {result.error_message}")
            return False
            
    except Exception as e:
        print(f"❌ 文生视频异常: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_image_to_video():
    """测试图生视频"""
    print("\n🖼️ 步骤3: 测试图生视频...")
    
    try:
        # 检查是否有测试图像
        test_images = [
            "test_image.jpg",
            "test_image.png", 
            "sample.jpg",
            "sample.png"
        ]
        
        test_image_path = None
        for img in test_images:
            if os.path.exists(img):
                test_image_path = img
                break
        
        if not test_image_path:
            print("⚠️ 未找到测试图像，创建一个简单的测试图像...")
            
            try:
                from PIL import Image
                import numpy as np
                
                # 创建一个简单的彩色图像
                img_array = np.random.randint(0, 255, (512, 512, 3), dtype=np.uint8)
                # 添加一些简单的图案
                img_array[200:300, 200:300] = [255, 0, 0]  # 红色方块
                img_array[100:150, 100:400] = [0, 255, 0]  # 绿色条带
                
                img = Image.fromarray(img_array)
                test_image_path = "temp_test_image.png"
                img.save(test_image_path)
                print(f"✅ 创建测试图像: {test_image_path}")
                
            except ImportError:
                print("❌ 需要安装 Pillow 和 numpy 来创建测试图像")
                print("   请运行: pip install Pillow numpy")
                return False
        
        print(f"📸 使用测试图像: {test_image_path}")
        
        from src.models.video_engines.video_generation_service import generate_video_simple
        
        prompt = "图像中的内容开始动起来，充满生机和活力"
        print(f"📝 提示词: {prompt}")
        print("⏳ 正在生成视频，请稍候...")
        
        start_time = time.time()
        
        # 生成视频
        result = await generate_video_simple(
            prompt=prompt,
            image_path=test_image_path,
            duration=3.0,
            width=1024,
            height=1024,
            fps=24,
            output_dir="output/videos",
            api_key="ed7ffe9976bb4484ab16647564c0cb27.rI1BXVjDDDURMtJY"
        )
        
        generation_time = time.time() - start_time
        
        if result.success:
            print(f"✅ 图生视频成功!")
            print(f"   视频路径: {result.video_path}")
            print(f"   视频时长: {result.duration:.1f}秒")
            print(f"   生成时间: {generation_time:.1f}秒")
            print(f"   分辨率: {result.resolution}")
            if result.file_size > 0:
                print(f"   文件大小: {result.file_size / 1024 / 1024:.2f}MB")
            
            # 检查文件是否存在
            if os.path.exists(result.video_path):
                print(f"✅ 视频文件已保存到: {result.video_path}")
            else:
                print(f"⚠️ 视频文件路径不存在: {result.video_path}")
            
            # 清理临时文件
            if test_image_path == "temp_test_image.png" and os.path.exists(test_image_path):
                os.remove(test_image_path)
                print("🗑️ 已清理临时测试图像")
            
            return True
        else:
            print(f"❌ 图生视频失败: {result.error_message}")
            return False
            
    except Exception as e:
        print(f"❌ 图生视频异常: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_processor_integration():
    """测试与视频处理器的集成"""
    print("\n🔗 步骤4: 测试处理器集成...")
    
    try:
        from src.processors.video_processor import VideoProcessor
        from src.core.service_manager import ServiceManager
        
        # 创建服务管理器和视频处理器
        service_manager = ServiceManager()
        processor = VideoProcessor(service_manager)
        
        # 检查视频生成引擎是否可用
        engines = processor.get_available_video_engines()
        print(f"📊 可用的视频引擎: {engines}")
        
        if 'cogvideox_flash' in engines:
            # 测试引擎
            result = await processor.test_video_engine('cogvideox_flash')
            print(f"🔧 CogVideoX-Flash引擎测试: {'✅ 可用' if result else '❌ 不可用'}")
            
            # 获取统计信息
            stats = processor.get_video_generation_statistics()
            if stats:
                print(f"📈 引擎统计:")
                print(f"   活跃任务: {stats.get('active_tasks', 0)}")
                print(f"   路由策略: {stats.get('routing_strategy', 'unknown')}")
            
            return result
        else:
            print("❌ CogVideoX-Flash引擎不在可用列表中")
            return False
        
    except Exception as e:
        print(f"❌ 处理器集成测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主测试函数"""
    print_banner()
    
    # 确保输出目录存在
    os.makedirs("output/videos", exist_ok=True)
    
    tests = [
        ("API连接测试", test_connection),
        ("文生视频测试", test_text_to_video),
        ("图生视频测试", test_image_to_video),
        ("处理器集成测试", test_processor_integration),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            print(f"\n{'='*20} {test_name} {'='*20}")
            result = await test_func()
            results.append((test_name, result))
            
            if result:
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
                
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
            results.append((test_name, False))
    
    # 输出测试结果
    print("\n" + "="*60)
    print("📋 测试结果汇总:")
    print("="*60)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:20} : {status}")
    
    success_count = sum(1 for _, result in results if result)
    total_count = len(results)
    
    print(f"\n📊 总计: {success_count}/{total_count} 测试通过")
    
    if success_count == total_count:
        print("🎉 所有测试通过！CogVideoX-Flash集成成功！")
        print("\n💡 您现在可以:")
        print("   - 使用文本生成视频")
        print("   - 从图像生成动态视频")
        print("   - 在您的项目中集成视频生成功能")
        print("   - 查看生成的视频文件在 output/videos/ 目录中")
    elif success_count > 0:
        print("⚠️ 部分测试通过，基本功能可用")
        print("   请检查失败的测试项目")
    else:
        print("❌ 所有测试失败，请检查配置和网络连接")
    
    print(f"\n📁 生成的视频文件保存在: {os.path.abspath('output/videos')}")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n👋 测试已取消")
    except Exception as e:
        print(f"\n❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
