"""
批量修复导入路径问题
将相对导入修改为绝对导入
"""

import os
import re
from pathlib import Path

def fix_imports_in_file(file_path):
    """修复单个文件的导入问题"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # 修复导入模式
        import_patterns = [
            (r'from utils\.', 'from src.utils.'),
            (r'from core\.', 'from src.core.'),
            (r'from gui\.', 'from src.gui.'),
            (r'from services\.', 'from src.services.'),
            (r'from audio_processing\.', 'from src.audio_processing.'),
            (r'from processors\.', 'from src.processors.'),
            (r'from models\.', 'from src.models.'),
        ]
        
        for pattern, replacement in import_patterns:
            content = re.sub(pattern, replacement, content)
        
        # 如果内容有变化，写回文件
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✅ 修复: {file_path}")
            return True
        else:
            print(f"⏭️ 跳过: {file_path} (无需修复)")
            return False
            
    except Exception as e:
        print(f"❌ 错误: {file_path} - {e}")
        return False

def main():
    """主函数"""
    print("🔧 开始批量修复导入路径问题...")
    
    # 查找所有Python文件
    src_dir = Path("src")
    if not src_dir.exists():
        print("❌ src目录不存在")
        return
    
    python_files = list(src_dir.rglob("*.py"))
    print(f"📁 找到 {len(python_files)} 个Python文件")
    
    fixed_count = 0
    for file_path in python_files:
        if fix_imports_in_file(file_path):
            fixed_count += 1
    
    print(f"\n🎉 修复完成！共修复 {fixed_count} 个文件")

if __name__ == "__main__":
    main()
