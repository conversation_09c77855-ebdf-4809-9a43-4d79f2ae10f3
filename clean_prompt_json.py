#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清理prompt.json文件中的重复和无用信息
"""

import os
import json
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def clean_prompt_json():
    """清理prompt.json文件"""
    
    prompt_file = "output/纸上谈兵/texts/prompt.json"
    
    if not os.path.exists(prompt_file):
        logger.error(f"prompt.json文件不存在: {prompt_file}")
        return
    
    try:
        # 读取现有数据
        with open(prompt_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        logger.info("开始清理prompt.json文件...")
        
        # 备份原文件
        backup_file = prompt_file + ".backup"
        with open(backup_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        logger.info(f"已创建备份文件: {backup_file}")
        
        # 清理场景数据
        if 'scenes' in data:
            cleaned_scenes = {}
            scene_counter = 1
            
            for scene_key, shots in data['scenes'].items():
                # 🔧 修复：提取场景名称，去除冗长的字典字符串
                if scene_key.startswith("{'scene_name':"):
                    # 从字典字符串中提取场景名称
                    try:
                        # 尝试解析字典字符串
                        scene_dict = eval(scene_key)
                        scene_name = scene_dict.get('scene_name', f'场景{scene_counter}')
                    except:
                        # 如果解析失败，使用默认名称
                        scene_name = f'场景{scene_counter}'
                else:
                    scene_name = scene_key
                
                # 清理镜头数据
                cleaned_shots = []
                for shot in shots:
                    cleaned_shot = {
                        "shot_number": shot.get("shot_number", f"### 镜头{len(cleaned_shots) + 1}"),
                        "original_description": shot.get("original_description", ""),
                        "enhanced_prompt": shot.get("enhanced_prompt", ""),
                        "content": shot.get("content", "")
                    }
                    
                    # 🔧 修复：统一content和enhanced_prompt字段
                    if not cleaned_shot["content"] and cleaned_shot["enhanced_prompt"]:
                        cleaned_shot["content"] = cleaned_shot["enhanced_prompt"]
                    elif not cleaned_shot["enhanced_prompt"] and cleaned_shot["content"]:
                        cleaned_shot["enhanced_prompt"] = cleaned_shot["content"]
                    
                    cleaned_shots.append(cleaned_shot)
                
                cleaned_scenes[scene_name] = cleaned_shots
                scene_counter += 1
            
            # 更新数据
            data['scenes'] = cleaned_scenes
            
            logger.info(f"清理完成，共处理 {len(cleaned_scenes)} 个场景")
            
            # 保存清理后的数据
            with open(prompt_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"已保存清理后的文件: {prompt_file}")
            
            # 显示清理结果
            for scene_name, shots in cleaned_scenes.items():
                logger.info(f"场景: {scene_name} - {len(shots)} 个镜头")
        
    except Exception as e:
        logger.error(f"清理失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    clean_prompt_json()
