#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI服务降级机制
实现服务熔断、降级策略和故障恢复机制
"""

import time
import asyncio
from typing import Dict, List, Optional, Callable, Any, Union
from dataclasses import dataclass, field
from enum import Enum
from collections import defaultdict, deque
import threading

from src.utils.logger import logger


class ServiceState(Enum):
    """服务状态"""
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    CIRCUIT_OPEN = "circuit_open"
    CIRCUIT_HALF_OPEN = "circuit_half_open"


@dataclass
class ServiceMetrics:
    """服务指标"""
    total_requests: int = 0
    failed_requests: int = 0
    success_requests: int = 0
    avg_response_time: float = 0.0
    last_failure_time: float = 0.0
    consecutive_failures: int = 0
    
    def get_failure_rate(self) -> float:
        """获取失败率"""
        if self.total_requests == 0:
            return 0.0
        return self.failed_requests / self.total_requests
    
    def get_success_rate(self) -> float:
        """获取成功率"""
        return 1.0 - self.get_failure_rate()


@dataclass
class CircuitBreakerConfig:
    """熔断器配置"""
    failure_threshold: int = 5  # 连续失败次数阈值
    failure_rate_threshold: float = 0.5  # 失败率阈值
    timeout: float = 60.0  # 熔断超时时间（秒）
    half_open_max_calls: int = 3  # 半开状态最大调用次数
    min_requests: int = 10  # 最小请求数（用于计算失败率）


class CircuitBreaker:
    """熔断器"""
    
    def __init__(self, service_name: str, config: CircuitBreakerConfig):
        self.service_name = service_name
        self.config = config
        self.state = ServiceState.HEALTHY
        self.metrics = ServiceMetrics()
        self.last_failure_time = 0.0
        self.half_open_calls = 0
        self._lock = threading.Lock()
    
    def can_execute(self) -> bool:
        """检查是否可以执行请求"""
        with self._lock:
            current_time = time.time()
            
            if self.state == ServiceState.HEALTHY:
                return True
            elif self.state == ServiceState.CIRCUIT_OPEN:
                # 检查是否可以转为半开状态
                if current_time - self.last_failure_time >= self.config.timeout:
                    self.state = ServiceState.CIRCUIT_HALF_OPEN
                    self.half_open_calls = 0
                    logger.info(f"熔断器 {self.service_name} 转为半开状态")
                    return True
                return False
            elif self.state == ServiceState.CIRCUIT_HALF_OPEN:
                return self.half_open_calls < self.config.half_open_max_calls
            
            return False
    
    def record_success(self, response_time: float = 0.0):
        """记录成功请求"""
        with self._lock:
            self.metrics.total_requests += 1
            self.metrics.success_requests += 1
            self.metrics.consecutive_failures = 0
            
            # 更新平均响应时间
            if response_time > 0:
                total_time = self.metrics.avg_response_time * (self.metrics.success_requests - 1)
                self.metrics.avg_response_time = (total_time + response_time) / self.metrics.success_requests
            
            # 状态转换
            if self.state == ServiceState.CIRCUIT_HALF_OPEN:
                self.half_open_calls += 1
                if self.half_open_calls >= self.config.half_open_max_calls:
                    self.state = ServiceState.HEALTHY
                    logger.info(f"熔断器 {self.service_name} 恢复为健康状态")
            elif self.state == ServiceState.DEGRADED:
                # 检查是否可以恢复
                if self.metrics.get_success_rate() > 0.8:  # 成功率超过80%恢复
                    self.state = ServiceState.HEALTHY
                    logger.info(f"服务 {self.service_name} 从降级状态恢复")
    
    def record_failure(self, error: Exception):
        """记录失败请求"""
        with self._lock:
            self.metrics.total_requests += 1
            self.metrics.failed_requests += 1
            self.metrics.consecutive_failures += 1
            self.metrics.last_failure_time = time.time()
            self.last_failure_time = self.metrics.last_failure_time
            
            # 状态转换
            if self.state == ServiceState.HEALTHY:
                if self._should_open_circuit():
                    self.state = ServiceState.CIRCUIT_OPEN
                    logger.warning(f"熔断器 {self.service_name} 开启，原因: {error}")
                elif self._should_degrade():
                    self.state = ServiceState.DEGRADED
                    logger.warning(f"服务 {self.service_name} 降级，原因: {error}")
            elif self.state == ServiceState.CIRCUIT_HALF_OPEN:
                self.state = ServiceState.CIRCUIT_OPEN
                logger.warning(f"熔断器 {self.service_name} 重新开启")
    
    def _should_open_circuit(self) -> bool:
        """判断是否应该开启熔断"""
        # 连续失败次数超过阈值
        if self.metrics.consecutive_failures >= self.config.failure_threshold:
            return True
        
        # 失败率超过阈值（且有足够的请求样本）
        if (self.metrics.total_requests >= self.config.min_requests and 
            self.metrics.get_failure_rate() >= self.config.failure_rate_threshold):
            return True
        
        return False
    
    def _should_degrade(self) -> bool:
        """判断是否应该降级"""
        # 失败率在30%-50%之间时降级
        failure_rate = self.metrics.get_failure_rate()
        return (self.metrics.total_requests >= self.config.min_requests and 
                0.3 <= failure_rate < self.config.failure_rate_threshold)
    
    def get_state(self) -> ServiceState:
        """获取当前状态"""
        return self.state
    
    def get_metrics(self) -> ServiceMetrics:
        """获取服务指标"""
        return self.metrics
    
    def reset(self):
        """重置熔断器"""
        with self._lock:
            self.state = ServiceState.HEALTHY
            self.metrics = ServiceMetrics()
            self.half_open_calls = 0
            logger.info(f"熔断器 {self.service_name} 已重置")


@dataclass
class FallbackConfig:
    """降级配置"""
    primary_providers: List[str] = field(default_factory=list)
    fallback_providers: List[str] = field(default_factory=list)
    local_fallback: bool = True  # 是否启用本地降级
    cache_fallback: bool = True  # 是否启用缓存降级
    timeout_threshold: float = 30.0  # 超时阈值


class ServiceFallbackManager:
    """服务降级管理器"""
    
    def __init__(self):
        self.circuit_breakers: Dict[str, CircuitBreaker] = {}
        self.fallback_configs: Dict[str, FallbackConfig] = {}
        self.provider_priorities: Dict[str, List[str]] = {}
        self._lock = threading.Lock()
        
        # 默认配置
        self._setup_default_configs()
        
        logger.info("服务降级管理器初始化完成")
    
    def _setup_default_configs(self):
        """设置默认配置"""
        # LLM服务降级配置
        self.fallback_configs['llm'] = FallbackConfig(
            primary_providers=['zhipu', 'deepseek'],
            fallback_providers=['tongyi', 'google'],
            local_fallback=False,
            cache_fallback=True
        )
        
        # 图像生成服务降级配置
        self.fallback_configs['image'] = FallbackConfig(
            primary_providers=['pollinations'],
            fallback_providers=['comfyui', 'stability'],
            local_fallback=True,
            cache_fallback=True
        )
        
        # 语音合成服务降级配置
        self.fallback_configs['voice'] = FallbackConfig(
            primary_providers=['edge-tts'],
            fallback_providers=['cosyvoice', 'ttsmaker'],
            local_fallback=True,
            cache_fallback=False
        )
    
    def register_service(self, service_name: str, providers: List[str], 
                        config: Optional[CircuitBreakerConfig] = None):
        """注册服务"""
        with self._lock:
            # 创建熔断器
            cb_config = config or CircuitBreakerConfig()
            for provider in providers:
                service_key = f"{service_name}:{provider}"
                self.circuit_breakers[service_key] = CircuitBreaker(service_key, cb_config)
            
            # 设置提供商优先级
            self.provider_priorities[service_name] = providers.copy()
            
            logger.info(f"已注册服务 {service_name}，提供商: {providers}")
    
    def get_available_provider(self, service_name: str, 
                             preferred_provider: Optional[str] = None) -> Optional[str]:
        """获取可用的提供商"""
        providers = self.provider_priorities.get(service_name, [])
        if not providers:
            return preferred_provider
        
        # 如果指定了首选提供商，优先检查
        if preferred_provider and preferred_provider in providers:
            service_key = f"{service_name}:{preferred_provider}"
            if service_key in self.circuit_breakers:
                if self.circuit_breakers[service_key].can_execute():
                    return preferred_provider
        
        # 按优先级检查其他提供商
        for provider in providers:
            if preferred_provider and provider == preferred_provider:
                continue  # 已经检查过了
            
            service_key = f"{service_name}:{provider}"
            if service_key in self.circuit_breakers:
                if self.circuit_breakers[service_key].can_execute():
                    return provider
        
        # 如果所有提供商都不可用，返回第一个（可能会触发熔断）
        return providers[0] if providers else preferred_provider
    
    def record_request_result(self, service_name: str, provider: str, 
                            success: bool, response_time: float = 0.0, 
                            error: Optional[Exception] = None):
        """记录请求结果"""
        service_key = f"{service_name}:{provider}"
        if service_key not in self.circuit_breakers:
            return
        
        circuit_breaker = self.circuit_breakers[service_key]
        if success:
            circuit_breaker.record_success(response_time)
        else:
            circuit_breaker.record_failure(error or Exception("Unknown error"))
    
    def get_service_status(self, service_name: str) -> Dict[str, Any]:
        """获取服务状态"""
        providers = self.provider_priorities.get(service_name, [])
        status = {
            'service_name': service_name,
            'providers': {}
        }
        
        for provider in providers:
            service_key = f"{service_name}:{provider}"
            if service_key in self.circuit_breakers:
                cb = self.circuit_breakers[service_key]
                status['providers'][provider] = {
                    'state': cb.get_state().value,
                    'metrics': {
                        'total_requests': cb.metrics.total_requests,
                        'success_rate': cb.metrics.get_success_rate(),
                        'failure_rate': cb.metrics.get_failure_rate(),
                        'consecutive_failures': cb.metrics.consecutive_failures,
                        'avg_response_time': cb.metrics.avg_response_time
                    }
                }
        
        return status
    
    def reset_service(self, service_name: str, provider: Optional[str] = None):
        """重置服务状态"""
        if provider:
            service_key = f"{service_name}:{provider}"
            if service_key in self.circuit_breakers:
                self.circuit_breakers[service_key].reset()
        else:
            providers = self.provider_priorities.get(service_name, [])
            for p in providers:
                service_key = f"{service_name}:{p}"
                if service_key in self.circuit_breakers:
                    self.circuit_breakers[service_key].reset()


# 全局实例
service_fallback_manager = ServiceFallbackManager()
