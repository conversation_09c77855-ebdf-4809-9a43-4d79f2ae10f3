#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据完整性系统测试
"""

import os
import sys
import time
import tempfile
import json
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.core.data_integrity_manager import (
    DataIntegrityManager, DataIntegrityLevel, BackupType
)
from src.core.data_integrity_integration import (
    initialize_data_integrity, create_manual_backup, restore_from_backup,
    check_data_integrity, get_data_integrity_status, list_backups
)
from src.utils.logger import logger

def test_checksum_calculation():
    """测试校验和计算"""
    print("🧪 测试校验和计算...")
    
    try:
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建测试文件
            test_file = Path(temp_dir) / "test.txt"
            test_content = "这是一个测试文件内容"
            
            with open(test_file, 'w', encoding='utf-8') as f:
                f.write(test_content)
            
            # 创建数据完整性管理器
            manager = DataIntegrityManager(temp_dir)
            
            # 计算校验和
            checksum1 = manager.calculate_file_checksum(str(test_file))
            checksum2 = manager.calculate_file_checksum(str(test_file))
            
            # 验证一致性
            if checksum1 == checksum2 and checksum1:
                print(f"✅ 校验和计算一致: {checksum1}")
            else:
                print("❌ 校验和计算不一致")
                return False
            
            # 修改文件内容
            with open(test_file, 'w', encoding='utf-8') as f:
                f.write(test_content + " - 修改后")
            
            checksum3 = manager.calculate_file_checksum(str(test_file))
            
            if checksum3 != checksum1:
                print("✅ 文件修改后校验和正确变化")
                return True
            else:
                print("❌ 文件修改后校验和未变化")
                return False
        
    except Exception as e:
        print(f"❌ 校验和计算测试失败: {e}")
        return False

def test_file_integrity_verification():
    """测试文件完整性验证"""
    print("\n🧪 测试文件完整性验证...")
    
    try:
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建测试文件
            test_file = Path(temp_dir) / "integrity_test.txt"
            test_content = "完整性测试文件"
            
            with open(test_file, 'w', encoding='utf-8') as f:
                f.write(test_content)
            
            # 创建管理器并更新校验和
            manager = DataIntegrityManager(temp_dir)
            manager.update_file_checksum(str(test_file))
            
            # 验证完整性（应该通过）
            is_valid = manager.verify_file_integrity(str(test_file))
            if is_valid:
                print("✅ 原始文件完整性验证通过")
            else:
                print("❌ 原始文件完整性验证失败")
                return False
            
            # 修改文件内容（模拟损坏）
            with open(test_file, 'w', encoding='utf-8') as f:
                f.write("损坏的内容")
            
            # 再次验证完整性（应该失败）
            is_valid_after_corruption = manager.verify_file_integrity(str(test_file))
            if not is_valid_after_corruption:
                print("✅ 损坏文件完整性验证正确失败")
                return True
            else:
                print("❌ 损坏文件完整性验证未检测到问题")
                return False
        
    except Exception as e:
        print(f"❌ 文件完整性验证测试失败: {e}")
        return False

def test_backup_and_restore():
    """测试备份和恢复功能"""
    print("\n🧪 测试备份和恢复功能...")
    
    try:
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建测试文件
            test_file = Path(temp_dir) / "backup_test.txt"
            original_content = "原始文件内容"
            
            with open(test_file, 'w', encoding='utf-8') as f:
                f.write(original_content)
            
            # 创建管理器
            manager = DataIntegrityManager(temp_dir)
            
            # 创建备份
            backup_id = manager.create_backup(str(test_file), BackupType.MANUAL, "测试备份")
            
            if backup_id:
                print(f"✅ 备份创建成功: {backup_id}")
            else:
                print("❌ 备份创建失败")
                return False
            
            # 修改原文件
            modified_content = "修改后的文件内容"
            with open(test_file, 'w', encoding='utf-8') as f:
                f.write(modified_content)
            
            # 验证文件已修改
            with open(test_file, 'r', encoding='utf-8') as f:
                current_content = f.read()
            
            if current_content == modified_content:
                print("✅ 文件修改确认")
            else:
                print("❌ 文件修改失败")
                return False
            
            # 恢复备份
            restore_success = manager.restore_backup(backup_id)
            
            if restore_success:
                print("✅ 备份恢复成功")
            else:
                print("❌ 备份恢复失败")
                return False
            
            # 验证恢复结果
            with open(test_file, 'r', encoding='utf-8') as f:
                restored_content = f.read()
            
            if restored_content == original_content:
                print("✅ 文件内容恢复正确")
                return True
            else:
                print(f"❌ 文件内容恢复错误: 期望 '{original_content}', 实际 '{restored_content}'")
                return False
        
    except Exception as e:
        print(f"❌ 备份和恢复测试失败: {e}")
        return False

def test_transaction_mechanism():
    """测试事务机制"""
    print("\n🧪 测试事务机制...")

    try:
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建测试文件
            test_file = Path(temp_dir) / "transaction_test.txt"
            original_content = "事务测试原始内容"

            with open(test_file, 'w', encoding='utf-8') as f:
                f.write(original_content)

            # 创建管理器
            manager = DataIntegrityManager(temp_dir)

            # 测试失败事务（应该回滚到原始内容）
            try:
                with manager.transaction("失败事务测试", [str(test_file)]):
                    # 验证事务开始前的内容
                    with open(test_file, 'r', encoding='utf-8') as f:
                        content_before_change = f.read()
                    print(f"  事务开始前内容: {content_before_change}")

                    # 修改文件
                    with open(test_file, 'w', encoding='utf-8') as f:
                        f.write("失败事务中的内容")

                    # 验证修改后的内容
                    with open(test_file, 'r', encoding='utf-8') as f:
                        content_after_change = f.read()
                    print(f"  事务中修改后内容: {content_after_change}")

                    # 故意抛出异常
                    raise Exception("模拟事务失败")

            except Exception as e:
                if "模拟事务失败" not in str(e):
                    print(f"❌ 意外异常: {e}")
                    return False

                # 预期的异常，检查是否回滚到原始状态
                with open(test_file, 'r', encoding='utf-8') as f:
                    content_after_rollback = f.read()

                print(f"  回滚后内容: {content_after_rollback}")

                # 应该回滚到原始内容
                if content_after_rollback == original_content:
                    print("✅ 失败事务正确回滚到原始状态")
                    return True
                else:
                    print(f"❌ 失败事务回滚错误: 期望 '{original_content}', 实际 '{content_after_rollback}'")
                    return False

    except Exception as e:
        print(f"❌ 事务机制测试失败: {e}")
        return False

def test_integrity_check():
    """测试完整性检查"""
    print("\n🧪 测试完整性检查...")
    
    try:
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建多个测试文件
            files = []
            for i in range(3):
                test_file = Path(temp_dir) / f"check_test_{i}.txt"
                with open(test_file, 'w', encoding='utf-8') as f:
                    f.write(f"测试文件 {i} 的内容")
                files.append(str(test_file))
            
            # 创建管理器
            manager = DataIntegrityManager(temp_dir)

            # 配置要检查的文件
            manager.config['critical_files'] = files

            # 更新所有文件的校验和
            for file_path in files:
                manager.update_file_checksum(file_path)
            
            # 执行完整性检查（应该通过）
            result = manager.perform_integrity_check(DataIntegrityLevel.BASIC)
            
            if result.is_valid:
                print("✅ 初始完整性检查通过")
            else:
                print("❌ 初始完整性检查失败")
                return False
            
            # 损坏一个文件
            with open(files[1], 'w', encoding='utf-8') as f:
                f.write("损坏的内容")
            
            # 再次执行完整性检查（应该失败）
            result_after_corruption = manager.perform_integrity_check(DataIntegrityLevel.BASIC)
            
            if not result_after_corruption.is_valid:
                print("✅ 损坏后完整性检查正确失败")
                print(f"   检测到损坏文件: {result_after_corruption.corrupted_files}")
                return True
            else:
                print("❌ 损坏后完整性检查未检测到问题")
                return False
        
    except Exception as e:
        print(f"❌ 完整性检查测试失败: {e}")
        return False

def test_integration_functions():
    """测试集成函数"""
    print("\n🧪 测试集成函数...")
    
    try:
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建测试文件
            test_file = Path(temp_dir) / "integration_test.txt"
            with open(test_file, 'w', encoding='utf-8') as f:
                f.write("集成测试文件")
            
            # 测试手动备份
            backup_id = create_manual_backup(str(test_file), "集成测试备份")
            
            if backup_id:
                print(f"✅ 集成函数创建备份成功: {backup_id}")
            else:
                print("❌ 集成函数创建备份失败")
                return False
            
            # 测试备份列表
            backups = list_backups()
            
            if backup_id in backups:
                print("✅ 备份列表功能正常")
            else:
                print("❌ 备份列表功能异常")
                return False
            
            # 测试状态获取
            status = get_data_integrity_status()
            
            if 'total_monitored_files' in status:
                print("✅ 状态获取功能正常")
                print(f"   监控文件数: {status['total_monitored_files']}")
                print(f"   备份数量: {status['total_backups']}")
                return True
            else:
                print("❌ 状态获取功能异常")
                return False
        
    except Exception as e:
        print(f"❌ 集成函数测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("💾 数据完整性系统测试")
    print("=" * 50)
    
    test_results = []
    
    # 运行所有测试
    test_results.append(test_checksum_calculation())
    test_results.append(test_file_integrity_verification())
    test_results.append(test_backup_and_restore())
    test_results.append(test_transaction_mechanism())
    test_results.append(test_integrity_check())
    test_results.append(test_integration_functions())
    
    # 汇总结果
    print("\n" + "=" * 50)
    print("📊 测试结果汇总:")
    
    passed = sum(test_results)
    total = len(test_results)
    
    print(f"✅ 通过: {passed}/{total}")
    print(f"❌ 失败: {total - passed}/{total}")
    
    if passed == total:
        print("🎉 所有测试通过！数据完整性系统工作正常。")
        return True
    else:
        print("⚠️ 部分测试失败，请检查相关功能。")
        return False

if __name__ == "__main__":
    # 运行测试
    result = main()
    sys.exit(0 if result else 1)
