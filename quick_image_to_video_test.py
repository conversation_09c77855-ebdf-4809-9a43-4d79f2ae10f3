#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速图生视频测试
"""

import asyncio
import os
import sys
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent))

async def test_image_to_video():
    """测试图生视频"""
    print("🖼️ 测试图生视频（修复版）...")
    
    try:
        # 创建测试图像
        from PIL import Image
        import numpy as np
        
        # 创建一个简单的彩色图像
        img_array = np.random.randint(0, 255, (512, 512, 3), dtype=np.uint8)
        # 添加一些简单的图案
        img_array[200:300, 200:300] = [255, 0, 0]  # 红色方块
        img_array[100:150, 100:400] = [0, 255, 0]  # 绿色条带
        
        img = Image.fromarray(img_array)
        test_image_path = "temp_test_image.png"
        img.save(test_image_path)
        print(f"✅ 创建测试图像: {test_image_path}")
        
        from src.models.video_engines.video_generation_service import generate_video_simple
        
        prompt = "图像中的内容开始动起来，充满生机和活力"
        print(f"📝 提示词: {prompt}")
        print("⏳ 正在生成视频，请稍候...")
        
        # 图生视频不指定duration
        result = await generate_video_simple(
            prompt=prompt,
            image_path=test_image_path,
            # duration=3.0,  # 不指定duration，让API使用默认值
            fps=24,
            width=1024,
            height=1024,
            motion_intensity=0.5,
            output_dir="output/videos",
            api_key="ed7ffe9976bb4484ab16647564c0cb27.rI1BXVjDDDURMtJY"
        )
        
        if result.success:
            print(f"✅ 图生视频成功!")
            print(f"   视频路径: {result.video_path}")
            print(f"   视频时长: {result.duration:.1f}秒")
            print(f"   分辨率: {result.resolution}")
            if result.file_size > 0:
                print(f"   文件大小: {result.file_size / 1024 / 1024:.2f}MB")
            
            # 检查文件是否存在
            if os.path.exists(result.video_path):
                print(f"✅ 视频文件已保存到: {result.video_path}")
            else:
                print(f"⚠️ 视频文件路径不存在: {result.video_path}")
            
            # 清理临时文件
            if os.path.exists(test_image_path):
                os.remove(test_image_path)
                print("🗑️ 已清理临时测试图像")
            
            return True
        else:
            print(f"❌ 图生视频失败: {result.error_message}")
            return False
            
    except Exception as e:
        print(f"❌ 图生视频异常: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主函数"""
    print("🎬 快速图生视频测试")
    print("=" * 40)
    
    result = await test_image_to_video()
    
    if result:
        print("\n🎉 图生视频测试成功！")
    else:
        print("\n❌ 图生视频测试失败")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n👋 测试已取消")
    except Exception as e:
        print(f"\n❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
