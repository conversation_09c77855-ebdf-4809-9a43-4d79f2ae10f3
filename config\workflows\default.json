{"id": "default", "name": "默认工作流", "description": "基础的文生图工作流", "version": "1.0", "workflow": {"1": {"inputs": {"text": "beautiful landscape, high quality, detailed", "clip": ["2", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "2": {"inputs": {"ckpt_name": "sd_xl_base_1.0.safetensors"}, "class_type": "CheckpointLoaderSimple", "_meta": {"title": "Load Checkpoint"}}, "3": {"inputs": {"text": "nsfw, low quality, blurry", "clip": ["2", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Negative)"}}, "4": {"inputs": {"width": 1024, "height": 1024, "batch_size": 1}, "class_type": "EmptyLatentImage", "_meta": {"title": "Empty Latent Image"}}, "5": {"inputs": {"seed": 42, "steps": 20, "cfg": 7.0, "sampler_name": "euler", "scheduler": "normal", "denoise": 1.0, "model": ["2", 0], "positive": ["1", 0], "negative": ["3", 0], "latent_image": ["4", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "6": {"inputs": {"samples": ["5", 0], "vae": ["2", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "7": {"inputs": {"filename_prefix": "ComfyUI", "images": ["6", 0]}, "class_type": "SaveImage", "_meta": {"title": "Save Image"}}}, "parameters": {"prompt_node": "1", "negative_prompt_node": "3", "width_node": "4", "height_node": "4", "steps_node": "5", "cfg_node": "5", "seed_node": "5", "sampler_node": "5", "scheduler_node": "5", "model_node": "2"}}