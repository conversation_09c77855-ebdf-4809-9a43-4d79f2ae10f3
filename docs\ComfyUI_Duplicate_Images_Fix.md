# ComfyUI重复保存图片问题修复

## 问题描述

在使用ComfyUI本地引擎生成图片时，发现会在 `output\[项目名]\images\comfyui` 目录下保存两张相同的图片。

## 问题分析

### 原因分析

通过代码分析发现，重复保存的原因是图片在两个不同的地方被保存了：

1. **ComfyUI引擎自动保存**
   - 位置：`src/models/engines/comfyui_engine.py`
   - 方法：`_download_image_to_local()`
   - 保存路径：`output/[项目名]/images/comfyui/comfyui_[timestamp]_[filename]`

2. **分镜界面重复保存**
   - 位置：`src/gui/storyboard_image_generation_tab.py`
   - 方法：`_ensure_image_in_project_folder()`
   - 保存路径：`output/[项目名]/images/comfyui_local/[sequence]_[timestamp].png`

### 重复保存流程

```
ComfyUI生成图片 
    ↓
ComfyUI引擎保存图片到项目目录
    ↓
分镜界面收到图片路径
    ↓
分镜界面检查图片是否在项目目录中
    ↓
检查逻辑不完善，误判为需要复制
    ↓
再次复制图片到项目目录
    ↓
结果：同一张图片被保存两次
```

## 解决方案

### 1. 改进路径检查逻辑

在 `src/gui/storyboard_image_generation_tab.py` 的 `_ensure_image_in_project_folder` 方法中：

```python
# 原来的检查逻辑
if project_dir in original_path.parents:
    return image_path

# 改进后的检查逻辑
if project_dir in original_path.parents:
    return image_path

# 特别检查ComfyUI引擎是否已经保存到正确位置
images_dir = project_dir / "images"
if images_dir in original_path.parents:
    return image_path

# 检查图片是否已经在对应的引擎目录中
expected_engine_dir = project_dir / "images" / engine_name
if expected_engine_dir in original_path.parents:
    return image_path
```

### 2. 统一引擎目录命名

将ComfyUI相关的目录统一命名为 `comfyui`，避免 `comfyui_local` 和 `comfyui` 的混淆：

```python
# 原来的命名
elif 'ComfyUI 本地' in current_engine:
    engine_name = 'comfyui_local'
elif 'ComfyUI 云端' in current_engine:
    engine_name = 'comfyui_cloud'

# 统一后的命名
elif 'ComfyUI 本地' in current_engine:
    engine_name = 'comfyui'  # 统一使用comfyui目录
elif 'ComfyUI 云端' in current_engine:
    engine_name = 'comfyui'  # 统一使用comfyui目录
```

## 修复效果

### 修复前
```
output/
└── 金毛狗救主/
    └── images/
        └── comfyui/
            ├── comfyui_1750314123_image.png  ← ComfyUI引擎保存
            └── 9-1_1750314124.png            ← 分镜界面重复保存
```

### 修复后
```
output/
└── 金毛狗救主/
    └── images/
        └── comfyui/
            └── comfyui_1750314123_image.png  ← 只保存一次
```

## 测试验证

创建了专门的测试脚本 `test_duplicate_images.py` 来验证修复效果：

### 测试场景

1. **场景1：图片已在ComfyUI目录中**
   - 测试图片路径检查逻辑
   - 验证不会重复复制

2. **场景2：图片在外部目录**
   - 测试正常的复制逻辑
   - 验证外部图片能正确复制到项目目录

### 测试结果

```
测试结果: 2/2 通过
✓ 所有测试通过！重复保存问题已修复
```

## 相关文件修改

### 主要修改文件

1. **src/gui/storyboard_image_generation_tab.py**
   - 改进 `_ensure_image_in_project_folder` 方法的路径检查逻辑
   - 统一ComfyUI引擎目录命名

### 测试文件

1. **test_duplicate_images.py**
   - 专门测试重复保存问题的脚本
   - 验证路径检查逻辑的正确性

## 使用建议

1. **重新测试ComfyUI生图功能**
   - 确保修复后的功能正常工作
   - 验证不再出现重复保存

2. **清理已有的重复图片**
   - 检查现有项目中是否有重复的图片文件
   - 手动删除重复的图片以节省存储空间

3. **监控后续使用**
   - 在后续使用中注意观察是否还有重复保存的情况
   - 如有问题及时反馈

## 技术细节

### 路径检查原理

使用Python的 `pathlib.Path` 对象的 `parents` 属性来检查路径关系：

```python
from pathlib import Path

project_dir = Path("/project")
image_path = Path("/project/images/comfyui/image.png")

# 检查project_dir是否是image_path的父目录
if project_dir in image_path.parents:
    print("图片已在项目目录中")
```

### 引擎目录映射

```python
engine_mapping = {
    'ComfyUI 本地': 'comfyui',
    'ComfyUI 云端': 'comfyui',
    'Pollinations AI': 'pollinations',
    'DALL-E': 'dalle',
    'Stability AI': 'stability',
    'Google Imagen': 'imagen'
}
```

## 总结

通过改进路径检查逻辑和统一目录命名，成功解决了ComfyUI生图时重复保存图片的问题。修复后：

- ✅ 不再重复保存相同图片
- ✅ 保持原有功能完整性
- ✅ 提高存储空间利用率
- ✅ 简化项目文件管理

这个修复确保了ComfyUI引擎生成的图片只会被保存一次，避免了不必要的存储浪费和文件管理混乱。
