#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
服务弹性集成模块
将弹性系统集成到现有的AI服务中
"""

import asyncio
import functools
from typing import Any, Callable, Optional, Dict

from src.core.service_resilience import (
    service_monitor, service_orchestrator, degradation_manager,
    ServiceConfig, RetryConfig, CircuitBreakerConfig, ServiceStatus
)
from src.utils.logger import logger
from src.core.service_base import ServiceResult

class ResilienceIntegration:
    """弹性系统集成器"""
    
    def __init__(self):
        self.is_integrated = False
        self._setup_default_configurations()
        self._setup_degradation_strategies()
        self._setup_fallback_responses()
    
    def integrate_services(self):
        """集成所有服务的弹性机制"""
        if self.is_integrated:
            return
        
        try:
            # 集成LLM服务
            self._integrate_llm_service()
            
            # 集成图像服务
            self._integrate_image_service()
            
            # 集成语音服务
            self._integrate_voice_service()
            
            # 集成视频服务
            self._integrate_video_service()
            
            self.is_integrated = True
            logger.info("服务弹性系统集成完成")
            
        except Exception as e:
            logger.error(f"服务弹性系统集成失败: {e}")
            raise
    
    def _setup_default_configurations(self):
        """设置默认配置"""
        # LLM服务配置
        llm_config = ServiceConfig(
            service_name="llm_service",
            providers=["deepseek", "tongyi", "zhipu", "google"],
            primary_provider="deepseek",
            fallback_providers=["tongyi", "zhipu"],
            circuit_breaker=CircuitBreakerConfig(failure_threshold=5, recovery_timeout=60.0),
            retry_config=RetryConfig(max_retries=3, base_delay=1.0),
            degradation_threshold=0.7
        )
        service_monitor.register_service(llm_config)
        
        # 图像服务配置
        image_config = ServiceConfig(
            service_name="image_service",
            providers=["pollinations", "comfyui_local", "openai_dalle"],
            primary_provider="pollinations",
            fallback_providers=["comfyui_local"],
            circuit_breaker=CircuitBreakerConfig(failure_threshold=3, recovery_timeout=120.0),
            retry_config=RetryConfig(max_retries=2, base_delay=2.0),
            degradation_threshold=0.6
        )
        service_monitor.register_service(image_config)
        
        # 语音服务配置
        voice_config = ServiceConfig(
            service_name="voice_service",
            providers=["edge_tts", "azure", "openai"],
            primary_provider="edge_tts",
            fallback_providers=["azure"],
            circuit_breaker=CircuitBreakerConfig(failure_threshold=4, recovery_timeout=45.0),
            retry_config=RetryConfig(max_retries=2, base_delay=1.5),
            degradation_threshold=0.8
        )
        service_monitor.register_service(voice_config)
        
        # 视频服务配置
        video_config = ServiceConfig(
            service_name="video_service",
            providers=["cogvideox_flash", "local_video"],
            primary_provider="cogvideox_flash",
            fallback_providers=["local_video"],
            circuit_breaker=CircuitBreakerConfig(failure_threshold=2, recovery_timeout=300.0),
            retry_config=RetryConfig(max_retries=1, base_delay=5.0),
            degradation_threshold=0.5
        )
        service_monitor.register_service(video_config)
    
    def _setup_degradation_strategies(self):
        """设置降级策略"""
        # LLM服务降级策略
        async def llm_degradation_strategy(*args, **kwargs):
            """LLM服务降级策略：使用缓存或简化响应"""
            logger.info("使用LLM服务降级策略")
            
            # 尝试从缓存获取响应
            prompt = kwargs.get('prompt', '')
            if len(prompt) > 100:
                # 简化长提示词
                simplified_prompt = prompt[:100] + "..."
                kwargs['prompt'] = simplified_prompt
                kwargs['max_tokens'] = min(kwargs.get('max_tokens', 1000), 500)
            
            # 返回简化的响应结构
            return ServiceResult(
                success=True,
                data={
                    'content': '由于服务负载较高，返回简化响应。请稍后重试获取完整结果。',
                    'usage': {'total_tokens': 50}
                },
                metadata={'degraded': True, 'strategy': 'simplified_response'}
            )
        
        degradation_manager.register_degradation_strategy("llm_service", llm_degradation_strategy)
        
        # 图像服务降级策略
        async def image_degradation_strategy(*args, **kwargs):
            """图像服务降级策略：使用占位图像"""
            logger.info("使用图像服务降级策略")
            
            # 返回占位图像路径
            placeholder_path = "assets/placeholder.png"
            
            return ServiceResult(
                success=True,
                data={
                    'image_paths': [placeholder_path]
                },
                metadata={'degraded': True, 'strategy': 'placeholder_image'}
            )
        
        degradation_manager.register_degradation_strategy("image_service", image_degradation_strategy)
        
        # 语音服务降级策略
        async def voice_degradation_strategy(*args, **kwargs):
            """语音服务降级策略：返回文本"""
            logger.info("使用语音服务降级策略")
            
            text = kwargs.get('text', '')
            
            return ServiceResult(
                success=True,
                data={
                    'audio_path': None,
                    'text': text
                },
                metadata={'degraded': True, 'strategy': 'text_only'}
            )
        
        degradation_manager.register_degradation_strategy("voice_service", voice_degradation_strategy)
    
    def _setup_fallback_responses(self):
        """设置备用响应"""
        # LLM服务备用响应
        llm_fallback = ServiceResult(
            success=False,
            error="LLM服务暂时不可用，请稍后重试或联系技术支持"
        )
        degradation_manager.register_fallback_response("llm_service", llm_fallback)

        # 图像服务备用响应
        image_fallback = ServiceResult(
            success=False,
            error="图像生成服务暂时不可用，请稍后重试或使用本地图像"
        )
        degradation_manager.register_fallback_response("image_service", image_fallback)

        # 语音服务备用响应
        voice_fallback = ServiceResult(
            success=False,
            error="语音服务暂时不可用，请使用文本模式或稍后重试"
        )
        degradation_manager.register_fallback_response("voice_service", voice_fallback)
    
    def _integrate_llm_service(self):
        """集成LLM服务"""
        try:
            from src.services.llm_service import LLMService
            
            # 保存原始方法
            original_call_api = LLMService.call_api
            
            # 创建弹性包装器
            async def resilient_call_api(self, prompt: str, max_tokens: int = 1000, 
                                       temperature: float = 0.7, provider: str = None, **kwargs):
                """带弹性机制的API调用"""
                return await service_orchestrator.call_service(
                    "llm_service",
                    lambda: original_call_api(self, prompt, max_tokens, temperature, provider, **kwargs)
                )
            
            # 替换方法
            LLMService.call_api = resilient_call_api
            
            logger.info("LLM服务弹性机制已集成")
            
        except ImportError as e:
            logger.warning(f"无法集成LLM服务弹性机制: {e}")
    
    def _integrate_image_service(self):
        """集成图像服务"""
        try:
            from src.services.image_service import ImageService
            
            # 保存原始方法
            original_generate_image = ImageService.generate_image
            
            # 创建弹性包装器
            async def resilient_generate_image(self, prompt: str, style: str = None, 
                                             negative_prompt: str = None, provider: str = None, **kwargs):
                """带弹性机制的图像生成"""
                return await service_orchestrator.call_service(
                    "image_service",
                    lambda: original_generate_image(self, prompt, style, negative_prompt, provider, **kwargs)
                )
            
            # 替换方法
            ImageService.generate_image = resilient_generate_image
            
            logger.info("图像服务弹性机制已集成")
            
        except ImportError as e:
            logger.warning(f"无法集成图像服务弹性机制: {e}")
    
    def _integrate_voice_service(self):
        """集成语音服务"""
        try:
            from src.services.voice_service import VoiceService
            
            # 保存原始方法
            original_text_to_speech = VoiceService.text_to_speech
            
            # 创建弹性包装器
            async def resilient_text_to_speech(self, text: str, voice: str = None, 
                                             provider: str = None, **kwargs):
                """带弹性机制的语音合成"""
                return await service_orchestrator.call_service(
                    "voice_service",
                    lambda: original_text_to_speech(self, text, voice, provider, **kwargs)
                )
            
            # 替换方法
            VoiceService.text_to_speech = resilient_text_to_speech
            
            logger.info("语音服务弹性机制已集成")
            
        except ImportError as e:
            logger.warning(f"无法集成语音服务弹性机制: {e}")
    
    def _integrate_video_service(self):
        """集成视频服务"""
        try:
            # 视频服务可能还没有实现，先预留接口
            logger.info("视频服务弹性机制预留")
            
        except Exception as e:
            logger.warning(f"无法集成视频服务弹性机制: {e}")

# 弹性装饰器
def with_resilience(service_name: str):
    """弹性装饰器"""
    def decorator(func):
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            return await service_orchestrator.call_service(service_name, func, *args, **kwargs)
        return wrapper
    return decorator

# 服务状态检查装饰器
def check_service_health(service_name: str):
    """服务健康检查装饰器"""
    def decorator(func):
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            status = service_monitor.get_service_status(service_name)
            
            if status == ServiceStatus.CIRCUIT_OPEN:
                raise Exception(f"服务 {service_name} 熔断器开启，请稍后重试")
            
            return await func(*args, **kwargs)
        return wrapper
    return decorator

# 全局集成实例
resilience_integration = ResilienceIntegration()

def initialize_service_resilience():
    """初始化服务弹性系统"""
    try:
        resilience_integration.integrate_services()
        logger.info("服务弹性系统初始化完成")
        return True
    except Exception as e:
        logger.error(f"服务弹性系统初始化失败: {e}")
        return False

def get_service_health_summary() -> Dict[str, Any]:
    """获取服务健康摘要"""
    summary = {}
    
    for service_name in ["llm_service", "image_service", "voice_service", "video_service"]:
        status = service_monitor.get_service_status(service_name)
        metrics = service_monitor.get_service_metrics(service_name)
        
        summary[service_name] = {
            'status': status.value,
            'success_rate': metrics.success_rate if metrics else 1.0,
            'total_requests': metrics.total_requests if metrics else 0,
            'consecutive_failures': metrics.consecutive_failures if metrics else 0,
            'recommended_provider': service_monitor.get_recommended_provider(service_name)
        }
    
    return summary
