# 视频生成配置文件

def get_config():
    """获取视频生成配置"""
    return {
        "default_engine": "cogvideox_flash",
        "engines": {
            "cogvideox_flash": {
                "enabled": True,
                "name": "CogVideoX-Flash",
                "description": "智谱AI免费视频生成服务",
                "api_key": "YOUR_ZHIPU_API_KEY",
                "base_url": "https://open.bigmodel.cn/api/paas/v4",
                "max_concurrent": 3,
                "supported_resolutions": ["720x480", "1024x1024", "1280x720", "1920x1080"],
                "supported_durations": [5.0, 5.5, 10.0],
                "default_resolution": "1024x1024",
                "default_duration": 5.0
            }
        }
    }

def get_enabled_engines():
    """获取启用的引擎列表"""
    config = get_config()
    enabled_engines = []
    
    for engine_id, engine_config in config["engines"].items():
        if engine_config.get("enabled", False):
            enabled_engines.append({
                "id": engine_id,
                "name": engine_config["name"],
                "description": engine_config["description"]
            })
    
    return enabled_engines
