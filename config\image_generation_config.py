# -*- coding: utf-8 -*-
"""
图像生成服务配置示例
"""

# 默认配置
DEFAULT_CONFIG = {
    # 输出目录
    'output_dir': 'output/images',
    
    # 路由策略: 'priority', 'load_balance', 'cost_optimize', 'quality_first'
    'routing_strategy': 'priority',
    
    # 引擎偏好: 'free', 'quality', 'speed', 'local'
    'engine_preferences': ['free', 'local', 'quality'],
    
    # 各引擎配置
    'engines': {
        # Pollinations AI (免费)
        'pollinations': {
            'enabled': True,
            'timeout': 60,
            'max_retries': 3,
            'models': ['flux', 'flux-realism', 'flux-anime', 'flux-3d']
        },

        # CogView-3-Flash (智谱AI免费)
        'cogview_3_flash': {
            'enabled': True,
            'api_key': 'ed7ffe9976bb4484ab16647564c0cb27.rI1BXVjDDDURMtJY',  # 使用现有的智谱AI API密钥
            'base_url': 'https://open.bigmodel.cn/api/paas/v4',
            'model': 'cogview-3-flash',
            'timeout': 300,  # 5分钟超时
            'max_retries': 3,
            'cost_per_image': 0.0  # 免费
        },
        
        # ComfyUI配置
        'comfyui': {
            'enabled': True,
            
            # 本地ComfyUI
            'local': {
                'enabled': True,
                'url': 'http://127.0.0.1:8188',
                'timeout': 300,
                'max_retries': 2,
                'workflow_dir': 'workflows'
            },
            
            # 云端ComfyUI
            'cloud': {
                'enabled': False,
                'url': 'https://your-comfyui-cloud.com',
                'api_key': '',  # 需要配置
                'timeout': 300,
                'max_retries': 2,
                'cost_per_image': 0.05
            }
        },
        
        # DALL-E (付费)
        'dalle': {
            'enabled': False,
            'api_key': '',  # 需要配置OpenAI API密钥
            'model': 'dall-e-3',
            'timeout': 120,
            'max_retries': 2,
            'cost_per_image': 0.040,  # DALL-E 3标准价格
            'quality': 'standard'  # 'standard' 或 'hd'
        },
        
        # Stability AI (付费)
        'stability': {
            'enabled': False,
            'api_key': '',  # 需要配置Stability AI API密钥
            'model': 'stable-diffusion-xl-1024-v1-0',
            'timeout': 120,
            'max_retries': 2,
            'cost_per_image': 0.020
        },
        
        # Google Imagen (使用Gemini API)
        'imagen': {
            'enabled': False,
            'api_key': '',  # 需要配置Google Gemini API密钥
            'project_id': '',  # 如果需要可以配置
            'timeout': 120,
            'max_retries': 2,
            'model': 'imagen-3.0-generate-001',  # Imagen模型
            'cost_per_image': 0.040  # 预估成本
        }
    }
}

# 开发环境配置（主要使用免费服务）
DEVELOPMENT_CONFIG = {
    **DEFAULT_CONFIG,
    'routing_strategy': 'priority',
    'engine_preferences': ['free', 'local'],
    'engines': {
        **DEFAULT_CONFIG['engines'],
        'dalle': {'enabled': False},
        'stability': {'enabled': False},
        'imagen': {'enabled': False}  # 需要配置API密钥后启用
    }
}

# 生产环境配置（包含付费服务作为备选）
PRODUCTION_CONFIG = {
    **DEFAULT_CONFIG,
    'routing_strategy': 'cost_optimize',
    'engine_preferences': ['free', 'quality', 'speed'],
    'engines': {
        **DEFAULT_CONFIG['engines'],
        'dalle': {
            **DEFAULT_CONFIG['engines']['dalle'],
            'enabled': True,
            'api_key': 'your-openai-api-key'  # 需要实际配置
        },
        'stability': {
            **DEFAULT_CONFIG['engines']['stability'],
            'enabled': True,
            'api_key': 'your-stability-api-key'  # 需要实际配置
        }
    }
}

# 高质量配置（优先使用付费高质量服务）
HIGH_QUALITY_CONFIG = {
    **DEFAULT_CONFIG,
    'routing_strategy': 'quality_first',
    'engine_preferences': ['quality', 'speed'],
    'engines': {
        **DEFAULT_CONFIG['engines'],
        'dalle': {
            **DEFAULT_CONFIG['engines']['dalle'],
            'enabled': True,
            'quality': 'hd',
            'cost_per_image': 0.080  # HD质量价格
        },
        'stability': {
            **DEFAULT_CONFIG['engines']['stability'],
            'enabled': True,
            'model': 'stable-diffusion-xl-1024-v1-0'
        }
    }
}

# 成本优化配置（优先使用免费服务，付费服务作为最后备选）
COST_OPTIMIZED_CONFIG = {
    **DEFAULT_CONFIG,
    'routing_strategy': 'cost_optimize',
    'engine_preferences': ['free', 'local'],
    'engines': {
        **DEFAULT_CONFIG['engines'],
        'dalle': {
            **DEFAULT_CONFIG['engines']['dalle'],
            'enabled': True,
            'quality': 'standard'  # 使用标准质量降低成本
        },
        'stability': {
            **DEFAULT_CONFIG['engines']['stability'],
            'enabled': True,
            'model': 'stable-diffusion-v1-6'  # 使用较便宜的模型
        }
    }
}


def get_config(environment: str = 'development'):
    """根据环境获取配置
    
    Args:
        environment: 环境名称 ('development', 'production', 'high_quality', 'cost_optimized')
    
    Returns:
        配置字典
    """
    configs = {
        'development': DEVELOPMENT_CONFIG,
        'production': PRODUCTION_CONFIG,
        'high_quality': HIGH_QUALITY_CONFIG,
        'cost_optimized': COST_OPTIMIZED_CONFIG,
        'default': DEFAULT_CONFIG
    }
    
    return configs.get(environment, DEFAULT_CONFIG)


def validate_config(config: dict) -> bool:
    """验证配置的有效性
    
    Args:
        config: 配置字典
    
    Returns:
        配置是否有效
    """
    required_keys = ['output_dir', 'routing_strategy', 'engines']
    
    for key in required_keys:
        if key not in config:
            print(f"配置缺少必需的键: {key}")
            return False
    
    # 检查至少有一个引擎启用
    enabled_engines = []
    for engine_name, engine_config in config['engines'].items():
        if engine_config.get('enabled', False):
            enabled_engines.append(engine_name)
    
    if not enabled_engines:
        print("至少需要启用一个图像生成引擎")
        return False
    
    print(f"配置验证通过，启用的引擎: {enabled_engines}")
    return True